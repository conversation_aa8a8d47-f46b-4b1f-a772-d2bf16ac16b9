import React, { useState } from 'react';
import { FieldValues, Path, UseFormReturn } from 'react-hook-form';
import { X, Plus } from 'lucide-react';
import { FormField, FormItem, FormControl, FormLabel, FormMessage } from '@/components/ui/form';
import { inputClassName, VARIANT_THEME_CLASSES } from './form.constants';
import { cn } from '@/lib/utils';
import { Input } from '../ui/input';

interface PrimaryMultiInputProps<TFieldValues extends FieldValues> {
  form: UseFormReturn<TFieldValues>;
  name: Path<TFieldValues>;
  label: string;
  placeholder: string;
  required?: boolean;
  variant?: 'primary' | 'secondary' | 'green' | 'purple';
  disabled?: boolean;
}

const PrimaryMultiInput = <TFieldValues extends FieldValues>({
  form,
  name,
  label,
  placeholder,
  required = false,
  variant = 'primary',
  disabled = false,
}: PrimaryMultiInputProps<TFieldValues>) => {
  const [inputValue, setInputValue] = useState('');
  const [isFocused, setIsFocused] = useState(false);
  const [showAllTags, setShowAllTags] = useState(false);

  const themeClasses = VARIANT_THEME_CLASSES[variant];

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if ((e.key === 'Enter' || e.key === ',') && !disabled) {
      e.preventDefault();
      const trimmedValue = inputValue.trim();
      if (trimmedValue) {
        const currentValues = (form.getValues(name) as string[]) || [];
        if (!currentValues.includes(trimmedValue)) {
          form.setValue(name, [...currentValues, trimmedValue] as any, { shouldValidate: true });
        }
      }
      setInputValue('');
    }
  };

  const removeValue = (index: number) => {
    if (disabled) return;
    const currentValues = (form.getValues(name) as string[]) || [];
    const newValues = [...currentValues];
    newValues.splice(index, 1);
    form.setValue(name, newValues as any, { shouldValidate: true });
  };

  const addValue = () => {
    if (disabled) return;
    const trimmedValue = inputValue.trim();
    if (trimmedValue) {
      const currentValues = (form.getValues(name) as string[]) || [];
      if (!currentValues.includes(trimmedValue)) {
        form.setValue(name, [...currentValues, trimmedValue] as any, { shouldValidate: true });
      }
    }
    setInputValue('');
  };

  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => {
        const values = (field.value as string[]) || [];
        const visibleTags = showAllTags ? values : values.slice(0, 2);
        const hiddenCount = values.length - visibleTags.length;
        return (
          <FormItem className='w-full'>
            <FormLabel className='primary-label'>
              <span>{label}</span>
              {required && <span className='text-primaryColor'>*</span>}
            </FormLabel>
            <FormControl>
              <div
                className={cn(
                  'min-h-[3rem] p-2.5 border rounded-md bg-white transition-all duration-200 shadow-sm flex flex-wrap gap-2 items-center',
                  isFocused ? `${themeClasses.borderStrong} ${themeClasses.ring} ring-2 ring-opacity-20` : 'border-gray-200',
                  disabled && 'bg-gray-50 cursor-not-allowed opacity-60'
                )}
              >
                {/* Tags inside input */}
                {values.length > 0 && (
                  <div className='flex flex-wrap gap-1.5'>
                    {visibleTags.map((value, index) => (
                      <div
                        key={index}
                        className={cn('flex items-center gap-1 px-2 py-0.5 rounded text-sm', `${themeClasses.bg} ${themeClasses.text}`)}
                        onClick={(e) => e.stopPropagation()}
                      >
                        <span className='select-none'>{value}</span>
                        {!disabled && (
                          <button
                            type='button'
                            className={cn(themeClasses.check, themeClasses.checkHover)}
                            onClick={(e) => {
                              e.stopPropagation();
                              removeValue(values.indexOf(value));
                            }}
                            aria-label={`Remove ${value}`}
                          >
                            <X size={14} />
                          </button>
                        )}
                      </div>
                    ))}
                    {hiddenCount > 0 && !showAllTags && (
                      <button
                        type='button'
                        className='flex items-center gap-1 px-2 py-0.5 rounded text-sm bg-gray-200 text-gray-700 hover:bg-gray-300 transition-all duration-200'
                        onClick={() => setShowAllTags(true)}
                      >
                        +{hiddenCount} more
                      </button>
                    )}
                    {showAllTags && values.length > 2 && (
                      <button
                        type='button'
                        className='flex items-center gap-1 px-2 py-0.5 rounded text-xs bg-gray-100 text-gray-500 hover:bg-gray-200 transition-all duration-200'
                        onClick={() => setShowAllTags(false)}
                      >
                        Show less
                      </button>
                    )}{' '}
                  </div>
                )}
                {/* Input field */}
                <Input
                  className='border-none shadow-none bg-transparent p-0 h-auto focus-visible:ring-0 focus-visible:ring-offset-0 flex-1 min-w-[120px]'
                  type='text'
                  value={inputValue}
                  onChange={(e) => setInputValue(e.target.value)}
                  onKeyDown={handleKeyDown}
                  onFocus={() => setIsFocused(true)}
                  onBlur={() => setIsFocused(false)}
                  placeholder={values.length === 0 ? placeholder : ''}
                  disabled={disabled}
                />
                {/* Helper Text */}
                {values.length === 0 && !isFocused && !inputValue && (
                  <div className='absolute bottom-1 left-3 text-xs text-gray-400'>Type and press Enter or comma to add items</div>
                )}
              </div>
            </FormControl>
            <FormMessage />
          </FormItem>
        );
      }}
    />
  );
};

export default PrimaryMultiInput;
