'use client';

import { Form } from '@/components/ui/form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import { toast } from 'react-toastify';
import { PrimaryInput, PrimarySwitchInput, SubmitButton } from '@/components/forms';
import { IAddUpdateFormProps } from '@/types/api';
import { useCreateExamSubject, useUpdateExamSubject } from '@/hooks/education/exam.hooks';
import { IExamSubjectDocument } from '@/server/services/education/exam.service';
import { createExamSubjectSchema } from '@/validation/schemas/education/exam.schema';
import { extractId } from '@/validation/utils/form.utils';

interface IExamSubjectFormProps extends IAddUpdateFormProps<IExamSubjectDocument> {
  onSuccess: () => void;
  examId?: string | null;
}

const ExamSubjectForm: React.FC<IExamSubjectFormProps> = ({ data, onSuccess, examId }) => {
  const { mutateAsync: createExamSubject, isPending: isCreating } = useCreateExamSubject();
  const { mutateAsync: updateExamSubject, isPending: isUpdating } = useUpdateExamSubject();

  const examSubjectId = data?._id;
  const isSubmitting = isCreating || isUpdating;

  const form = useForm<z.infer<typeof createExamSubjectSchema>>({
    resolver: zodResolver(createExamSubjectSchema),
    defaultValues: {
      name: data?.name || '',
      exam: extractId(data?.exam) || examId || '',
      isActive: data?.isActive !== undefined ? data.isActive : true,
    },
  });

  const onSubmit = async (values: z.infer<typeof createExamSubjectSchema>) => {
    try {
      const result = examSubjectId 
        ? await updateExamSubject({ id: examSubjectId, data: values }) 
        : await createExamSubject(values);
        
      if (!result.success) throw new Error(result.message || 'Operation failed');

      toast.success(result.message);
      onSuccess();
    } catch (error: any) {
      toast.error(error.message || 'An error occurred');
    }
  };

  return (
    <section className='relative'>
      <div className='bg-white rounded-lg shadow-sm p-6 max-w-2xl'>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className='grid grid-cols-1 gap-6 items-start justify-start'>
            {/* Name field */}
            <PrimaryInput form={form} name='name' label='Subject Name' placeholder='Enter subject name' required />

            {/* Is Active */}
            <PrimarySwitchInput
              form={form}
              name='isActive'
              label='Is this subject active?'
              description='Making it inactive will hide it from the website.'
            />

            <SubmitButton
              submittingLabel={examSubjectId ? 'Updating...' : 'Creating...'}
              label={examSubjectId ? 'Update Subject' : 'Create Subject'}
              isSubmitting={isSubmitting}
              disabled={isSubmitting}
              className='w-fit'
            />
          </form>
        </Form>
      </div>
    </section>
  );
};

export default ExamSubjectForm;
