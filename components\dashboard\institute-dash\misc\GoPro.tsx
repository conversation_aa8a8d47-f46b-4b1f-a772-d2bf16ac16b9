const GoPro = () => {
  return (
    <div className='bg-primaryColor-50 p-4 rounded-3xl relative'>
      <img src='/icons/fancy-arrow.webp' alt='Arrow' className='h-20 w-auto absolute -top-4 rotate-[-120deg] -left-8' />
      <div className='flex -space-x-1 absolute -top-6'>
        <img src='/icons/avatar-female.webp' alt='Avatar Female' className='size-10 rounded-full border-2 border-white' />
        <img src='/icons/avatar-male.webp' alt='Avatar Male' className='size-10 rounded-full border-2 border-white' />
      </div>
      <img src='/icons/rocket.webp' alt='Rocket' className='h-36 w-auto absolute -top-16 -right-8' />
      <div className='mt-4 text-center'>
        <h3 className='text-lg font-semibold text-gray-700'>GO PRO</h3>
        <p className='text-sm text-gray-500 my-4'>Everyone can view all discussion easily and create groups or sort default.</p>
        <button className='btn-default animate-bounce bg-my-gradient-1 !py-2 !rounded-full !text-sm'>Upgrade to PRO</button>
      </div>
    </div>
  );
};

export default GoPro;
