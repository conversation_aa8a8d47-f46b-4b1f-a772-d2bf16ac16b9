import apiClient from '@/server/apiClient';
import { IPagination } from '@/types/api';
import { CreateServiceCategoryInput, UpdateServiceCategoryInput } from '@/validation/schemas/service-category.schema';

// Types for Service Categories
export interface IServiceCategoryDocument extends CreateServiceCategoryInput {
  _id: string;
  createdAt: string;
  updatedAt: string;
}

export interface IServiceCategoriesResponse {
  serviceCategories: IServiceCategoryDocument[];
  pagination: IPagination;
}

export interface IServiceCategoryResponse {
  serviceCategory: IServiceCategoryDocument;
}

// Service Categories Service
const serviceCategoryService = {
  getAllServiceCategories: async (params?: Record<string, any>) => {
    const queryParams = new URLSearchParams(params);
    return await apiClient.get<IServiceCategoriesResponse>(`/education/service-categories?${queryParams.toString()}`);
  },

  getServiceCategoryById: async (id: string) => {
    return await apiClient.get<IServiceCategoryResponse>(`/education/service-categories/${id}`);
  },

  createServiceCategory: async (data: CreateServiceCategoryInput) => {
    return await apiClient.post<IServiceCategoryResponse>('/education/service-categories', data);
  },

  updateServiceCategory: async (id: string, data: UpdateServiceCategoryInput) => {
    return await apiClient.patch<IServiceCategoryResponse>(`/education/service-categories/${id}`, data);
  },
};

export default serviceCategoryService;
