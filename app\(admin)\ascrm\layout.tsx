import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import '@/app/globals.css';
import { NavBar, SideBar } from '@/components/dashboard/ascrm/misc';
import LayoutWrapper from './LayoutWrapper';
import QueryProvider from '@/lib/react-query/QueryProvider';
import ToastProvider from '@/components/providers/ToastProvider';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'Master Dashboard',
  description: 'Generated by create next app',
};

export default function RootLayout({ children }: Readonly<{ children: React.ReactNode }>) {
  return (
    <html lang='en'>
      <body className={`${inter.className} bg-slate-100`}>
        <QueryProvider>
          <LayoutWrapper>
            <SideBar />
            <div className='flex flex-col w-full min-h-screen'>
              <NavBar />
              <main className='flex-1 p-4 bg-slate-50 rounded-lg m-2'>{children}</main>
            </div>
            <ToastProvider />
          </LayoutWrapper>
        </QueryProvider>
      </body>
    </html>
  );
}
