'use client';

import { useEffect, useState } from 'react';
import { Eye, EyeOff } from 'lucide-react';
import { Input } from '@/components/ui/input';

const PasswordInput = () => {
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [password, setPassword] = useState('*');
  const [confirmPassword, setConfirmPassword] = useState('');

  return (
    <>
      <div className='mt-4 space-y-2'>
        <div className='relative'>
          <Input
            onChange={(e) => setPassword(e.target.value)}
            type={showPassword ? 'text' : 'password'}
            placeholder='Enter new password'
            className='primary-input pr-10'
          />
          <div className='absolute inset-y-0 right-0 flex items-center pr-3 cursor-pointer' onClick={() => setShowPassword(!showPassword)}>
            {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}
          </div>
        </div>
        <div className='relative'>
          <Input
            onChange={(e) => setConfirmPassword(e.target.value)}
            type={showConfirmPassword ? 'text' : 'password'}
            placeholder='Confirm new password'
            className='primary-input pr-10'
          />
          <div
            className='absolute inset-y-0 right-0 flex items-center pr-3 cursor-pointer'
            onClick={() => setShowConfirmPassword(!showConfirmPassword)}
          >
            {showConfirmPassword ? <EyeOff size={20} /> : <Eye size={20} />}
          </div>
        </div>
      </div>
      <button disabled={password !== confirmPassword} className='btn-default-sm mt-4 w-full justify-center !rounded-3xl'>
        Modify Password
      </button>
    </>
  );
};

export default PasswordInput;
