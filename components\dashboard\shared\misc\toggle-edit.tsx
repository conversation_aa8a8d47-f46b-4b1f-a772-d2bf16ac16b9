import React from 'react';
import { PencilOff, Edit2 } from 'lucide-react';
import { cn } from '@/lib/utils';

interface ToggleEditProps {
  editItem: boolean;
  toggleEditItem: (setItem: React.Dispatch<React.SetStateAction<boolean>>) => void;
  setEditItem: React.Dispatch<React.SetStateAction<boolean>>;
  title?: string;
  disabled?: boolean;
}

const ToggleEdit: React.FC<ToggleEditProps> = ({ editItem, toggleEditItem, setEditItem, title, disabled = false }) => {
  const buttonContent = (
    <button
      onClick={() => !disabled && toggleEditItem(setEditItem)}
      className={cn(
        'py-2 px-3 bg-primaryColor-50 text-primaryColor rounded transition-all duration-300',
        disabled && 'opacity-50 cursor-not-allowed'
      )}
      disabled={disabled}
    >
      {editItem ? <PencilOff size={16} /> : <Edit2 size={16} />}
    </button>
  );

  if (title) {
    return (
      <div className='flex items-start justify-between'>
        <h2 className='text-xl font-semibold mb-4'>{title}</h2>
        {buttonContent}
      </div>
    );
  }

  return buttonContent;
};

export default ToggleEdit;
