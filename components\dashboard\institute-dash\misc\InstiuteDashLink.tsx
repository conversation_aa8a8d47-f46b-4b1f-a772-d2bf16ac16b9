import Link, { LinkProps } from 'next/link';
import { INSTITUTE_DASH_PATH } from '@/constants/institute-dash';

interface IInstiuteDashLink extends LinkProps {
  children: React.ReactNode;
  className?: string;
}

const InstiuteDashLink = ({ children, href, className, ...props }: IInstiuteDashLink) => (
  <Link href={`${INSTITUTE_DASH_PATH}${href}`} className={className} {...props}>
    {children}
  </Link>
);

export default InstiuteDashLink;
