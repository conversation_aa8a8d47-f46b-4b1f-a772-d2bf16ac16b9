'use client';

import { Form } from '@/components/ui/form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import { toast } from 'react-toastify';
import { PrimaryInput, PrimarySwitchInput, SubmitButton } from '@/components/forms';
import { IAddUpdateFormProps } from '@/types/api';
import { useCreateDegree, useUpdateDegree } from '@/hooks/education/college.hooks';
import { IDegreeDocument } from '@/server/services/education/college.service';
import { createDegreeSchema } from '@/validation/schemas/education/college.schema';
import { extractId } from '@/validation/utils/form.utils';

interface IDegreeFormProps extends IAddUpdateFormProps<IDegreeDocument> {
  onSuccess: () => void;
  degreeLevelId?: string | null;
}

const DegreeForm: React.FC<IDegreeFormProps> = ({ data, onSuccess, degreeLevelId }) => {
  const { mutateAsync: createDegree, isPending: isCreating } = useCreateDegree();
  const { mutateAsync: updateDegree, isPending: isUpdating } = useUpdateDegree();

  const degreeId = data?._id;
  const isSubmitting = isCreating || isUpdating;

  const form = useForm<z.infer<typeof createDegreeSchema>>({
    resolver: zodResolver(createDegreeSchema),
    defaultValues: {
      name: data?.name || '',
      degreeLevel: extractId(data?.degreeLevel) || degreeLevelId || '',
      isActive: data?.isActive !== undefined ? data.isActive : true,
    },
  });

  const onSubmit = async (values: z.infer<typeof createDegreeSchema>) => {
    try {
      const result = degreeId ? await updateDegree({ id: degreeId, data: values }) : await createDegree(values);

      if (!result.success) throw new Error(result.message || 'Operation failed');

      toast.success(result.message);
      onSuccess();
    } catch (error: any) {
      toast.error(error.message || 'An error occurred');
    }
  };

  return (
    <section className='relative'>
      <div className='bg-white rounded-lg shadow-sm p-6 max-w-2xl'>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className='grid grid-cols-1 gap-6 items-start justify-start'>
            {/* Name field */}
            <PrimaryInput form={form} name='name' label='Degree Name' placeholder='Enter degree name' required />

            {/* Is Active */}
            <PrimarySwitchInput
              form={form}
              name='isActive'
              label='Is this degree active?'
              description='Making it inactive will hide it from the website.'
            />

            <SubmitButton
              submittingLabel={degreeId ? 'Updating...' : 'Creating...'}
              label={degreeId ? 'Update Degree' : 'Create Degree'}
              isSubmitting={isSubmitting}
              disabled={isSubmitting}
              className='w-fit'
            />
          </form>
        </Form>
      </div>
    </section>
  );
};

export default DegreeForm;
