import apiClient from '@/server/apiClient';
import { IAPIResponse, IPagination } from '@/types/api';
import {
  CreateBoardInput,
  CreateClassInput,
  CreateSubjectInput,
  UpdateBoardInput,
  UpdateClassInput,
  UpdateSubjectInput,
} from '@/validation/schemas/education/school.schema';

// Types for School Boards
export interface IBoardDocument extends CreateBoardInput {
  _id: string;
  createdAt: string;
  updatedAt: string;
}

export interface IBoardsResponse {
  boards: IBoardDocument[];
  pagination: IPagination;
}

export interface IBoardResponse {
  board: IBoardDocument;
}

// Types for School Classes
export interface IClassDocument extends CreateClassInput {
  _id: string;
  createdAt: string;
  updatedAt: string;
  boardDetails?: IBoardDocument;
}

export interface IClassesResponse {
  classes: IClassDocument[];
  pagination: IPagination;
}

export interface IClassResponse {
  class: IClassDocument;
}

// Types for School Subjects
export interface ISubjectDocument extends CreateSubjectInput {
  _id: string;
  createdAt: string;
  updatedAt: string;
  boardDetails?: IBoardDocument;
  classDetails?: IClassDocument;
}

export interface ISubjectsResponse {
  subjects: ISubjectDocument[];
  pagination: IPagination;
}

export interface ISubjectResponse {
  subject: ISubjectDocument;
}

// School Services

// 2. School Boards Service
const boardService = {
  getAllBoards: async (params?: Record<string, any>) => {
    const queryParams = new URLSearchParams(params);
    return await apiClient.get<IBoardsResponse>(`/education/boards?${queryParams.toString()}`);
  },

  getBoardById: async (id: string) => {
    return await apiClient.get<IBoardResponse>(`/education/boards/${id}`);
  },

  createBoard: async (data: CreateBoardInput) => {
    return await apiClient.post<IBoardResponse>('/education/boards', data);
  },

  updateBoard: async (id: string, data: UpdateBoardInput) => {
    return await apiClient.patch<IBoardResponse>(`/education/boards/${id}`, data);
  },

  deleteBoard: async (id: string) => {
    return await apiClient.delete<IAPIResponse>(`/education/boards/${id}`);
  },
};

// 3. School Classes Service
const classService = {
  getAllClasses: async (params?: Record<string, any>) => {
    const queryParams = new URLSearchParams(params);
    return await apiClient.get<IClassesResponse>(`/education/classes?${queryParams.toString()}`);
  },

  getClassesByBoard: async (boardId: string, params?: Record<string, any>) => {
    const queryParams = new URLSearchParams(params);
    return await apiClient.get<{ classes: IClassDocument[] }>(`/education/classes/board/${boardId}?${queryParams.toString()}`);
  },

  getClassById: async (id: string) => {
    return await apiClient.get<IClassResponse>(`/education/classes/${id}`);
  },

  createClass: async (data: CreateClassInput) => {
    return await apiClient.post<IClassResponse>('/education/classes', data);
  },

  updateClass: async (id: string, data: UpdateClassInput) => {
    return await apiClient.patch<IClassResponse>(`/education/classes/${id}`, data);
  },

  deleteClass: async (id: string) => {
    return await apiClient.delete<IAPIResponse>(`/education/classes/${id}`);
  },
};

// 4. School Subjects Service
const subjectService = {
  getAllSubjects: async (params?: Record<string, any>) => {
    const queryParams = new URLSearchParams(params);
    return await apiClient.get<ISubjectsResponse>(`/education/subjects?${queryParams.toString()}`);
  },

  getSubjectsByClass: async (classId: string, params?: Record<string, any>) => {
    const queryParams = new URLSearchParams(params);
    return await apiClient.get<{ subjects: ISubjectDocument[] }>(`/education/subjects/class/${classId}?${queryParams.toString()}`);
  },

  getSubjectById: async (id: string) => {
    return await apiClient.get<ISubjectResponse>(`/education/subjects/${id}`);
  },

  createSubject: async (data: CreateSubjectInput) => {
    return await apiClient.post<ISubjectResponse>('/education/subjects', data);
  },

  updateSubject: async (id: string, data: UpdateSubjectInput) => {
    return await apiClient.patch<ISubjectResponse>(`/education/subjects/${id}`, data);
  },

  deleteSubject: async (id: string) => {
    return await apiClient.delete<IAPIResponse>(`/education/subjects/${id}`);
  },
};

export { boardService, classService, subjectService };
