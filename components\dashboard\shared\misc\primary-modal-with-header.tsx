'use client';

import { X } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';

type ModalVariant = 'primary' | 'secondary' | 'green' | 'purple';

const modalVariants = {
  primary: {
    iconBg: 'bg-gradient-to-br from-primaryColor-500 to-primaryColor-600',
    iconBlur: 'bg-gradient-to-br from-primaryColor-400 to-primaryColor-600',
  },
  secondary: {
    iconBg: 'bg-gradient-to-br from-secondaryColor-500 to-secondaryColor-600',
    iconBlur: 'bg-gradient-to-br from-secondaryColor-400 to-secondaryColor-600',
  },
  green: {
    iconBg: 'bg-gradient-to-br from-green-500 to-green-600',
    iconBlur: 'bg-gradient-to-br from-green-400 to-green-600',
  },
  purple: {
    iconBg: 'bg-gradient-to-br from-purple-500 to-purple-600',
    iconBlur: 'bg-gradient-to-br from-purple-400 to-purple-600',
  },
};

interface PrimaryModalWithHeaderProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  subtitle?: string;
  icon?: React.ReactNode;
  variant?: ModalVariant;
  maxWidth?: string;
  children: React.ReactNode;
}

const PrimaryModalWithHeader = ({
  isOpen,
  onClose,
  title,
  subtitle,
  icon,
  variant = 'primary',
  maxWidth = 'max-w-3xl',
  children,
}: PrimaryModalWithHeaderProps) => {
  const colors = modalVariants[variant];
  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          className='fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm'
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
        >
          <motion.div
            className={cn('relative w-full max-h-[90vh] overflow-hidden bg-white rounded-2xl shadow-2xl', maxWidth)}
            initial={{ scale: 0.95, y: 20, opacity: 0 }}
            animate={{ scale: 1, y: 0, opacity: 1 }}
            exit={{ scale: 0.95, y: 20, opacity: 0 }}
            transition={{ type: 'spring', damping: 25, stiffness: 300 }}
          >
            {title || icon || subtitle ? (
              <div className='sticky top-0 z-10 flex items-center justify-between p-6 bg-white border-b border-gray-100'>
                <div className='flex items-center gap-4'>
                  {icon && (
                    <div className={cn('relative w-12 h-12 rounded-xl flex items-center justify-center shadow-md', colors.iconBg)}>
                      <div className={cn('absolute inset-0 rounded-xl opacity-50 blur-[2px]', colors.iconBlur)}></div>
                      <div className='relative z-10'>{icon}</div>
                    </div>
                  )}
                  <div>
                    {title && <h3 className='text-2xl font-bold text-gray-800'>{title}</h3>}
                    {subtitle && <p className='text-sm text-gray-500'>{subtitle}</p>}
                  </div>
                </div>
                <button
                  onClick={onClose}
                  className='w-10 h-10 rounded-full flex items-center justify-center text-gray-500 bg-gray-100 hover:bg-gray-200 transition-colors'
                >
                  <X size={20} />
                </button>
              </div>
            ) : (
              <button
                onClick={onClose}
                className='w-10 h-10 z-20 absolute top-4 right-4 rounded-full flex items-center justify-center text-gray-500 bg-gray-100 hover:bg-gray-200 transition-colors'
              >
                <X size={20} />
              </button>
            )}

            {/* Content */}
            <div className={cn('overflow-y-auto', title || icon || subtitle ? 'max-h-[calc(90vh-80px)]' : 'max-h-[90vh]')}>{children}</div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default PrimaryModalWithHeader;
