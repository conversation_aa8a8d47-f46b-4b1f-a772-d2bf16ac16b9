import { useState } from 'react';
import { PlusCircle, GraduationCap, Edit } from 'lucide-react';
import { calculateAge } from '@/lib/date.utils';
import { cn } from '@/lib/utils';
import StudentForm from './student-form';
import { IChildProfileDocument } from '@/server/services/profile.service';
import { getImageUrl } from '@/lib/string.utils';

interface StudentProfilesProps {
  students: IChildProfileDocument[];
  activeStudentId: string;
  setActiveStudentId: (id: string) => void;
}

const StudentProfiles = ({ students, activeStudentId, setActiveStudentId }: StudentProfilesProps) => {
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [studentToEdit, setStudentToEdit] = useState<IChildProfileDocument | null>(null);

  const handleEditStudent = (student: IChildProfileDocument) => {
    setStudentToEdit(student);
    setIsEditModalOpen(true);
  };

  return (
    <div className='relative'>
      <div className='absolute inset-0 bg-gradient-to-r from-blue-50/40 via-white to-red-50/30 rounded-2xl h-full w-full'></div>
      <div className='absolute inset-0 overflow-hidden pointer-events-none'>
        <div className='absolute -right-10 top-0 w-40 h-40 bg-blue-200/20 rounded-full blur-xl'></div>
        <div className='absolute left-1/4 -bottom-20 w-60 h-60 bg-red-200/20 rounded-full blur-xl'></div>
        <div
          className='absolute inset-0 opacity-5'
          style={{
            backgroundImage: 'linear-gradient(to right, #6b7280 1px, transparent 1px), linear-gradient(to bottom, #6b7280 1px, transparent 1px)',
            backgroundSize: '20px 20px',
          }}
        ></div>
        <div className='absolute left-0 top-0 w-full h-full bg-gradient-to-br from-transparent via-transparent to-blue-100/20'></div>
      </div>

      <div className='relative rounded-2xl bg-white/90 backdrop-blur-md shadow-[0_8px_30px_rgb(0,0,0,0.04)] border border-gray-100/80 p-6'>
        <div className='mb-6 flex flex-col md:flex-row md:items-center justify-between'>
          <div className='flex items-start gap-3'>
            <div className='relative w-10 h-10 rounded-lg bg-gradient-to-br from-red-500 to-red-600 flex items-center justify-center shadow-sm'>
              <div className='absolute inset-0 rounded-lg bg-gradient-to-br from-red-400 to-red-600 opacity-50 blur-[2px]'></div>
              <div className='relative z-10'>
                <GraduationCap size={20} color='white' />
              </div>
            </div>

            <div>
              <h3 className='text-xl font-bold text-gray-800 mb-1'>Student Profiles</h3>
              <p className='text-sm text-gray-500'>Select a student to view their education details</p>
            </div>
          </div>

          <div className='mt-4 md:mt-0 flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-blue-50 to-blue-100 text-blue-700 rounded-lg text-sm font-medium shadow-sm border border-blue-100'>
            <div className='w-6 h-6 rounded-full bg-blue-500/10 flex items-center justify-center'>
              <div className='w-2 h-2 rounded-full bg-blue-500'></div>
            </div>
            <span>{students.length} Students</span>
          </div>
        </div>

        <div className='relative z-10 flex flex-nowrap overflow-x-auto gap-4 hide-scrollbar pb-2'>
          {students.map((student) => (
            <button
              key={student._id}
              onClick={() => setActiveStudentId(student._id)}
              className={cn(
                'group relative flex flex-col items-center justify-center transition-all min-w-[140px] rounded-xl p-4 overflow-hidden',
                activeStudentId === student._id
                  ? 'bg-gradient-to-br from-blue-50 to-blue-100 shadow-md border border-blue-100'
                  : 'bg-red-50/50 shadow-sm border border-red-100'
              )}
            >
              {activeStudentId === student._id && (
                <div className='absolute inset-0 pointer-events-none overflow-hidden'>
                  <div className='absolute -right-4 top-4 w-12 h-12 rounded-full bg-blue-100 transition-opacity'></div>
                  <div className='absolute left-2 top-2 w-2 h-2 rounded-full bg-blue-200 transition-opacity'></div>
                  <div className='absolute -left-2 top-2 w-6 h-6 rotate-45 bg-blue-100/50 transition-opacity'></div>
                </div>
              )}
              {activeStudentId === student._id && (
                <div className='absolute top-0 left-1/2 transform -translate-x-1/2 h-1 w-12 rounded-full bg-gradient-to-r from-blue-500 to-blue-600 transition-all'></div>
              )}

              <div className='relative mb-3'>
                {activeStudentId === student._id && (
                  <div className='absolute -inset-2 rounded-full blur-md bg-gradient-to-r from-blue-400 to-blue-600 opacity-15 transition-opacity duration-300'></div>
                )}

                <div
                  className={cn(
                    'relative w-12 h-12 rounded-full flex items-center justify-center overflow-hidden transition-all',
                    activeStudentId === student._id ? 'border-2 border-blue-500 ring-1 ring-blue-200' : 'border-1 border-red-300 ring-1 ring-red-100'
                  )}
                >
                  {student.avatar ? (
                    <img src={getImageUrl(student.avatar)} alt={student.fullName} className='w-full h-full object-cover' />
                  ) : (
                    <div
                      className={cn('w-full h-full flex items-center justify-center', activeStudentId === student._id ? 'bg-blue-100' : 'bg-red-50')}
                    >
                      <span className={cn('text-sm font-medium', activeStudentId === student._id ? 'text-blue-600' : 'text-red-600')}>
                        {student.fullName.charAt(0)}
                      </span>
                    </div>
                  )}

                  <div
                    className={cn(
                      'absolute bottom-0 right-0 w-3 h-3 rounded-full border-1 border-white transition-all',
                      activeStudentId === student._id ? 'bg-blue-500' : 'bg-red-400'
                    )}
                  >
                    {activeStudentId === student._id && <span className='absolute inset-0 rounded-full bg-blue-500 opacity-75 animate-ping'></span>}
                  </div>
                </div>

                {activeStudentId === student._id && (
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      handleEditStudent(student);
                    }}
                    className='absolute -top-1 -right-1 w-5 h-5 rounded-full bg-white flex items-center justify-center shadow-sm hover:bg-blue-50 transition-colors'
                  >
                    <Edit size={12} className='text-blue-600' />
                  </button>
                )}

                {activeStudentId === student._id && (
                  <div className='absolute -bottom-0.5 right-0 w-4 h-4 rounded-full bg-white flex items-center justify-center p-0.5 shadow-sm'>
                    <div className='w-full h-full rounded-full bg-gradient-to-r from-blue-500 to-blue-600'></div>
                  </div>
                )}
              </div>

              <span
                className={cn(
                  'text-sm font-semibold text-center line-clamp-1 transition-colors',
                  activeStudentId === student._id ? 'text-blue-700' : 'text-red-600'
                )}
              >
                {student.fullName}
              </span>

              <div
                className={cn(
                  'mt-1.5 px-3 py-0.5 rounded-full text-xs font-medium transition-all',
                  activeStudentId === student._id ? 'bg-blue-100 text-blue-600 border border-blue-200' : 'bg-red-100 text-red-600'
                )}
              >
                {calculateAge(student.dateOfBirth)} years
              </div>
            </button>
          ))}

          <button
            onClick={() => setIsAddModalOpen(true)}
            className='group relative flex flex-col items-center justify-center min-w-[140px] rounded-xl p-4 bg-gradient-to-br from-red-50 to-red-100/50 shadow-sm border border-red-100 transition-all overflow-hidden hover:shadow-md hover:border-red-200'
          >
            <div className='absolute inset-0 pointer-events-none overflow-hidden'>
              <div className='absolute -right-4 top-4 w-12 h-12 rounded-full bg-red-100 transition-opacity'></div>
              <div className='absolute left-2 top-2 w-2 h-2 rounded-full bg-red-200 transition-opacity'></div>
              <div className='absolute -left-2 top-2 w-6 h-6 rotate-45 bg-red-100/50 transition-opacity'></div>
            </div>

            <div className='relative mb-3'>
              <div className='absolute -inset-2 bg-red-300 rounded-full opacity-10 blur-md transition-all'></div>
              <div className='relative w-12 h-12 rounded-full bg-white border-2 border-dashed border-red-300 flex items-center justify-center transition-all shadow-sm group-hover:border-red-400'>
                <PlusCircle size={20} className='text-red-500 transition-colors group-hover:text-red-600' />
              </div>
            </div>

            <span className='text-sm font-semibold text-red-700 transition-colors group-hover:text-red-800'>Add Child</span>
            <div className='mt-1.5 px-3 py-0.5 rounded-full bg-red-100 text-red-600 text-xs font-medium shadow-sm transition-all group-hover:bg-red-200 group-hover:text-red-700'>
              New Profile
            </div>
          </button>
        </div>
      </div>

      {/* Add Student Modal */}
      <StudentForm isOpen={isAddModalOpen} onClose={() => setIsAddModalOpen(false)} />

      {/* Edit Student Modal */}
      {studentToEdit && (
        <StudentForm
          isOpen={isEditModalOpen}
          onClose={() => {
            setIsEditModalOpen(false);
            setStudentToEdit(null);
          }}
          initialValues={studentToEdit}
        />
      )}
    </div>
  );
};

export default StudentProfiles;
