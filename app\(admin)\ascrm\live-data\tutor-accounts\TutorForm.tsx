'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import { Form } from '@/components/ui/form';
import { PrimaryInput, PrimarySelect, PrimaryTextarea, SubmitButton } from '@/components/forms';

const tutorFormSchema = z.object({
  name: z.string().min(2, { message: 'Name must be at least 2 characters.' }),
  email: z.string().email({ message: 'Invalid email address.' }),
  subject: z.string().min(1, { message: 'Please select a subject.' }),
  experience: z.number().min(0, { message: "Experience can't be negative." }).max(50, { message: 'Experience seems too high.' }),
  description: z
    .string()
    .min(10, { message: 'Description must be at least 10 characters.' })
    .max(500, { message: 'Description must not exceed 500 characters.' }),
});

type TutorFormValues = z.infer<typeof tutorFormSchema>;

const subjects = [
  { value: 'math', label: 'Mathematics' },
  { value: 'science', label: 'Science' },
  { value: 'english', label: 'English' },
  { value: 'history', label: 'History' },
];

export default function SimplifiedTutorForm() {
  const form = useForm<TutorFormValues>({
    resolver: zodResolver(tutorFormSchema),
    defaultValues: {
      name: '',
      email: '',
      subject: '',
      experience: 0,
      description: '',
    },
  });

  async function onSubmit(data: TutorFormValues) {
    await new Promise((resolve) => setTimeout(resolve, 2000));
    console.log(data);
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className='grid grid-cols-2 gap-6'>
        <PrimaryInput form={form} name='name' label='Name' placeholder='Enter your name' required />
        <PrimaryInput form={form} name='email' label='Email' placeholder='Enter your email' type='email' required />
        <PrimarySelect form={form} name='subject' label='Subject' placeholder='Select a subject' required options={subjects} />
        <PrimaryInput form={form} name='experience' label='Years of Experience' type='number' required />
        <div className='col-span-2'>
          <PrimaryTextarea form={form} name='description' label='Description' placeholder='Describe your teaching experience and approach' required />
        </div>

        <SubmitButton
          isSubmitting={form.formState.isSubmitting}
          label='Submit'
          submittingLabel='Submitting...'
          disabled={form.formState.isSubmitting}
          className='w-full'
        />
      </form>
    </Form>
  );
}
