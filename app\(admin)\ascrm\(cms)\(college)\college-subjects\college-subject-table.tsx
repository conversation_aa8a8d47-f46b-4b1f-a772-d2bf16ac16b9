'use client';

import { useState, useEffect, useCallback } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { AdminDashActionLinks, HeadingBar, LimitFilter, MasterFilter, MasterTable, SectionWrapper } from '@/components/dashboard/ascrm/misc';
import { ErrorState } from '@/components/dashboard/ascrm/misc/StateUI';
import { filterTypeMaps, FilterTypeKey } from '@/constants';
import { FilterValues, FilterOption } from '@/types/api';
import { useGetAllCollegeSubjects, useDeleteCollegeSubject, useGetCollegeSubjectById, useGetBranchById } from '@/hooks/education/college.hooks';
import { PrimaryFormModal, useModal, StatusToggle } from '@/components/dashboard/shared/misc';
import CollegeSubjectForm from './college-subject-form';
import { collegeSubjectService } from '@/server/services/education/college.service';
import { QUERY_KEYS } from '@/lib/react-query/queryKeys';

const CollegeSubjectTable: React.FC = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const addSubjectModal = useModal();
  const editSubjectModal = useModal();

  const [selectedSubjectId, setSelectedSubjectId] = useState<string | null>(null);
  const [filters, setFilters] = useState<FilterValues>({});
  const [branchName, setBranchName] = useState<string>('');
  const [degreeName, setDegreeName] = useState<string>('');
  const [degreeLevelName, setDegreeLevelName] = useState<string>('');
  const [streamName, setStreamName] = useState<string>('');

  const branchId = searchParams.get('branch');

  if (!branchId) return <ErrorState message='You are not authorized to view this page.' />;

  const { data: branchData, error: branchError } = useGetBranchById(branchId || '');

  useEffect(() => {
    if (branchData?.data?.branch) {
      const branch = branchData.data.branch;
      setBranchName(branch.name);

      if (branch.degreeDetails) {
        const degree = branch.degreeDetails;
        setDegreeName(degree.name);

        if (degree.degreeLevelDetails) {
          setDegreeLevelName(degree.degreeLevelDetails.name);
        }

        if (degree.streamDetails) {
          setStreamName(degree.streamDetails.name);
        }
      }
    }
  }, [branchData]);

  const updateFiltersFromParams = useCallback(() => {
    const initialFilters: FilterValues = {};
    searchParams.forEach((value, key) => {
      if (key !== 'page' && key !== 'branch') {
        initialFilters[key] = value;
      }
    });
    setFilters(initialFilters);
  }, [searchParams]);

  useEffect(() => {
    updateFiltersFromParams();
  }, [updateFiltersFromParams]);

  const page = Number(searchParams.get('page')) || 1;

  const queryParams = { page, branch: branchId, ...filters };

  const { data: subjectsResponse, isLoading, error } = useGetAllCollegeSubjects(queryParams);
  const deleteItem = useDeleteCollegeSubject();
  const {
    data: selectedSubjectData,
    isLoading: isLoadingSelectedSubject,
    error: selectedSubjectError,
  } = useGetCollegeSubjectById(selectedSubjectId || '');

  const subjects = subjectsResponse?.data?.subjects || [];
  const pagination = subjectsResponse?.data?.pagination;

  const handleEditSubject = (id: string) => {
    setSelectedSubjectId(id);
    editSubjectModal.open();
  };

  if (error || branchError) {
    return <ErrorState message={error?.message || branchError?.message || 'An unexpected error occurred.'} />;
  }

  const headers = ['Name', 'Status', 'Actions'];

  const rows = subjects.map((subject) => {
    return {
      id: subject._id,
      values: [
        <p className='font-medium'>{subject.name}</p>,
        <StatusToggle
          id={subject._id}
          isActive={subject.isActive}
          updateFn={collegeSubjectService.updateCollegeSubject}
          queryKey={[QUERY_KEYS.EDUCATION.COLLEGE_SUBJECTS, [QUERY_KEYS.EDUCATION.COLLEGE_SUBJECTS, { page, branch: branchId, ...filters }]]}
          entityName='Subject'
        />,
        <AdminDashActionLinks
          delete
          editAction={() => handleEditSubject(subject._id)}
          basePath='/college-subjects'
          id={subject._id}
          itemName={subject.name}
          deleteItem={() => deleteItem.mutateAsync(subject._id)}
        />,
      ],
    };
  });

  const filterOptions: FilterOption[] = [
    {
      type: filterTypeMaps.input.key as FilterTypeKey,
      label: 'Subject Name',
      key: 'name',
    },
  ];

  const handleApplyFilters = (newFilters: FilterValues) => {
    const params = new URLSearchParams(searchParams);
    Object.entries(newFilters).forEach(([key, value]) => {
      if (value && typeof value === 'string') {
        params.set(key, value);
      } else {
        params.delete(key);
      }
    });
    params.set('page', '1');
    router.push(`?${params.toString()}`);
  };

  const handleResetFilters = () => {
    const params = new URLSearchParams();
    params.set('branch', branchId);
    params.set('page', '1');
    router.push(`?${params.toString()}`);
  };

  let title = 'College Subjects';

  if (branchName) {
    title = `Subjects of ${branchName}`;

    if (degreeName) {
      title += ` (${degreeName}`;

      if (degreeLevelName && streamName) {
        title += `, ${degreeLevelName}, ${streamName}`;
      }

      title += ')';
    }
  }

  return (
    <SectionWrapper>
      <HeadingBar
        title={title}
        elements={[
          <LimitFilter
            key='limit'
            value={(filters.limit as string) || '10'}
            onChange={(value: string) => {
              handleApplyFilters({ limit: value });
            }}
          />,
          <MasterFilter
            key='filter'
            filterOptions={filterOptions}
            onApplyFilters={handleApplyFilters}
            onResetFilters={handleResetFilters}
            initialFilters={filters}
          />,
        ]}
        addAction={addSubjectModal.open}
      />
      <MasterTable headers={headers} rows={rows} isLoading={isLoading} pagination={pagination} />

      {/* Add Subject Modal */}
      <PrimaryFormModal
        isOpen={addSubjectModal.isOpen}
        onClose={addSubjectModal.close}
        title={branchName ? `Add New Subject for ${branchName}` : 'Add New College Subject'}
        subtitle='Create a new college subject for your platform'
        size='sm'
        showSparkles={true}
      >
        <CollegeSubjectForm onSuccess={addSubjectModal.close} branchId={branchId} />
      </PrimaryFormModal>

      {/* Edit Subject Modal */}
      <PrimaryFormModal
        isOpen={editSubjectModal.isOpen}
        onClose={() => {
          editSubjectModal.close();
          setSelectedSubjectId(null);
        }}
        title={branchName ? `Edit ${branchName} Subject` : 'Edit College Subject'}
        subtitle='Update existing college subject details'
        size='sm'
        isLoading={isLoadingSelectedSubject}
        loadingTitle='Loading Subject Data'
        loadingMessage='Please wait while we fetch the subject details...'
        isError={!!selectedSubjectError}
        errorMessage={selectedSubjectError?.message}
      >
        {selectedSubjectData?.data?.subject && (
          <CollegeSubjectForm data={selectedSubjectData.data.subject} onSuccess={editSubjectModal.close} branchId={branchId} />
        )}
      </PrimaryFormModal>
    </SectionWrapper>
  );
};

export default CollegeSubjectTable;
