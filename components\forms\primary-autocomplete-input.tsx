import type React from 'react';
import { useState, useRef, useEffect } from 'react';
import { FormField, FormItem, FormControl, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { ChevronDown } from 'lucide-react';
import type { UseFormReturn, FieldValues, Path } from 'react-hook-form';
import { inputClassName } from './form.constants';
import { cn } from '@/lib/utils';

interface PrimaryAutoCompleteInputProps<TFieldValues extends FieldValues> {
  form: UseFormReturn<TFieldValues>;
  name: Path<TFieldValues>;
  label?: string;
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  variant?: 'primary' | 'secondary' | 'green' | 'purple';
  onFocus?: () => void;
  onSelect?: (value: string) => void;
  options: { value: string; label: string }[];
  icon?: React.ReactNode;
  onInputChange?: (value: string) => void;
}

const PrimaryAutoCompleteInput = <TFieldValues extends FieldValues>({
  form,
  name,
  label,
  placeholder = 'Type to search...',
  required = false,
  disabled = false,
  variant = 'primary',
  onFocus,
  onSelect,
  options,
  icon,
  onInputChange,
}: PrimaryAutoCompleteInputProps<TFieldValues>) => {
  const [isOpen, setIsOpen] = useState(false);
  const [filteredSuggestions, setFilteredSuggestions] = useState<{ value: string; label: string }[]>([]);
  const [inputValue, setInputValue] = useState('');
  const dropdownRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const isSelectingRef = useRef(false);

  useEffect(() => {
    const fieldValue = form.getValues(name);
    if (fieldValue && fieldValue !== inputValue) {
      setInputValue(fieldValue);
    }
  }, [form, name, inputValue]);

  useEffect(() => {
    if (isSelectingRef.current) {
      isSelectingRef.current = false;
      return;
    }

    if (inputValue.trim()) {
      const filtered = options.filter(
        (option) => option.label.toUpperCase().includes(inputValue.toUpperCase()) || option.value.toUpperCase().includes(inputValue.toUpperCase())
      );
      setFilteredSuggestions(filtered);
    } else {
      setFilteredSuggestions([]);
    }
  }, [inputValue, options]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleInputChange = (value: string, onChange: (value: string) => void) => {
    setInputValue(value);
    onChange(value);
    onInputChange?.(value);
    if (value.trim()) {
      setIsOpen(true);
    } else {
      setIsOpen(false);
    }
  };

  const handleSuggestionClick = (suggestion: { value: string; label: string }, onChange: (value: string) => void) => {
    isSelectingRef.current = true;
    setInputValue(suggestion.label);
    onChange(suggestion.value);
    onSelect?.(suggestion.value);
    setFilteredSuggestions([]);
    setIsOpen(false);
    inputRef.current?.blur();
  };

  const handleInputFocus = () => {
    if (inputValue.trim() && filteredSuggestions.length > 0) {
      setIsOpen(true);
    }
    onFocus?.();
  };

  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => (
        <FormItem className='w-full'>
          {label && (
            <FormLabel className='primary-label'>
              <span>{label}</span>
              {required && <span className='text-primaryColor'>*</span>}
            </FormLabel>
          )}
          <FormControl>
            <div className='relative' ref={dropdownRef}>
              <div className='relative'>
                {icon && <div className='absolute left-3 top-1/2 -translate-y-1/2 pointer-events-none z-10'>{icon}</div>}
                <Input
                  {...field}
                  ref={inputRef}
                  className={cn(inputClassName[variant], icon ? 'pl-10 pr-10' : 'pr-10')}
                  placeholder={placeholder}
                  disabled={disabled}
                  value={inputValue || field.value || ''}
                  onChange={(e) => {
                    handleInputChange(e.target.value, field.onChange);
                  }}
                  onFocus={handleInputFocus}
                  autoComplete='off'
                />
                <div className='absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none'>
                  <ChevronDown className={cn('h-4 w-4 text-gray-400 transition-transform', isOpen && 'rotate-180')} />
                </div>
              </div>

              {/* Dropdown */}
              {isOpen && filteredSuggestions.length > 0 && (
                <div className='absolute top-full left-0 right-0 z-50 mt-1 bg-white border border-gray-200 rounded-md shadow-lg max-h-60 overflow-y-auto'>
                  {filteredSuggestions.map((suggestion, index) => (
                    <button
                      key={index}
                      type='button'
                      className='w-full px-4 py-3 text-left hover:bg-gray-50 focus:bg-gray-50 focus:outline-none border-b border-gray-100 last:border-b-0 flex items-center gap-3'
                      onClick={() => handleSuggestionClick(suggestion, field.onChange)}
                    >
                      {icon && <span className='flex-shrink-0'>{icon}</span>}
                      <span className='text-sm text-gray-700'>{suggestion.label}</span>
                    </button>
                  ))}
                </div>
              )}
            </div>
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
};

export default PrimaryAutoCompleteInput;
