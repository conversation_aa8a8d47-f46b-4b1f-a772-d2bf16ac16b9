'use client';

import { useState, useEffect, useCallback } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { AdminDashActionLinks, HeadingBar, LimitFilter, MasterFilter, MasterTable, SectionWrapper } from '@/components/dashboard/ascrm/misc';
import { ErrorState } from '@/components/dashboard/ascrm/misc/StateUI';
import { filterTypeMaps, FilterTypeKey } from '@/constants';
import { FilterValues, FilterOption } from '@/types/api';
import { useGetAllStreams, useDeleteStream, useGetStreamById } from '@/hooks/education/college.hooks';
import { AdminViewButton, PrimaryFormModal, useModal, StatusToggle } from '@/components/dashboard/shared/misc';
import StreamForm from './stream-form';
import { streamService } from '@/server/services/education/college.service';
import { QUERY_KEYS } from '@/lib/react-query/queryKeys';

const StreamTable: React.FC = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const addStreamModal = useModal();
  const editStreamModal = useModal();

  const [selectedStreamId, setSelectedStreamId] = useState<string | null>(null);
  const [filters, setFilters] = useState<FilterValues>({});

  const updateFiltersFromParams = useCallback(() => {
    const initialFilters: FilterValues = {};
    searchParams.forEach((value, key) => {
      if (key !== 'page') {
        initialFilters[key] = value;
      }
    });
    setFilters(initialFilters);
  }, [searchParams]);

  useEffect(() => {
    updateFiltersFromParams();
  }, [updateFiltersFromParams]);

  const page = Number(searchParams.get('page')) || 1;

  const queryParams = { page, ...filters };

  const { data: streamsResponse, isLoading, error } = useGetAllStreams(queryParams);
  const deleteItem = useDeleteStream();
  const { data: selectedStreamData, isLoading: isLoadingSelectedStream, error: selectedStreamError } = useGetStreamById(selectedStreamId || '');

  const streams = streamsResponse?.data?.streams || [];
  const pagination = streamsResponse?.data?.pagination;

  const handleEditStream = (id: string) => {
    setSelectedStreamId(id);
    editStreamModal.open();
  };

  if (error) {
    return <ErrorState message={error?.message || 'An unexpected error occurred.'} />;
  }

  const headers = ['Name', 'Status', 'Actions'];

  const rows = streams.map((stream) => {
    return {
      id: stream._id,
      values: [
        <p className='font-medium'>{stream.name}</p>,
        <StatusToggle
          id={stream._id}
          isActive={stream.isActive}
          updateFn={streamService.updateStream}
          queryKey={[QUERY_KEYS.EDUCATION.STREAMS, [QUERY_KEYS.EDUCATION.STREAMS, filters]]}
          entityName='Stream'
        />,
        <AdminDashActionLinks
          delete
          editAction={() => handleEditStream(stream._id)}
          basePath='/streams'
          id={stream._id}
          itemName={stream.name}
          deleteItem={() => deleteItem.mutateAsync(stream._id)}
          manualAction={[<AdminViewButton variant='icon' href={`/degree-levels/?stream=${stream._id}`} />]}
        />,
      ],
    };
  });

  const filterOptions: FilterOption[] = [
    {
      type: filterTypeMaps.input.key as FilterTypeKey,
      label: 'Stream Name',
      key: 'name',
    },
  ];

  const handleApplyFilters = (newFilters: FilterValues) => {
    const params = new URLSearchParams(searchParams);
    Object.entries(newFilters).forEach(([key, value]) => {
      if (value && typeof value === 'string') {
        params.set(key, value);
      } else {
        params.delete(key);
      }
    });
    params.set('page', '1');
    router.push(`?${params.toString()}`);
  };

  const handleResetFilters = () => {
    router.push(`?page=1`);
  };

  return (
    <SectionWrapper>
      <HeadingBar
        title='College Streams'
        elements={[
          <LimitFilter
            key='limit'
            value={(filters.limit as string) || '10'}
            onChange={(value: string) => {
              handleApplyFilters({ limit: value });
            }}
          />,
          <MasterFilter
            key='filter'
            filterOptions={filterOptions}
            onApplyFilters={handleApplyFilters}
            onResetFilters={handleResetFilters}
            initialFilters={filters}
          />,
        ]}
        addAction={addStreamModal.open}
      />
      <MasterTable headers={headers} rows={rows} isLoading={isLoading} pagination={pagination} />

      {/* Add Stream Modal */}
      <PrimaryFormModal
        isOpen={addStreamModal.isOpen}
        onClose={addStreamModal.close}
        title='Add New College Stream'
        subtitle='Create a new college stream for your platform'
        size='sm'
        showSparkles={true}
      >
        <StreamForm onSuccess={addStreamModal.close} />
      </PrimaryFormModal>

      {/* Edit Stream Modal */}
      <PrimaryFormModal
        isOpen={editStreamModal.isOpen}
        onClose={() => {
          editStreamModal.close();
          setSelectedStreamId(null);
        }}
        title='Edit College Stream'
        subtitle='Update existing college stream details'
        size='sm'
        isLoading={isLoadingSelectedStream}
        loadingTitle='Loading Stream Data'
        loadingMessage='Please wait while we fetch the stream details...'
        isError={!!selectedStreamError}
        errorMessage={selectedStreamError?.message}
      >
        {selectedStreamData?.data?.stream && <StreamForm data={selectedStreamData.data.stream} onSuccess={editStreamModal.close} />}
      </PrimaryFormModal>
    </SectionWrapper>
  );
};

export default StreamTable;
