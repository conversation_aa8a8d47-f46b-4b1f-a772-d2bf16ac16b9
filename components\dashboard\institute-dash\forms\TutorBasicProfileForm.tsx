'use client';

import { FormField, FormItem, FormControl, Form, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { tutorBasicProfileSchema } from '@/lib/validations/tutor-dash/zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { CalendarIcon, Loader2 } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { useRouter } from 'next/navigation';
import * as z from 'zod';
import { gendersMap } from '@/constants';
import { cn } from '@/lib/utils';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { format } from 'date-fns';

const TutorBasicProfileForm = () => {
  const router = useRouter();

  const form = useForm<z.infer<typeof tutorBasicProfileSchema>>({
    resolver: zodResolver(tutorBasicProfileSchema),
    defaultValues: {
      alternativeMobile: '',
      primaryWhatsApp: '',
      alternativeWhatsApp: '',
    },
  });

  async function onSubmit(values: z.infer<typeof tutorBasicProfileSchema>) {
    console.log(values);
  }

  return (
    <div>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className='grid grid-cols-1 md:grid-cols-2 items-start gap-6'>
          {/* Alternative Mobile */}
          <FormField
            control={form.control}
            name='alternativeMobile'
            render={({ field }) => (
              <FormItem className='w-full'>
                <FormLabel className='primary-label'>
                  <span>Alternative Mobile</span>
                </FormLabel>
                <FormControl>
                  <Input className='primary-input' type='text' placeholder='Enter your alternative mobile number' {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Primary WhatsApp */}
          <FormField
            control={form.control}
            name='primaryWhatsApp'
            render={({ field }) => (
              <FormItem className='w-full'>
                <FormLabel className='primary-label'>
                  <span>Primary WhatsApp</span>
                  <span className='text-primaryColor'>*</span>
                </FormLabel>
                <FormControl>
                  <Input className='primary-input' type='text' placeholder='Enter your primary WhatsApp number' {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Alternative WhatsApp */}
          <FormField
            control={form.control}
            name='alternativeWhatsApp'
            render={({ field }) => (
              <FormItem className='w-full'>
                <FormLabel className='primary-label'>
                  <span>Alternative WhatsApp</span>
                </FormLabel>
                <FormControl>
                  <Input className='primary-input' type='text' placeholder='Enter your alternative WhatsApp number' {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Gender */}
          <FormField
            control={form.control}
            name='gender'
            render={({ field }) => (
              <FormItem className='w-full'>
                <FormLabel className='primary-label'>
                  <span>Select a gender</span> <span className='text-primaryColor'>*</span>
                </FormLabel>
                <Select onValueChange={field.onChange}>
                  <FormControl>
                    <SelectTrigger className='primary-select'>
                      <SelectValue placeholder='Select a gender' />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {Object.entries(gendersMap).map(([key, value]) => (
                      <SelectItem key={key} value={value.key}>
                        {value.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name='dateOfBirth'
            render={({ field }) => (
              <FormItem className='flex flex-col'>
                <FormLabel className='primary-label'>
                  <span>Date of Birth</span>
                  <span className='text-primaryColor'>*</span>
                </FormLabel>
                <Popover>
                  <PopoverTrigger asChild>
                    <FormControl>
                      <Button variant={'outline'} className={cn('w-[240px] pl-3 text-left font-normal', !field.value && 'text-muted-foreground')}>
                        {field.value ? format(field.value, 'PPP') : <span>Pick a date</span>}
                        <CalendarIcon className='ml-auto h-4 w-4 opacity-50' />
                      </Button>
                    </FormControl>
                  </PopoverTrigger>
                  <PopoverContent className='w-auto p-0' align='start'>
                    <Calendar
                      mode='single'
                      selected={new Date(field.value)}
                      onSelect={field.onChange}
                      disabled={(date) => date > new Date() || date < new Date('1900-01-01')}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
                <FormMessage />
              </FormItem>
            )}
          />

          <button type='submit' disabled={form.formState.isSubmitting} className='btn-default-md md:col-span-2 w-fit'>
            {form.formState.isSubmitting ? (
              <>
                <Loader2 size={20} className='animate-spin mr-3' />
                <span>Saving...</span>
              </>
            ) : (
              <span>Submit</span>
            )}
          </button>
        </form>
      </Form>
    </div>
  );
};

export default TutorBasicProfileForm;
