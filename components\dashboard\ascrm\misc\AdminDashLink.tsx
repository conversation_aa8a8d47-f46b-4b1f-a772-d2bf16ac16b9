import Link, { LinkProps } from 'next/link';
import { ADMIN_DASHBOARD_PATH } from '@/constants/ascrm';

interface IAdminDashLink extends LinkProps {
  children: React.ReactNode;
  className?: string;
  title?: string;
}

const AdminDashLink = ({ children, href, className, title, ...props }: IAdminDashLink) => (
  <Link href={`${ADMIN_DASHBOARD_PATH}${href}`} className={className} title={title} {...props}>
    {children}
  </Link>
);

export default AdminDashLink;
