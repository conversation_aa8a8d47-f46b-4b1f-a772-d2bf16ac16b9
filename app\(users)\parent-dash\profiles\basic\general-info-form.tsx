'use client';

import { Form } from '@/components/ui/form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import {
  PrimarySelect,
  PrimaryDateInput,
  PrimaryPhoneInput,
  SubmitButton,
  PrimaryInput,
  PrimaryLocationInput,
  PrimaryReferralInput,
} from '@/components/forms';
import { genderMap } from '@/validation/schemas/maps';
import { useUpdateGeneralInfo } from '@/hooks/users/user.hooks';
import { toast } from 'react-toastify';
import { IGetGeneralInfo } from '@/server/services/user.service';
import { generalInfoSchema } from '@/validation/schemas/user.schema';

const studentGeneralInfoSchema = generalInfoSchema.superRefine((data, ctx) => {
  if (!data.parentGuardianFullName || data.parentGuardianFullName.trim().length < 2) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: 'Parent/Guardian full name is required for students and must be at least 2 characters.',
      path: ['parentGuardianFullName'],
    });
  }
});

const ParentBasicProfileForm = ({ parent, handleClose }: { parent: IGetGeneralInfo | undefined; handleClose: () => void }) => {
  const { mutateAsync: updateGeneralInfo, isPending: isUpdatingGeneralInfo } = useUpdateGeneralInfo();

  const form = useForm<z.infer<typeof studentGeneralInfoSchema>>({
    resolver: zodResolver(studentGeneralInfoSchema),
    defaultValues: {
      fullName: parent?.fullName || '',
      alternativeMobile: parent?.alternativeMobile || '',
      primaryWhatsApp: parent?.primaryWhatsApp || '',
      alternativeWhatsApp: parent?.alternativeWhatsApp || '',
      gender: parent?.gender,
      dateOfBirth: parent?.dateOfBirth ? new Date(parent.dateOfBirth) : undefined,
      location: parent?.location || '',
      referralSource: parent?.referralSource || '',
      otherReferralSource: parent?.otherReferralSource || '',
      parentGuardianFullName: parent?.parentGuardianFullName || '',
    },
  });

  async function onSubmit(values: z.infer<typeof studentGeneralInfoSchema>) {
    try {
      const response = await updateGeneralInfo(values);
      if (response.success) {
        toast.success(response.message);
        handleClose();
      } else {
        throw new Error(response.message);
      }
    } catch (error: any) {
      toast.error(error.message || 'An error occurred while updating general information');
    }
  }

  return (
    <div className='w-full'>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className='grid grid-cols-1 md:grid-cols-2 items-start gap-6'>
          <PrimaryInput form={form} name='fullName' label='Full Name' placeholder='Enter your full name' required />
          <PrimaryPhoneInput form={form} name='alternativeMobile' label='Alternative Mobile' placeholder='Enter your alternative mobile number' />
          <PrimaryPhoneInput form={form} name='primaryWhatsApp' label='Primary WhatsApp' placeholder='Enter your primary WhatsApp number' required />
          <PrimaryPhoneInput
            form={form}
            name='alternativeWhatsApp'
            label='Alternative WhatsApp'
            placeholder='Enter your alternative WhatsApp number'
          />
          <PrimarySelect
            form={form}
            name='gender'
            label='Select a gender'
            placeholder='Select a gender'
            required
            options={Object.entries(genderMap).map(([_, value]) => ({ value: value.key, label: value.label }))}
          />
          <PrimaryDateInput form={form} name='dateOfBirth' label='Date of Birth' required />
          <PrimaryLocationInput form={form} name='location' label='Location' placeholder='Enter your location' required />
          <PrimaryInput
            form={form}
            name='parentGuardianFullName'
            label='Parent/Guardian Full Name'
            placeholder='Enter parent/guardian full name'
            required
          />
          <PrimaryReferralInput form={form} selectName='referralSource' otherInputName='otherReferralSource' required={false} />
          <SubmitButton
            isSubmitting={isUpdatingGeneralInfo || form.formState.isSubmitting}
            label='Submit'
            submittingLabel='Saving...'
            className='md:col-span-2 w-fit'
          />
        </form>
      </Form>
    </div>
  );
};

export default ParentBasicProfileForm;
