interface IKeyValueDisplay {
  label: string;
  value: string;
  sameLine?: string;
}

const KeyValueDisplay: React.FC<IKeyValueDisplay> = ({ label, value }) => {
  if (!value || typeof value !== 'string' || value.trim() === '') {
    return null;
  }

  return (
    <div className='bg-gradient-to-br from-white via-gray-50 to-primaryColor-50 rounded-lg p-3 border border-gray-100 shadow-sm hover:shadow-md transition-shadow'>
      <div className='flex flex-col gap-1.5'>
        <span className='text-xs font-semibold text-primaryColor-700 tracking-wide uppercase'>{label}</span>
        <span className='text-sm font-medium text-gray-800 break-words leading-snug'>{value}</span>
      </div>
    </div>
  );
};

export default KeyValueDisplay;
