'use client';

import { Form } from '@/components/ui/form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import { toast } from 'react-toastify';
import { PrimaryInput, PrimarySwitchInput, SubmitButton } from '@/components/forms';
import { IAddUpdateFormProps } from '@/types/api';
import { useCreateDegreeLevel, useUpdateDegreeLevel } from '@/hooks/education/college.hooks';
import { IDegreeLevelDocument } from '@/server/services/education/college.service';
import { createDegreeLevelSchema } from '@/validation/schemas/education/college.schema';
import { extractId } from '@/validation/utils/form.utils';

interface IDegreeLevelFormProps extends IAddUpdateFormProps<IDegreeLevelDocument> {
  onSuccess: () => void;
  streamId?: string | null;
}

const DegreeLevelForm: React.FC<IDegreeLevelFormProps> = ({ data, onSuccess, streamId }) => {
  const { mutateAsync: createDegreeLevel, isPending: isCreating } = useCreateDegreeLevel();
  const { mutateAsync: updateDegreeLevel, isPending: isUpdating } = useUpdateDegreeLevel();

  const degreeLevelId = data?._id;
  const isSubmitting = isCreating || isUpdating;

  const form = useForm<z.infer<typeof createDegreeLevelSchema>>({
    resolver: zodResolver(createDegreeLevelSchema),
    defaultValues: {
      name: data?.name || '',
      stream: extractId(data?.stream) || streamId || '',
      isActive: data?.isActive !== undefined ? data.isActive : true,
    },
  });

  const onSubmit = async (values: z.infer<typeof createDegreeLevelSchema>) => {
    try {
      const result = degreeLevelId ? await updateDegreeLevel({ id: degreeLevelId, data: values }) : await createDegreeLevel(values);

      if (!result.success) throw new Error(result.message || 'Operation failed');

      toast.success(result.message);
      onSuccess();
    } catch (error: any) {
      toast.error(error.message || 'An error occurred');
    }
  };

  return (
    <section className='relative'>
      <div className='bg-white rounded-lg shadow-sm p-6 max-w-2xl'>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className='grid grid-cols-1 gap-6 items-start justify-start'>
            {/* Name field */}
            <PrimaryInput form={form} name='name' label='Degree Level Name' placeholder='Enter degree level name' required />

            {/* Is Active */}
            <PrimarySwitchInput
              form={form}
              name='isActive'
              label='Is this degree level active?'
              description='Making it inactive will hide it from the website.'
            />

            <SubmitButton
              submittingLabel={degreeLevelId ? 'Updating...' : 'Creating...'}
              label={degreeLevelId ? 'Update Degree Level' : 'Create Degree Level'}
              isSubmitting={isSubmitting}
              disabled={isSubmitting}
              className='w-fit'
            />
          </form>
        </Form>
      </div>
    </section>
  );
};

export default DegreeLevelForm;
