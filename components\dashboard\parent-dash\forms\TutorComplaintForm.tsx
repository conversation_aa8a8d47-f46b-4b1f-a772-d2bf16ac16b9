'use client';

import { FormField, FormItem, FormControl, Form, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { useForm, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Loader2, X } from 'lucide-react';
import * as z from 'zod';
import { tutorComplaintSchema } from '@/lib/validations/tutor-dash/zod';
import { complaintCategoryMap } from '@/constants';

const TutorComplaintForm = () => {
  const form = useForm<z.infer<typeof tutorComplaintSchema>>({
    resolver: zodResolver(tutorComplaintSchema),
    defaultValues: {
      subject: '',
      description: '',
      attachments: [],
    },
  });

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: 'attachments',
  });

  async function onSubmit(values: z.infer<typeof tutorComplaintSchema>) {
    console.log(values);
  }

  return (
    <div>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className='flex flex-col gap-6'>
          <div className='grid grid-cols-2 gap-6'>
            <FormField
              control={form.control}
              name='category'
              render={({ field }) => (
                <FormItem className='w-full'>
                  <FormLabel className='primary-label'>Category</FormLabel>
                  <FormControl>
                    <select {...field} className='primary-select'>
                      <option value='' disabled>
                        Select a category
                      </option>
                      {Object.values(complaintCategoryMap).map((category) => (
                        <option key={category.key} value={category.key}>
                          {category.label}
                        </option>
                      ))}
                    </select>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='subject'
              render={({ field }) => (
                <FormItem className='w-full'>
                  <FormLabel className='primary-label'>Subject</FormLabel>
                  <FormControl>
                    <Input className='primary-input' type='text' placeholder='Enter your subject' {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
          <FormField
            control={form.control}
            name='description'
            render={({ field }) => (
              <FormItem className='w-full'>
                <FormLabel className='primary-label'>Description</FormLabel>
                <FormControl>
                  <textarea className='primary-input' placeholder='Enter your description' {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className='grid grid-cols-2 gap-6'>
            {fields.map((item, index) => (
              <FormField
                key={item.id}
                control={form.control}
                name={`attachments.${index}`}
                render={({ field }) => (
                  <FormItem className='w-full relative'>
                    <FormLabel className='primary-label'>Attachment {index + 1}</FormLabel>
                    <FormControl>
                      <Input type='text' className='primary-input' placeholder='Enter attachment URL' {...field} />
                    </FormControl>
                    <FormMessage />
                    {index > 0 && (
                      <button
                        type='button'
                        onClick={() => remove(index)}
                        className='bg-red-50 text-red-500 py-0.5 px-1 rounded absolute -bottom-5 right-0'
                      >
                        <X size={12} className='cursor-pointer' />
                      </button>
                    )}
                  </FormItem>
                )}
              />
            ))}
          </div>
          <button type='button' onClick={() => append('')} className='btn-default__outline-sm col-span-2 w-fit self-center'>
            Add New Attachment
          </button>

          <ul className='list-decimal list-inside text-sm text-red-500 md:col-span-2'>
            <p className='text-slate-900 text-lg font-semibold mb-1'>Supported documents:</p>
            <li>Accepted formats include both image files (such as JPEG, PNG, etc) and PDF files.</li>
            <li>Please ensure that the documents you upload are of high resolution to avoid any processing delays.</li>
            <li>The maximum allowed file size for each document is 2MB. Files larger than this will not be accepted.</li>
          </ul>

          <button type='submit' disabled={form.formState.isSubmitting} className='btn-default-md w-fit'>
            {form.formState.isSubmitting ? (
              <>
                <Loader2 size={20} className='animate-spin mr-3' />
                <span>Saving...</span>
              </>
            ) : (
              <span>Submit</span>
            )}
          </button>
        </form>
      </Form>
    </div>
  );
};

export default TutorComplaintForm;
