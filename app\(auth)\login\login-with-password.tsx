'use client';

import { Form } from '@/components/ui/form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';

import Link from 'next/link';
import { PrimaryInput, PrimaryPhoneInput, SubmitButton } from '@/components/forms';
import { useState } from 'react';
import { phoneSchema } from '@/validation/schemas/common.schema';
import { useRouter } from 'next/navigation';
import { toast } from 'react-toastify';
import authService from '@/server/services/auth.service';
import { IUserTypeMap } from '@/validation/schemas/maps';
import { userDashboardsByRoleMap } from '@/constants';

const combinedLoginSchema = z
  .object({
    email: z.string().optional(),
    phone: z.string().optional(),
    password: z.string().min(8, { message: 'Password must be at least 8 characters long.' }),
  })
  .refine(
    (data) => {
      return !!data.email || !!data.phone;
    },
    {
      message: 'Please enter either an email address or a phone number',
      path: ['email'],
    }
  )
  .refine(
    (data) => {
      if (data.email) {
        try {
          z.string().email().parse(data.email);
          return true;
        } catch {
          return false;
        }
      }
      return true;
    },
    {
      message: 'Please enter a valid email address',
      path: ['email'],
    }
  )
  .refine(
    (data) => {
      if (data.phone) {
        try {
          phoneSchema.parse(data.phone);
          return true;
        } catch {
          return false;
        }
      }
      return true;
    },
    {
      message: 'Indian phone numbers must be exactly 10 digits',
      path: ['phone'],
    }
  );

type CombinedFormValues = z.infer<typeof combinedLoginSchema>;

const LoginWithEmailPasswordForm = ({ selectedUserType }: { selectedUserType: IUserTypeMap }) => {
  const [activeField, setActiveField] = useState<'email' | 'phone' | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  const form = useForm<CombinedFormValues>({
    resolver: zodResolver(combinedLoginSchema),
    defaultValues: {
      email: '',
      phone: '',
      password: '',
    },
  });

  const handleEmailFocus = () => {
    setActiveField('email');
    if (activeField === 'phone') {
      form.setValue('phone', '');
    }
  };

  const handlePhoneFocus = () => {
    setActiveField('phone');
    if (activeField === 'email') {
      form.setValue('email', '');
    }
  };

  async function onSubmit(data: CombinedFormValues) {
    setIsLoading(true);
    try {
      const loginData = { email: data.email || undefined, phone: data.phone || undefined, password: data.password, userType: selectedUserType! };

      const response = await authService.login(loginData);

      if (response.success) {
        toast.success(response.message);
        router.push(`/${userDashboardsByRoleMap[selectedUserType].dashboard}`);
      } else {
        throw new Error(response.message);
      }
    } catch (error: any) {
      toast.error(error.message || 'An error occurred during login');
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <div>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className='w-full flex flex-col items-start justify-start gap-4'>
          {/* Email Input */}
          <div className={`w-full ${activeField === 'phone' ? 'opacity-50' : ''}`}>
            <PrimaryInput form={form} name='email' label='Email Address' placeholder='Enter your email address' onFocus={handleEmailFocus} />
          </div>

          {/* OR Divider */}
          <div className='relative flex items-center justify-center w-full my-2'>
            <div className='border-t border-gray-300 flex-grow'></div>
            <span className='px-3 text-sm text-gray-500 bg-white'>OR</span>
            <div className='border-t border-gray-300 flex-grow'></div>
          </div>

          {/* Phone Input */}
          <div className={`w-full ${activeField === 'email' ? 'opacity-50' : ''}`}>
            <PrimaryPhoneInput form={form} name='phone' label='Phone Number' placeholder='Enter your phone number' onFocus={handlePhoneFocus} />
          </div>

          {/* Password Input */}
          <PrimaryInput form={form} name='password' label='Password' placeholder='Enter your password' type='password' required />

          <div className='flex justify-end w-full'>
            <Link className='text-sm text-primaryColor font-medium hover:underline text-end' href='#'>
              Forgot Password?
            </Link>
          </div>

          <SubmitButton
            disabled={isLoading || form.formState.isSubmitting}
            isSubmitting={isLoading || form.formState.isSubmitting}
            label='Sign In'
            submittingLabel='Verifying...'
            className='w-full'
          />
        </form>
      </Form>
    </div>
  );
};

export default LoginWithEmailPasswordForm;
