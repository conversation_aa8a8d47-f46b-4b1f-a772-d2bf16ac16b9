'use client';

import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { toast } from 'react-toastify';
import { Form } from '@/components/ui/form';
import { User } from 'lucide-react';
import {
  PrimaryInput,
  PrimaryTextarea,
  PrimarySwitchInput,
  PrimaryMultiSelectForm,
  PrimaryLocationInput,
  SubmitButton,
  CancelButton,
} from '@/components/forms';
import PrimaryMultiInput from '@/components/forms/primary-multi-input';
import { PrimaryModalWithHeader } from '@/components/dashboard/shared/misc';
import { createTuitionInfoSchema, CreateTuitionInfoInput } from '@/validation/schemas/tutor/profiles/tuition-info.schema';
import { useCreateTuitionInfo, useUpdateTuitionInfo } from '@/hooks/tutor/tuition-profile.hooks';
import { deliveryModeOptions } from '@/validation/schemas/enquiry.maps';
import { ITuitionInfoDocument } from '@/server/services/tutor/tuition-profile.service';
import { IAddressOutput } from '@/components/forms/primary-location-input';

interface TuitionInfoFormProps {
  isOpen: boolean;
  onClose: () => void;
  tuitionInfo?: ITuitionInfoDocument;
}

const TuitionInfoForm = ({ isOpen, onClose, tuitionInfo }: TuitionInfoFormProps) => {
  if (!isOpen) return null;

  const createTuitionInfo = useCreateTuitionInfo();
  const updateTuitionInfo = useUpdateTuitionInfo();

  const isSubmitting = createTuitionInfo.isPending || updateTuitionInfo.isPending;
  const isEditing = !!tuitionInfo;

  const form = useForm<CreateTuitionInfoInput>({
    resolver: zodResolver(createTuitionInfoSchema),
    mode: 'onChange',
    defaultValues: {
      totalTeachingExperience: tuitionInfo?.totalTeachingExperience || 0,
      isFullTimeTeacher: tuitionInfo?.isFullTimeTeacher || false,
      teachesSpecialStudents: tuitionInfo?.teachesSpecialStudents || false,
      maxTravelDistance: tuitionInfo?.maxTravelDistance || 0,
      spokenLanguages: tuitionInfo?.spokenLanguages || [],
      deliveryModes: tuitionInfo?.deliveryModes || [],
      location: tuitionInfo?.location || '',
      coordinates: tuitionInfo?.coordinates || undefined,
      description: tuitionInfo?.description || '',
    },
  });

  const handleParsedAddress = (address: IAddressOutput) => {
    if (address.lat && address.lng) {
      form.setValue('coordinates', {
        lat: address.lat,
        lng: address.lng,
      });
    }
  };

  const onSubmit = async (values: CreateTuitionInfoInput) => {
    try {
      if (isEditing && tuitionInfo) {
        const result = await updateTuitionInfo.mutateAsync({
          id: tuitionInfo._id,
          data: values,
        });
        if (!result.success) throw new Error(result.message || 'Operation failed');
        toast.success('Tuition information updated successfully!');
      } else {
        const result = await createTuitionInfo.mutateAsync(values);
        if (!result.success) throw new Error(result.message || 'Operation failed');
        toast.success('Tuition information added successfully!');
      }

      form.reset();
      onClose();
    } catch (error: any) {
      toast.error(error.message || 'Failed to save tuition information');
      console.error(error);
    }
  };

  return (
    <PrimaryModalWithHeader
      isOpen={isOpen}
      onClose={onClose}
      title={`${isEditing ? 'Edit' : 'Add'} Tuition Information`}
      subtitle={`${isEditing ? 'Update' : 'Add'} your teaching information and preferences`}
      icon={<User className='text-white' size={22} />}
      variant='primary'
      maxWidth='max-w-6xl'
    >
      <Form {...form} key={tuitionInfo ? `edit-${tuitionInfo._id}` : 'add-new'}>
        <form onSubmit={form.handleSubmit(onSubmit)} className='p-8'>
          <div className='grid grid-cols-1 md:grid-cols-3 gap-6'>
            {/* Total Teaching Experience */}
            <PrimaryInput
              form={form}
              name='totalTeachingExperience'
              label='Total Teaching Experience (Months)'
              placeholder='Enter experience in months'
              type='number'
              required
              variant='primary'
            />

            {/* Max Travel Distance */}
            <PrimaryInput
              form={form}
              name='maxTravelDistance'
              label='Maximum Travel Distance (KM)'
              placeholder='Enter distance in kilometers'
              type='number'
              required
              variant='primary'
            />

            {/* Spoken Languages */}
            <PrimaryMultiInput
              form={form}
              name='spokenLanguages'
              label='Languages You Can Speak'
              placeholder='Type a language and press Enter or comma'
              required
              variant='primary'
            />

            {/* Location */}
            <PrimaryLocationInput
              form={form}
              name='location'
              label='Your Tuition Location'
              placeholder='Enter your primary teaching location'
              required
              setParsedAddress={handleParsedAddress}
            />

            {/* Delivery Modes */}
            <PrimaryMultiSelectForm
              form={form}
              name='deliveryModes'
              label='Where Do You Want to Teach?'
              options={deliveryModeOptions}
              placeholder='Select teaching modes'
              required
              variant='primary'
            />
          </div>

          {/* Description */}
          <div className='grid grid-cols-1 md:grid-cols-3 gap-6 mt-8'>
            <div className='md:col-span-1'>
              <PrimaryTextarea
                form={form}
                name='description'
                label='Brief Yourself'
                placeholder='Describe your teaching experience, approach, and what makes you unique...'
                rows={4}
                required
                variant='primary'
              />
            </div>

            {/* Switches Row */}
            <div className='bg-gray-50 rounded-xl p-6 flex items-center gap-4 shadow-sm'>
              <PrimarySwitchInput
                form={form}
                name='isFullTimeTeacher'
                label='Are you a full-time teacher?'
                description='Toggle if you are currently working as a full-time teacher'
              />
            </div>
            <div className='bg-gray-50 rounded-xl p-6 flex items-center gap-4 shadow-sm'>
              <PrimarySwitchInput
                form={form}
                name='teachesSpecialStudents'
                label='Do you teach students with special needs?'
                description='Toggle if you have experience teaching students with special abilities'
              />
            </div>
          </div>

          <div className='flex gap-4 justify-end pt-6 mt-10 border-t border-gray-100'>
            <CancelButton onClose={onClose} size='md' />
            <SubmitButton
              isSubmitting={isSubmitting}
              label={isEditing ? 'Update Information' : 'Add Information'}
              submittingLabel={isEditing ? 'Updating...' : 'Adding...'}
              variant='primary'
              size='md'
            />
          </div>
        </form>
      </Form>
    </PrimaryModalWithHeader>
  );
};

export default TuitionInfoForm;
