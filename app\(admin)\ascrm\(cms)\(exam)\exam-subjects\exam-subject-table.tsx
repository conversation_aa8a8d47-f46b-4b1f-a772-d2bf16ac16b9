'use client';

import { useState, useEffect, useCallback } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { AdminDashActionLinks, HeadingBar, LimitFilter, MasterFilter, MasterTable, SectionWrapper } from '@/components/dashboard/ascrm/misc';
import { ErrorState } from '@/components/dashboard/ascrm/misc/StateUI';
import { filterTypeMaps, FilterTypeKey } from '@/constants';
import { FilterValues, FilterOption } from '@/types/api';
import { useGetAllExamSubjects, useDeleteExamSubject, useGetExamSubjectById, useGetExamById } from '@/hooks/education/exam.hooks';
import { PrimaryFormModal, useModal, StatusToggle } from '@/components/dashboard/shared/misc';
import ExamSubjectForm from './exam-subject-form';
import { examSubjectService } from '@/server/services/education/exam.service';
import { QUERY_KEYS } from '@/lib/react-query/queryKeys';

const ExamSubjectTable: React.FC = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const addExamSubjectModal = useModal();
  const editExamSubjectModal = useModal();

  const [selectedExamSubjectId, setSelectedExamSubjectId] = useState<string | null>(null);
  const [filters, setFilters] = useState<FilterValues>({});
  const [examName, setExamName] = useState<string>('');
  const [examCategoryName, setExamCategoryName] = useState<string>('');

  const examId = searchParams.get('exam');

  if (!examId) return <ErrorState message='You are not authorized to view this page.' />;

  const { data: examData, error: examError } = useGetExamById(examId || '');

  useEffect(() => {
    if (examData?.data?.exam) {
      const exam = examData.data.exam;
      setExamName(exam.name);

      if (exam.examCategoryDetails) {
        setExamCategoryName(exam.examCategoryDetails.name);
      }
    }
  }, [examData]);

  const updateFiltersFromParams = useCallback(() => {
    const initialFilters: FilterValues = {};
    searchParams.forEach((value, key) => {
      if (key !== 'page' && key !== 'exam') {
        initialFilters[key] = value;
      }
    });
    setFilters(initialFilters);
  }, [searchParams]);

  useEffect(() => {
    updateFiltersFromParams();
  }, [updateFiltersFromParams]);

  const page = Number(searchParams.get('page')) || 1;

  const queryParams = { page, exam: examId, ...filters };

  const { data: examSubjectsResponse, isLoading, error } = useGetAllExamSubjects(queryParams);
  const deleteItem = useDeleteExamSubject();
  const {
    data: selectedExamSubjectData,
    isLoading: isLoadingSelectedExamSubject,
    error: selectedExamSubjectError,
  } = useGetExamSubjectById(selectedExamSubjectId || '');

  const examSubjects = examSubjectsResponse?.data?.examSubjects || [];
  const pagination = examSubjectsResponse?.data?.pagination;

  const handleEditExamSubject = (id: string) => {
    setSelectedExamSubjectId(id);
    editExamSubjectModal.open();
  };

  if (error || examError) {
    return <ErrorState message={error?.message || examError?.message || 'An unexpected error occurred.'} />;
  }

  const headers = ['Name', 'Status', 'Actions'];

  const rows = examSubjects.map((examSubject) => {
    return {
      id: examSubject._id,
      values: [
        <p className='font-medium'>{examSubject.name}</p>,
        <StatusToggle
          id={examSubject._id}
          isActive={examSubject.isActive}
          updateFn={examSubjectService.updateExamSubject}
          queryKey={[QUERY_KEYS.EDUCATION.EXAM_SUBJECTS, [QUERY_KEYS.EDUCATION.EXAM_SUBJECTS, { page, exam: examId, ...filters }]]}
          entityName='Subject'
        />,
        <AdminDashActionLinks
          delete
          editAction={() => handleEditExamSubject(examSubject._id)}
          basePath='/exam-subjects'
          id={examSubject._id}
          itemName={examSubject.name}
          deleteItem={() => deleteItem.mutateAsync(examSubject._id)}
        />,
      ],
    };
  });

  const filterOptions: FilterOption[] = [
    {
      type: filterTypeMaps.input.key as FilterTypeKey,
      label: 'Subject Name',
      key: 'name',
    },
  ];

  const handleApplyFilters = (newFilters: FilterValues) => {
    const params = new URLSearchParams(searchParams);
    Object.entries(newFilters).forEach(([key, value]) => {
      if (value && typeof value === 'string') {
        params.set(key, value);
      } else {
        params.delete(key);
      }
    });
    params.set('page', '1');
    router.push(`?${params.toString()}`);
  };

  const handleResetFilters = () => {
    const params = new URLSearchParams();
    params.set('exam', examId);
    params.set('page', '1');
    router.push(`?${params.toString()}`);
  };

  let title = 'Exam Subjects';

  if (examName) {
    title = `Subjects of ${examName}`;

    if (examCategoryName) {
      title += ` (${examCategoryName})`;
    }
  }

  return (
    <SectionWrapper>
      <HeadingBar
        title={title}
        elements={[
          <LimitFilter
            key='limit'
            value={(filters.limit as string) || '10'}
            onChange={(value: string) => {
              handleApplyFilters({ limit: value });
            }}
          />,
          <MasterFilter
            key='filter'
            filterOptions={filterOptions}
            onApplyFilters={handleApplyFilters}
            onResetFilters={handleResetFilters}
            initialFilters={filters}
          />,
        ]}
        addAction={addExamSubjectModal.open}
      />
      <MasterTable headers={headers} rows={rows} isLoading={isLoading} pagination={pagination} />

      {/* Add Exam Subject Modal */}
      <PrimaryFormModal
        isOpen={addExamSubjectModal.isOpen}
        onClose={addExamSubjectModal.close}
        title={examName ? `Add New Subject for ${examName}` : 'Add New Exam Subject'}
        subtitle='Create a new exam subject for your platform'
        size='sm'
        showSparkles={true}
      >
        <ExamSubjectForm onSuccess={addExamSubjectModal.close} examId={examId} />
      </PrimaryFormModal>

      {/* Edit Exam Subject Modal */}
      <PrimaryFormModal
        isOpen={editExamSubjectModal.isOpen}
        onClose={() => {
          editExamSubjectModal.close();
          setSelectedExamSubjectId(null);
        }}
        title={examName ? `Edit ${examName} Subject` : 'Edit Exam Subject'}
        subtitle='Update existing exam subject details'
        size='sm'
        isLoading={isLoadingSelectedExamSubject}
        loadingTitle='Loading Subject Data'
        loadingMessage='Please wait while we fetch the subject details...'
        isError={!!selectedExamSubjectError}
        errorMessage={selectedExamSubjectError?.message}
      >
        {selectedExamSubjectData?.data?.examSubject && (
          <ExamSubjectForm data={selectedExamSubjectData.data.examSubject} onSuccess={editExamSubjectModal.close} examId={examId} />
        )}
      </PrimaryFormModal>
    </SectionWrapper>
  );
};

export default ExamSubjectTable;
