'use client';

import { useState } from 'react';
import { BadgeCheck, X } from 'lucide-react';
import { TabsFilterWithBadge, TabsMobileFilterWithBadge } from '@/components/dashboard/shared/misc/TabsFilterWithBadge';

interface FilterProps {
  selectedTab: string;
  setSelectedTab: (tab: string) => void;
  selectedCategory: string;
  setSelectedCategory: (category: string) => void;
  tabCounts: { [key: string]: number };
  categoryCounts: { [key: string]: number };
}

const Filter: React.FC<FilterProps> = ({ selectedTab, setSelectedTab, selectedCategory, setSelectedCategory, tabCounts, categoryCounts }) => {
  const complaintCategoryMap = {
    batch: { key: 'batch', label: 'Batch' },
    wallet: { key: 'wallet', label: 'Wallet' },
    billing: { key: 'billing', label: 'Billing' },
    leads: { key: 'leads', label: 'Leads' },
    other: { key: 'other', label: 'Other' },
  };

  return (
    <>
      <div className='flex flex-col gap-2 rounded-3xl bg-muted p-1 text-muted-foreground max-w-full w-full max-lg:flex-wrap lg:flex-col items-start justify-start h-full lg:w-64 py-6 px-4 shrink-0'>
        {['all', 'seen', 'unseen'].map((tab) => (
          <button
            key={tab}
            type='button'
            onClick={() => setSelectedTab(tab)}
            className={`w-full py-2.5 transition-all duration-300 pl-6 text-sm flex justify-between items-center ${
              selectedTab === tab ? 'bg-white pl-7 pr-4 rounded-full font-medium text-black' : ''
            }`}
          >
            {tab.charAt(0).toUpperCase() + tab.slice(1)} Notifications
            <span className='text-primaryColor border border-primaryColor size-5 text-sm flex items-center justify-center rounded-full shrink-0'>
              {tabCounts[tab] || 0}
            </span>
          </button>
        ))}
      </div>

      <div className='w-full md:col-span-2 lg:col-span-1 flex flex-col gap-2 rounded-3xl bg-muted p-1 text-muted-foreground max-w-full max-lg:flex-wrap lg:flex-col items-start justify-start h-full lg:w-64 py-6 px-4 shrink-0'>
        {Object.entries(complaintCategoryMap).map(([key, { label }], index) => (
          <button
            type='button'
            key={index}
            onClick={() => setSelectedCategory(key)}
            className={`w-full py-2.5 transition-all duration-300 pl-6 text-sm flex justify-between items-center ${
              selectedCategory === key ? 'bg-white pl-7 pr-4 rounded-full font-medium text-black' : ''
            }`}
          >
            {label} Notifications
            <span className='text-primaryColor border border-primaryColor size-5 text-sm flex items-center justify-center rounded-full shrink-0'>
              {categoryCounts[key] || 0}
            </span>
          </button>
        ))}
      </div>
    </>
  );
};

const TutorNotificationPage = () => {
  const [selectedTab, setSelectedTab] = useState('all');
  const [selectedCategory, setSelectedCategory] = useState('all');

  const notifications = [
    {
      id: 1,
      type: 'joined new user',
      title: 'New Registration: Finibus Bonorum et Malorum',
      description: 'Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium',
      sender: 'Allen Deu',
      date: '24 Nov 2018 at 9:30 AM',
      status: 'unseen',
      category: 'batch',
    },
    {
      id: 2,
      type: 'message',
      title: 'Darren Smith sent new message',
      description: 'Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium',
      sender: 'Darren',
      date: '24 Nov 2018 at 9:30 AM',
      status: 'seen',
      category: 'wallet',
    },
    {
      id: 3,
      type: 'comment',
      title: 'Arin Ganshiram Commented on post',
      description: 'Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium',
      sender: 'Arin Ganshiram',
      date: '24 Nov 2018 at 9:30 AM',
      status: 'unseen',
      category: 'billing',
    },
    {
      id: 4,
      type: 'connect',
      title: 'Juliet Den Connect Allen Depk',
      description: 'Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium',
      sender: 'Juliet Den',
      date: '24 Nov 2018 at 9:30 AM',
      status: 'seen',
      category: 'leads',
    },
    {
      id: 5,
      type: 'connect',
      title: 'Juliet Den Connect Allen Depk',
      description: 'Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium',
      sender: 'Juliet Den',
      date: '24 Nov 2018 at 9:30 AM',
      status: 'unseen',
      category: 'other',
    },
  ];

  const filteredNotifications = notifications.filter((notification) => {
    return (
      (selectedTab === 'all' || notification.status === selectedTab) && (selectedCategory === 'all' || notification.category === selectedCategory)
    );
  });

  const tabCounts = {
    all: notifications.length,
    seen: notifications.filter((n) => n.status === 'seen').length,
    unseen: notifications.filter((n) => n.status === 'unseen').length,
  };

  const categoryCounts = notifications.reduce((acc, curr) => {
    acc[curr.category] = (acc[curr.category] || 0) + 1;
    return acc;
  }, {} as { [key: string]: number });

  return (
    <div className='bg-white p-6 rounded-3xl relative lg:min-h-[650px] flex flex-col lg:flex-row items-start justify-start'>
      <div className='lg:w-1/4 gap-5 max-md:hidden grid grid-cols-1 md:grid-cols-3 lg:grid-cols-1'>
        <Filter
          selectedTab={selectedTab}
          setSelectedTab={setSelectedTab}
          selectedCategory={selectedCategory}
          setSelectedCategory={setSelectedCategory}
          tabCounts={tabCounts}
          categoryCounts={categoryCounts}
        />
      </div>
      <div className='lg:w-3/4'>
        {filteredNotifications.map((notification) => (
          <div key={notification.id} className='flex flex-col md:flex-row gap-4 items-start lg:items-center p-4 border-b'>
            <div className='flex items-center justify-center size-10 rounded-full bg-primaryColor text-white'>
              <BadgeCheck size={20} />
            </div>
            <div className='flex flex-col flex-grow max-md:gap-2'>
              <div className='flex flex-col lg:flex-row justify-between items-start lg:items-center'>
                <h3 className='text-base font-semibold'>{notification.title}</h3>
                <span className='text-sm text-gray-500'>{notification.date}</span>
              </div>
              <p className='text-sm text-gray-600'>{notification.description}</p>
              <p className='text-sm text-gray-500'>Sender: {notification.sender}</p>
            </div>
          </div>
        ))}
      </div>
      <div className='w-full md:hidden'>
        <TabsMobileFilterWithBadge
          selectedTab={selectedTab}
          setSelectedTab={setSelectedTab}
          tabCounts={tabCounts}
          showNumbers={false}
          labels={{ all: 'All', seen: 'Seen', unseen: 'Unseen' }}
          showSecondLevel={false}
          secondLevelTabCounts={categoryCounts}
          secondLevelLabels={{
            batch: { label: 'Batch', key: 'batch' },
            wallet: { label: 'Wallet', key: 'wallet' },
            billing: { label: 'Billing', key: 'billing' },
            leads: { label: 'Leads', key: 'leads' },
            other: { label: 'Other', key: 'other' },
          }}
          selectedSecondTab={selectedCategory}
          secondLevelShowNumbers={true}
          setSelectedSecondTab={setSelectedCategory}
        />
      </div>
    </div>
  );
};

export default TutorNotificationPage;
