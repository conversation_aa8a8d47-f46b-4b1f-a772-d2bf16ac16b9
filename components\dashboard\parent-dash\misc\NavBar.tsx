'use client';

import { cn } from '@/lib/utils';
import { useSidebarStore } from '@/store/sidebarStore';
import { AlignJustify, Bell, HandCoins, User } from 'lucide-react';

const NavBar = () => {
  const { isSidebarOpen, toggleSidebar } = useSidebarStore();
  return (
    <nav className='bg-white p-3 md:p-5 w-full flex justify-between items-center rounded-3xl'>
      <div className='flex gap-4 md:gap-6 max-lg:hidden items-center text-sm tracking-wider'>
        <div
          className={cn(
            'mr-3 md:mr-5 cursor-pointer hover:bg-primaryColor-50 hover:text-black p-2.5 md:p-3 rounded-full',
            !isSidebarOpen && 'bg-primaryColor text-white'
          )}
          onClick={toggleSidebar}
        >
          <AlignJustify size={25} className='size-5 md:size-6' />
        </div>
      </div>
      <div className='flex items-center w-full gap-3 md:gap-4'>
        <div className='flex items-center bg-yellow-100 py-1.5 md:py-2 pl-2.5 md:pl-3 pr-3 md:pr-5 rounded-lg gap-1.5 md:gap-2'>
          <span className='text-white bg-yellow-600 size-7 md:size-8 rounded-lg flex items-center justify-center'>
            <HandCoins size={18} strokeWidth={1.5} />
          </span>
          <span className='font-semibold text-xs md:text-base'>630 Coins</span>
        </div>
        <button className='relative p-1.5 md:p-2 rounded-full hover:bg-gray-200'>
          <Bell size={25} className='text-gray-600' />
          <span className='absolute top-0.5 right-2 inline-block size-2 bg-red-600 rounded-full'></span>
        </button>
        <div className='hidden md:flex items-center btn-default-md ml-auto'>
          <User size={20} className='mr-1 md:mr-2' />
          <span className='text-xs md:text-base max-md:hidden'>Yevhen H.</span>
        </div>
      </div>
    </nav>
  );
};

export default NavBar;
