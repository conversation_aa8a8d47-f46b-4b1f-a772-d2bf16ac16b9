'use client';

import Image from 'next/image';
import { useSidebarStore } from '@/store/sidebarStore';
import { cn } from '@/lib/utils';
import { NavigationLinkProps, navigationLinks, NavigationLinksGroupProps } from '@/constants/ascrm';
import AdminDashLink from './AdminDashLink';
import logo from '@/public/images/logo.png';

const Sidebar = () => {
  const { isSidebarOpen } = useSidebarStore();

  return (
    <aside
      className={cn(
        'fixed top-2 left-2 bg-white rounded-3xl py-4',
        'transition-all duration-300 ease-in-out',
        'flex flex-col gap-6',
        'h-[calc(100vh-16px)] overflow-hidden hover:overflow-auto no-scrollbar',
        isSidebarOpen ? 'w-[270px]' : 'w-[72px]'
      )}
    >
      <Logo isSidebarOpen={isSidebarOpen} />
      <nav className={cn('flex-1 overflow-hidden hover:overflow-auto no-scrollbar', isSidebarOpen ? 'w-72' : 'w-auto')}>
        {navigationLinks.map((section, idx) => (
          <NavigationSection key={idx} section={section} />
        ))}
      </nav>
    </aside>
  );
};

const Logo = ({ isSidebarOpen }: { isSidebarOpen: boolean }) => (
  <div>
    <AdminDashLink href='/' className='flex items-center justify-start'>
      {isSidebarOpen ? (
        <Image src={logo || '/placeholder.svg'} alt='Logo' className='object-contain h-10 max-md:w-32' />
      ) : (
        <p className='bg-gradient-to-br from-primaryColor-400 to-primaryColor-600 rounded-full mx-auto size-10 flex items-center justify-center text-white font-bold'>
          PT
        </p>
      )}
    </AdminDashLink>
  </div>
);

const NavigationSection = ({ section }: { section: NavigationLinksGroupProps }) => {
  const { isSidebarOpen } = useSidebarStore();

  return (
    <div>
      {isSidebarOpen && <p className='text-gray-400 text-xs font-medium pl-6 mt-4 mb-2 uppercase'>{section.label}</p>}
      <div className={cn('px-4', !isSidebarOpen && 'flex flex-col items-center justify-start')}>
        {section.links?.map((link, idx) => (
          <NavigationLink key={idx} {...link} />
        ))}
      </div>
    </div>
  );
};

const NavigationLink = ({ href, icon: Icon, title }: NavigationLinkProps) => {
  const { isSidebarOpen } = useSidebarStore();

  return (
    <AdminDashLink
      href={href}
      title={title}
      className={cn(
        'shrink-0 flex items-center transition-all duration-300',
        'text-gray-600 text-sm',
        'relative',
        isSidebarOpen
          ? [
              'w-60 gap-4 pr-4 p-3 rounded-full',
              'hover:pl-6 hover:bg-my-gradient-1 hover:text-white',
              'before:hover:absolute before:hover:h-full before:hover:w-4',
              'before:hover:bg-my-gradient-1 before:hover:-left-7 before:hover:rounded-full',
            ]
          : 'h-10 w-10 justify-center rounded-lg hover:bg-my-gradient-1 hover:text-white'
      )}
    >
      <Icon className='shrink-0 size-6' />
      {isSidebarOpen && <span className='capitalize'>{title}</span>}
    </AdminDashLink>
  );
};

export default Sidebar;
