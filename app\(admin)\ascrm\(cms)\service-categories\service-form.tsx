'use client';

import { Form } from '@/components/ui/form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import { toast } from 'react-toastify';
import { PrimarySelect, PrimarySwitchInput, SubmitButton } from '@/components/forms';
import { IAddUpdateFormProps } from '@/types/api';
import { useCreateServiceCategory, useUpdateServiceCategory } from '@/hooks/service-category.hooks';
import { IServiceCategoryDocument } from '@/server/services/service-category.service';
import { IServiceCategoryMap, serviceCategoryOptions } from '@/validation/schemas/education/index.maps';
import { createServiceCategorySchema } from '@/validation/schemas/service-category.schema';

interface IServiceFormProps extends IAddUpdateFormProps<IServiceCategoryDocument> {
  onSuccess: () => void;
}

const ServiceForm: React.FC<IServiceFormProps> = ({ data, onSuccess }) => {
  const { mutateAsync: createServiceCategory, isPending: isCreating } = useCreateServiceCategory();
  const { mutateAsync: updateServiceCategory, isPending: isUpdating } = useUpdateServiceCategory();

  const serviceCategoryId = data?._id;
  const isSubmitting = isCreating || isUpdating;

  const form = useForm<z.infer<typeof createServiceCategorySchema>>({
    resolver: zodResolver(createServiceCategorySchema),
    defaultValues: {
      name: (data?.name || '') as IServiceCategoryMap,
    },
  });

  const onSubmit = async (values: z.infer<typeof createServiceCategorySchema>) => {
    try {
      const result = serviceCategoryId ? await updateServiceCategory({ id: serviceCategoryId, data: values }) : await createServiceCategory(values);
      if (!result.success) throw new Error(result.message || 'Operation failed');

      toast.success(result.message);
      onSuccess();
    } catch (error: any) {
      toast.error(error.message || 'An error occurred');
    }
  };

  return (
    <section className='relative'>
      <div className='bg-white rounded-lg shadow-sm p-6 max-w-2xl'>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className='grid grid-cols-1 gap-6 items-start justify-start'>
            {/* Name field */}
            <PrimarySelect form={form} name='name' label='Name' placeholder='Enter service category name' required options={serviceCategoryOptions} />

            {/* Is Active  */}
            <PrimarySwitchInput
              form={form}
              name='isActive'
              label='Is this service category active?'
              description='Making it inactive will hide it from the website.'
            />

            <SubmitButton
              submittingLabel={serviceCategoryId ? 'Updating...' : 'Creating...'}
              label={serviceCategoryId ? 'Update Service Category' : 'Create Service Category'}
              isSubmitting={isSubmitting}
              disabled={isSubmitting}
              className='w-fit'
            />
          </form>
        </Form>
      </div>
    </section>
  );
};

export default ServiceForm;
