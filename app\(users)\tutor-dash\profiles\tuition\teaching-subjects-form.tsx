'use client';

import { useForm } from 'react-hook-form';
import { useEffect } from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import { toast } from 'react-toastify';
import { Form } from '@/components/ui/form';
import { BookOpen } from 'lucide-react';
import { PrimaryInput, PrimarySelect, PrimaryMultiSelectForm, EducationalSearchInput, SubmitButton, CancelButton } from '@/components/forms';
import { PrimaryModalWithHeader } from '@/components/dashboard/shared/misc';
import { createTeachingSubjectSchema, CreateTeachingSubjectInput } from '@/validation/schemas/tutor/profiles/teaching-subjects.schema';
import { useCreateTeachingSubject, useUpdateTeachingSubject } from '@/hooks/tutor/tuition-profile.hooks';
import { IRateTypeMap, rateTypeOptions } from '@/validation/schemas/tutor/tuition.maps';
import { serviceCategoryMap, serviceCategoryOptions } from '@/validation/schemas/education/index.maps';
import { ITeachingSubjectDocument } from '@/server/services/tutor/tuition-profile.service';
import { createSelectOptions } from '@/validation/utils/form.utils';
import { useEducationalSearch } from '@/hooks/shared/useEducationalSearch';
import { createFormPopulator, EducationalFormPopulator } from '@/utils/form/educationalFormPopulator';

// Import hooks for dependent dropdowns
import { useGetAllBoards, useGetAllClasses, useGetAllSubjects } from '@/hooks/education/school.hooks';
import {
  useGetAllStreams,
  useGetAllDegreeLevels,
  useGetAllDegrees,
  useGetAllBranches,
  useGetAllCollegeSubjects,
} from '@/hooks/education/college.hooks';
import { useGetAllLanguageTypes, useGetAllLanguages } from '@/hooks/education/language.hooks';
import { useGetAllHobbyTypes, useGetAllHobbies } from '@/hooks/education/hobby.hooks';
import { useGetAllExamCategories, useGetAllExams, useGetAllExamSubjects } from '@/hooks/education/exam.hooks';
import { useGetAllCourseTypes, useGetAllCourses } from '@/hooks/education/course.hooks';

interface TeachingSubjectsFormProps {
  isOpen: boolean;
  onClose: () => void;
  subject?: ITeachingSubjectDocument;
  selectedCategory?: string;
}

const categoryVariantMap: Record<string, 'primary' | 'secondary' | 'green' | 'purple'> = {
  schools: 'primary',
  colleges: 'secondary',
  languages: 'green',
  hobbies: 'purple',
  exams: 'primary',
  it_courses: 'secondary',
};

const TeachingSubjectsForm = ({ isOpen, onClose, subject, selectedCategory }: TeachingSubjectsFormProps) => {
  if (!isOpen) return null;

  const createSubject = useCreateTeachingSubject();
  const updateSubject = useUpdateTeachingSubject();

  const isSubmitting = createSubject.isPending || updateSubject.isPending;
  const isEditing = !!subject;

  const {
    searchTerm,
    setSearchTerm,
    selectedMatch,
    setSelectedMatch,
    searchResults,
    isSearching,
    handleSelectMatch: handleSearchMatch,
    resetSearch,
  } = useEducationalSearch({
    onSelectMatch: (match) => {
      const formPopulator = createFormPopulator(form);
      formPopulator.populateTeachingSubjectForm(match);
    },
  });
  const form = useForm<CreateTeachingSubjectInput>({
    resolver: zodResolver(createTeachingSubjectSchema),
    mode: 'onChange',
    defaultValues: {
      serviceCategory: (subject?.serviceCategory || selectedCategory || 'schools') as any,
      amount: subject?.amount || 0,
      rateType: (subject?.rateType || 'perHour') as IRateTypeMap,
      // School fields
      boardId: subject?.boardId || '',
      classId: subject?.classId || '',
      subjectIds: subject?.subjectIds || [],
      allSubjects: subject?.allSubjects || false,
      // College fields
      streamId: subject?.streamId || '',
      degreeLevelId: subject?.degreeLevelId || '',
      degreeId: subject?.degreeId || '',
      branchId: subject?.branchId || '',
      collegeSubjectIds: subject?.collegeSubjectIds || [],
      // Language fields
      languageTypeId: subject?.languageTypeId || '',
      languageId: subject?.languageId || '',
      // Hobby fields
      hobbyTypeId: subject?.hobbyTypeId || '',
      hobbyId: subject?.hobbyId || '',
      // Exam fields
      examCategoryId: subject?.examCategoryId || '',
      examId: subject?.examId || '',
      examSubjectIds: subject?.examSubjectIds || [],
      // IT Course fields
      courseTypeId: subject?.courseTypeId || '',
      courseId: subject?.courseId || '',
    },
  });

  const watchedCategory = form.watch('serviceCategory');
  const watchedBoardId = form.watch('boardId');
  const watchedClassId = form.watch('classId');
  const watchedStreamId = form.watch('streamId');
  const watchedDegreeLevelId = form.watch('degreeLevelId');
  const watchedDegreeId = form.watch('degreeId');
  const watchedLanguageTypeId = form.watch('languageTypeId');
  const watchedHobbyTypeId = form.watch('hobbyTypeId');
  const watchedExamCategoryId = form.watch('examCategoryId');
  const watchedExamId = form.watch('examId');
  const watchedCourseTypeId = form.watch('courseTypeId');
  const watchedAllSubjects = form.watch('allSubjects');
  const watchedSubjectIds = form.watch('subjectIds');
  const watchedCollegeSubjectIds = form.watch('collegeSubjectIds');
  const watchedExamSubjectIds = form.watch('examSubjectIds');

  // Fetch data based on category - School data
  const { data: boardsData, isLoading: isLoadingBoards } = useGetAllBoards({ limit: 100 }, { enabled: watchedCategory === 'schools' });
  const { data: classesData, isLoading: isLoadingClasses } = useGetAllClasses({ board: watchedBoardId, limit: 100 }, { enabled: !!watchedBoardId });
  const { data: subjectsData, isLoading: isLoadingSubjects } = useGetAllSubjects(
    { class: watchedClassId, limit: 100 },
    { enabled: !!watchedClassId }
  );

  // College data
  const { data: streamsData, isLoading: isLoadingStreams } = useGetAllStreams({ limit: 100 }, { enabled: watchedCategory === 'colleges' });
  const { data: degreeLevelsData, isLoading: isLoadingDegreeLevels } = useGetAllDegreeLevels(
    { stream: watchedStreamId, limit: 100 },
    { enabled: !!watchedStreamId }
  );
  const { data: degreesData, isLoading: isLoadingDegrees } = useGetAllDegrees(
    { degreeLevel: watchedDegreeLevelId, limit: 100 },
    { enabled: !!watchedDegreeLevelId }
  );
  const { data: branchesData, isLoading: isLoadingBranches } = useGetAllBranches(
    { degree: watchedDegreeId, limit: 100 },
    { enabled: !!watchedDegreeId }
  );
  const { data: collegeSubjectsData, isLoading: isLoadingCollegeSubjects } = useGetAllCollegeSubjects(
    { degree: watchedDegreeId, limit: 100 },
    { enabled: !!watchedDegreeId }
  );

  // Language data
  const { data: languageTypesData, isLoading: isLoadingLanguageTypes } = useGetAllLanguageTypes(
    { limit: 100 },
    { enabled: watchedCategory === 'languages' }
  );
  const { data: languagesData, isLoading: isLoadingLanguages } = useGetAllLanguages(
    { languageType: watchedLanguageTypeId, limit: 100 },
    { enabled: !!watchedLanguageTypeId }
  );

  // Hobby data
  const { data: hobbyTypesData, isLoading: isLoadingHobbyTypes } = useGetAllHobbyTypes({ limit: 100 }, { enabled: watchedCategory === 'hobbies' });
  const { data: hobbiesData, isLoading: isLoadingHobbies } = useGetAllHobbies(
    { hobbyType: watchedHobbyTypeId, limit: 100 },
    { enabled: !!watchedHobbyTypeId }
  );

  // Exam data
  const { data: examCategoriesData, isLoading: isLoadingExamCategories } = useGetAllExamCategories(
    { limit: 100 },
    { enabled: watchedCategory === 'exams' }
  );
  const { data: examsData, isLoading: isLoadingExams } = useGetAllExams(
    { examCategory: watchedExamCategoryId, limit: 100 },
    { enabled: !!watchedExamCategoryId }
  );
  const { data: examSubjectsData, isLoading: isLoadingExamSubjects } = useGetAllExamSubjects(
    { exam: watchedExamId, limit: 100 },
    { enabled: !!watchedExamId }
  );

  // Course data
  const { data: courseTypesData, isLoading: isLoadingCourseTypes } = useGetAllCourseTypes(
    { limit: 100 },
    { enabled: watchedCategory === 'it_courses' }
  );
  const { data: coursesData, isLoading: isLoadingCourses } = useGetAllCourses(
    { courseType: watchedCourseTypeId, limit: 100 },
    { enabled: !!watchedCourseTypeId }
  );

  // Create select options using the utility function
  const boardOptions = createSelectOptions({ items: boardsData?.data?.boards, keyMapping: '_id', labelKey: 'name' });
  const classOptions = createSelectOptions({ items: classesData?.data?.classes, keyMapping: '_id', labelKey: 'name' });
  const subjectOptions = createSelectOptions({ items: subjectsData?.data?.subjects, keyMapping: '_id', labelKey: 'name' });

  const streamOptions = createSelectOptions({ items: streamsData?.data?.streams, keyMapping: '_id', labelKey: 'name' });
  const degreeLevelOptions = createSelectOptions({ items: degreeLevelsData?.data?.degreeLevels, keyMapping: '_id', labelKey: 'name' });
  const degreeOptions = createSelectOptions({ items: degreesData?.data?.degrees, keyMapping: '_id', labelKey: 'name' });
  const branchOptions = createSelectOptions({ items: branchesData?.data?.branches, keyMapping: '_id', labelKey: 'name' });
  const collegeSubjectOptions = createSelectOptions({ items: collegeSubjectsData?.data?.subjects, keyMapping: '_id', labelKey: 'name' });

  const languageTypeOptions = createSelectOptions({ items: languageTypesData?.data?.languageTypes, keyMapping: '_id', labelKey: 'name' });
  const languageOptions = createSelectOptions({ items: languagesData?.data?.languages, keyMapping: '_id', labelKey: 'name' });

  const hobbyTypeOptions = createSelectOptions({ items: hobbyTypesData?.data?.hobbyTypes, keyMapping: '_id', labelKey: 'name' });
  const hobbyOptions = createSelectOptions({ items: hobbiesData?.data?.hobbies, keyMapping: '_id', labelKey: 'name' });

  const examCategoryOptions = createSelectOptions({ items: examCategoriesData?.data?.examCategories, keyMapping: '_id', labelKey: 'name' });
  const examOptions = createSelectOptions({ items: examsData?.data?.exams, keyMapping: '_id', labelKey: 'name' });
  const examSubjectOptions = createSelectOptions({ items: examSubjectsData?.data?.examSubjects, keyMapping: '_id', labelKey: 'name' });

  const courseTypeOptions = createSelectOptions({ items: courseTypesData?.data?.courseTypes, keyMapping: '_id', labelKey: 'name' });
  const courseOptions = createSelectOptions({ items: coursesData?.data?.courses, keyMapping: '_id', labelKey: 'name' });

  // Handle allSubjects functionality
  useEffect(() => {
    if (watchedCategory === 'schools' && subjectOptions.length > 0) {
      const selectedSubjects = watchedSubjectIds || [];
      const allSelected = selectedSubjects.length === subjectOptions.length;

      if (allSelected !== watchedAllSubjects) {
        form.setValue('allSubjects', allSelected);
      }
    }
  }, [watchedSubjectIds, subjectOptions.length, watchedCategory, form, watchedAllSubjects]);

  useEffect(() => {
    if (!isOpen) {
      resetSearch();
    }
  }, [isOpen, resetSearch]);

  useEffect(() => {
    if (selectedMatch && selectedMatch.type !== watchedCategory) {
      setSelectedMatch(null);
    }
  }, [watchedCategory, selectedMatch]);

  useEffect(() => {
    if (watchedCategory === 'colleges' && collegeSubjectOptions.length > 0) {
      const selectedSubjects = watchedCollegeSubjectIds || [];
      const allSelected = selectedSubjects.length === collegeSubjectOptions.length;

      if (allSelected !== watchedAllSubjects) {
        form.setValue('allSubjects', allSelected);
      }
    }
  }, [watchedCollegeSubjectIds, collegeSubjectOptions.length, watchedCategory, form]);

  useEffect(() => {
    if (watchedCategory === 'exams' && examSubjectOptions.length > 0) {
      const selectedSubjects = watchedExamSubjectIds || [];
      const allSelected = selectedSubjects.length === examSubjectOptions.length;

      if (allSelected !== watchedAllSubjects) {
        form.setValue('allSubjects', allSelected);
      }
    }
  }, [watchedExamSubjectIds, examSubjectOptions.length, watchedCategory, form]);

  const onSubmit = async (values: CreateTeachingSubjectInput) => {
    try {
      const displayCategory = serviceCategoryMap[values.serviceCategory]?.label || 'Subject';

      if (isEditing && subject) {
        const result = await updateSubject.mutateAsync({ id: subject._id, data: values });
        if (!result.success) throw new Error(result.message || 'Operation failed');
        toast.success(`${displayCategory} updated successfully!`);
      } else {
        const result = await createSubject.mutateAsync(values);
        if (!result.success) throw new Error(result.message || 'Operation failed');
        toast.success(`${displayCategory} added successfully!`);
      }

      form.reset();
      onClose();
    } catch (error: any) {
      toast.error(error.message || 'Failed to save teaching subject');
      console.error(error);
    }
  };

  // Get title based on category
  const getCategoryTitle = () => {
    const baseTitle = isEditing ? 'Edit' : 'Add';
    const categoryLabel = serviceCategoryMap[watchedCategory]?.label || 'Subject';
    return `${baseTitle} ${categoryLabel}`;
  };

  return (
    <PrimaryModalWithHeader
      isOpen={isOpen}
      onClose={onClose}
      title={getCategoryTitle()}
      subtitle={`${isEditing ? 'Update' : 'Add'} what you teach and your rates`}
      icon={<BookOpen className='text-white' size={22} />}
      variant='primary'
      maxWidth='max-w-5xl'
    >
      <Form {...form} key={subject ? `edit-${subject._id}` : 'add-new'}>
        <form onSubmit={form.handleSubmit(onSubmit)} className='p-8'>
          <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
            {/* Service Category */}
            <PrimarySelect
              form={form}
              name='serviceCategory'
              label='Service Category'
              options={serviceCategoryOptions}
              placeholder='Select category'
              required
              variant='primary'
            />
            {/* Rate Type */}
            <PrimarySelect
              form={form}
              name='rateType'
              label='Rate Type'
              options={rateTypeOptions}
              placeholder='Select rate type'
              required
              variant='primary'
            />
            {/* Amount */}
            <PrimaryInput form={form} name='amount' label='Your Rate' placeholder='Enter your rate' type='number' required variant='primary' />
          </div>
          {/* Search Section */}
          <EducationalSearchInput
            searchTerm={searchTerm}
            setSearchTerm={setSearchTerm}
            selectedMatch={selectedMatch}
            setSelectedMatch={setSelectedMatch}
            searchResults={searchResults}
            isSearching={isSearching}
            onSelectMatch={handleSearchMatch}
            className='mt-8 mb-6'
          />
          {/* Dynamic fields based on category */}
          {watchedCategory === 'schools' && (
            <div className='space-y-6 mt-6'>
              <h3 className='text-lg font-medium text-gray-800 border-b pb-2'>School Details</h3>

              <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
                <PrimarySelect
                  form={form}
                  name='boardId'
                  label='Board'
                  options={boardOptions}
                  placeholder='Select board'
                  required
                  isLoading={isLoadingBoards}
                  variant={categoryVariantMap[watchedCategory]}
                  disabled={EducationalFormPopulator.isFieldDisabled('boardId', selectedMatch, 'schools')}
                />

                <PrimarySelect
                  form={form}
                  name='classId'
                  label='Class'
                  options={classOptions}
                  placeholder='Select class'
                  required
                  disabled={!watchedBoardId || EducationalFormPopulator.isFieldDisabled('classId', selectedMatch, 'schools')}
                  isLoading={isLoadingClasses}
                  variant={categoryVariantMap[watchedCategory]}
                />

                <div className='md:col-span-2'>
                  <PrimaryMultiSelectForm
                    form={form}
                    name='subjectIds'
                    label='Subjects'
                    options={subjectOptions}
                    placeholder='Select subjects'
                    required
                    disabled={!watchedClassId}
                    isLoading={isLoadingSubjects}
                    variant={categoryVariantMap[watchedCategory]}
                  />
                </div>
              </div>
            </div>
          )}
          {watchedCategory === 'colleges' && (
            <div className='space-y-6 mt-6'>
              <h3 className='text-lg font-medium text-gray-800 border-b pb-2'>College Details</h3>
              <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
                <PrimarySelect
                  form={form}
                  name='streamId'
                  label='Stream'
                  options={streamOptions}
                  placeholder='Select stream'
                  required
                  isLoading={isLoadingStreams}
                  variant={categoryVariantMap[watchedCategory]}
                />

                <PrimarySelect
                  form={form}
                  name='degreeLevelId'
                  label='Degree Level'
                  options={degreeLevelOptions}
                  placeholder='Select degree level'
                  required
                  disabled={!watchedStreamId}
                  isLoading={isLoadingDegreeLevels}
                  variant={categoryVariantMap[watchedCategory]}
                />

                <PrimarySelect
                  form={form}
                  name='degreeId'
                  label='Degree'
                  options={degreeOptions}
                  placeholder='Select degree'
                  required
                  disabled={!watchedDegreeLevelId}
                  isLoading={isLoadingDegrees}
                  variant={categoryVariantMap[watchedCategory]}
                />
                <PrimarySelect
                  form={form}
                  name='branchId'
                  label='Branch'
                  options={branchOptions}
                  placeholder='Select branch'
                  required
                  disabled={!watchedDegreeId}
                  isLoading={isLoadingBranches}
                  variant={categoryVariantMap[watchedCategory]}
                />

                <div className='md:col-span-2'>
                  <PrimaryMultiSelectForm
                    form={form}
                    name='collegeSubjectIds'
                    label='Subjects'
                    options={collegeSubjectOptions}
                    placeholder='Select subjects'
                    required
                    disabled={!watchedDegreeId}
                    isLoading={isLoadingCollegeSubjects}
                    variant={categoryVariantMap[watchedCategory]}
                  />
                </div>
              </div>
            </div>
          )}
          {watchedCategory === 'languages' && (
            <div className='space-y-6 mt-6'>
              <h3 className='text-lg font-medium text-gray-800 border-b pb-2'>Language Details</h3>

              <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
                <PrimarySelect
                  form={form}
                  name='languageTypeId'
                  label='Language Type'
                  options={languageTypeOptions}
                  placeholder='Select language type'
                  required
                  isLoading={isLoadingLanguageTypes}
                  variant={categoryVariantMap[watchedCategory]}
                  disabled={selectedMatch?.type === 'languages' && !!selectedMatch.details?.languageType}
                />

                <PrimarySelect
                  form={form}
                  name='languageId'
                  label='Language'
                  options={languageOptions}
                  placeholder='Select language'
                  required
                  disabled={!watchedLanguageTypeId || (selectedMatch?.type === 'languages' && !!selectedMatch.details?.language)}
                  isLoading={isLoadingLanguages}
                  variant={categoryVariantMap[watchedCategory]}
                />
              </div>
            </div>
          )}
          {watchedCategory === 'hobbies' && (
            <div className='space-y-6 mt-6'>
              <h3 className='text-lg font-medium text-gray-800 border-b pb-2'>Hobby Details</h3>

              <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
                <PrimarySelect
                  form={form}
                  name='hobbyTypeId'
                  label='Hobby Type'
                  options={hobbyTypeOptions}
                  placeholder='Select hobby type'
                  required
                  isLoading={isLoadingHobbyTypes}
                  variant={categoryVariantMap[watchedCategory]}
                  disabled={selectedMatch?.type === 'hobbies' && !!selectedMatch.details?.hobbyType}
                />

                <PrimarySelect
                  form={form}
                  name='hobbyId'
                  label='Hobby'
                  options={hobbyOptions}
                  placeholder='Select hobby'
                  required
                  disabled={!watchedHobbyTypeId || (selectedMatch?.type === 'hobbies' && !!selectedMatch.details?.hobby)}
                  isLoading={isLoadingHobbies}
                  variant={categoryVariantMap[watchedCategory]}
                />
              </div>
            </div>
          )}
          {watchedCategory === 'exams' && (
            <div className='space-y-6 mt-6'>
              <h3 className='text-lg font-medium text-gray-800 border-b pb-2'>Exam Details</h3>

              <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
                <PrimarySelect
                  form={form}
                  name='examCategoryId'
                  label='Exam Category'
                  options={examCategoryOptions}
                  placeholder='Select exam category'
                  required
                  isLoading={isLoadingExamCategories}
                  variant={categoryVariantMap[watchedCategory]}
                  disabled={selectedMatch?.type === 'exams' && !!selectedMatch.details?.examCategory}
                />

                <PrimarySelect
                  form={form}
                  name='examId'
                  label='Exam'
                  options={examOptions}
                  placeholder='Select exam'
                  required
                  disabled={!watchedExamCategoryId || (selectedMatch?.type === 'exams' && !!selectedMatch.details?.exam)}
                  isLoading={isLoadingExams}
                  variant={categoryVariantMap[watchedCategory]}
                />

                <div className='md:col-span-2'>
                  <PrimaryMultiSelectForm
                    form={form}
                    name='examSubjectIds'
                    label='Exam Subjects'
                    options={examSubjectOptions}
                    placeholder='Select exam subjects'
                    required
                    disabled={!watchedExamId}
                    isLoading={isLoadingExamSubjects}
                    variant={categoryVariantMap[watchedCategory]}
                  />
                </div>
              </div>
            </div>
          )}
          {watchedCategory === 'it_courses' && (
            <div className='space-y-6 mt-6'>
              <h3 className='text-lg font-medium text-gray-800 border-b pb-2'>IT Course Details</h3>

              <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
                <PrimarySelect
                  form={form}
                  name='courseTypeId'
                  label='Course Type'
                  options={courseTypeOptions}
                  placeholder='Select course type'
                  required
                  isLoading={isLoadingCourseTypes}
                  variant='secondary'
                  disabled={selectedMatch?.type === 'it_courses' && !!selectedMatch.details?.courseCategory}
                />

                <PrimarySelect
                  form={form}
                  name='courseId'
                  label='Course'
                  options={courseOptions}
                  placeholder='Select course'
                  required
                  disabled={!watchedCourseTypeId || (selectedMatch?.type === 'it_courses' && !!selectedMatch.details?.course)}
                  isLoading={isLoadingCourses}
                  variant='secondary'
                />
              </div>
            </div>
          )}
          {(!watchedCategory || !['schools', 'colleges', 'languages', 'hobbies', 'exams', 'it_courses'].includes(watchedCategory)) && (
            <div className='space-y-6 mt-6 p-8 rounded-md bg-gray-50 border border-gray-100 text-center'>
              <div className='flex flex-col items-center justify-center py-6'>
                <BookOpen className='h-12 w-12 text-gray-400 mb-4' />
                <h3 className='text-lg font-medium text-gray-700 mb-2'>Select a Service Category</h3>
                <p className='text-gray-500 max-w-md'>
                  Choose a service category from the dropdown above to start adding the subjects you teach and your rates.
                </p>
              </div>
            </div>
          )}
          <div className='flex gap-4 justify-end pt-6 mt-6 border-t border-gray-100'>
            <CancelButton onClose={onClose} />
            <SubmitButton
              isSubmitting={isSubmitting}
              label={isEditing ? 'Update Subject' : 'Add Subject'}
              submittingLabel={isEditing ? 'Updating...' : 'Adding...'}
              variant='primary'
            />
          </div>
        </form>
      </Form>
    </PrimaryModalWithHeader>
  );
};

export default TeachingSubjectsForm;
