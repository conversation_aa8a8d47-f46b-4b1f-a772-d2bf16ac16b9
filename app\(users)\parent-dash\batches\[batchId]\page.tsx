import { KeyValueDisplay } from '@/components/dashboard/shared/misc';
import { BookOpen, Calendar, CheckCircle, Clipboard, FileText, Users, Video } from 'lucide-react';
import Image from 'next/image';

const TutorSingleBatchPage = () => {
  return (
    <section className='flex flex-col lg:flex-row gap-8 justify-start items-start'>
      {/* Left Column */}
      <div className='w-full lg:w-1/4 flex flex-col md:flex-row lg:flex-col items-start gap-8'>
        {/* Contact Buttons */}
        <div className='space-y-4 bg-white rounded-3xl p-6 w-full relative'>
          <div className='absolute top-0 left-0 transform bg-gradient-1 uppercase tracking-wider text-white text-xs font-semibold px-5 py-1.5 rounded-tl-3xl rounded-br-3xl'>
            Tutor
          </div>
          <div className='flex justify-center'>
            <Image
              alt='avatar'
              src='/temp/avatar.jpg'
              height={350}
              width={350}
              className='max-w-full md:max-w-[260px] lg:max-w-60 h-auto object-contain object-center rounded-3xl'
            />
          </div>
          <a href='mailto:<EMAIL>' className='btn-default__outline-sm w-full justify-center !rounded-3xl'>
            Email Now
          </a>
          <a href='tel:+911234567890' className='btn-default-sm w-full justify-center !rounded-3xl'>
            Call Now
          </a>
        </div>
        {/* User Info */}
        <div className='bg-white rounded-3xl p-6 w-full'>
          <h2 className='text-xl font-semibold mb-4'>Class Room</h2>
          <div className='space-y-2'>
            {[
              { label: 'Calendar', icon: Calendar },
              { label: 'Attendance', icon: Users },
              { label: 'Live Class', icon: Video },
              { label: 'Videos', icon: FileText },
              { label: 'Materials', icon: BookOpen },
              { label: 'Exams', icon: Clipboard },
              { label: 'Results', icon: CheckCircle },
            ].map((item, index) => (
              <div key={index} className='py-2 border-b flex justify-between items-center text-gray-600'>
                <span>{item.label}</span>
                {<item.icon size={20} strokeWidth={1.5} />}
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Right Column */}
      <div className='w-full lg:w-3/4 flex flex-col items-start gap-8'>
        {/* Batch Information */}
        <div className='bg-white rounded-3xl p-6 w-full relative'>
          <div className='flex items-start justify-between'>
            <h2 className='text-xl font-semibold mb-4'>Batch Information</h2>
          </div>

          <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
            <KeyValueDisplay label='Student Name' value='Divya' />
            <KeyValueDisplay label='Contact Number' value='+911234567890' />
            <KeyValueDisplay label='Faculty Name' value='Pramod' />
            <KeyValueDisplay label='Batch ID' value='ARSHKB24' />
            <KeyValueDisplay label='Section' value='Schools' />
            <KeyValueDisplay label='Board' value='CBSE' />
            <KeyValueDisplay label='Class' value='Class VII' />
            <KeyValueDisplay label='Subjects' value='Hindi, Sanskrit' />
            <KeyValueDisplay label='Batch Start Date' value='08-08-2022' />
            <KeyValueDisplay label='Batch End Date' value='08-08-2022' />
          </div>
        </div>
      </div>
    </section>
  );
};

export default TutorSingleBatchPage;
