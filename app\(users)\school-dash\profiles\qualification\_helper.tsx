'use client';

import { useState } from 'react';
import TutorQualificationForm from '@/components/dashboard/tutor-dash/forms/TutorQualificationForm';
import { AlertDialog, AlertDialogCancel, AlertDialogContent, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { Clock, Home, MapPin, School, X } from 'lucide-react';
import { SimpleTable } from '@/components/dashboard/tutor-dash/misc';
import { TabsFilterWithBadge, TabsMobileFilterWithBadge } from '@/components/dashboard/shared/misc/TabsFilterWithBadge';
import TutorDashActionLinks from '@/components/dashboard/tutor-dash/misc/TutorDashActionLinks';

const TutorQualificationHelper = () => {
  const [selectedMainTab, setSelectedMainTab] = useState('undergraduate');
  const [selectedSubTab, setSelectedSubTab] = useState('');

  const mainTabCounts = { undergraduate: 6, graduate: 6, other: 1 };
  const mainTabLabels = {
    large: {
      undergraduate: 'Undergraduate',
      graduate: 'Graduate',
      other: 'Any Other',
    },
    mobile: {
      undergraduate: 'UG',
      graduate: 'Graduate',
      other: 'Other',
    },
  };

  return (
    <div className='bg-white p-6 rounded-3xl relative min-h-[650px] grid grid-cols-1 md:grid-cols-4 items-start gap-6 lg:gap-12'>
      <div className='flex flex-col gap-6 max-md:hidden max-lg:flex-row max-lg:w-full'>
        <TabsFilterWithBadge
          selectedTab={selectedMainTab}
          setSelectedTab={setSelectedMainTab}
          tabCounts={mainTabCounts}
          labels={mainTabLabels.large}
        />
      </div>
      <div className='md:col-span-3'>
        {selectedMainTab === 'undergraduate' && (
          <div>
            {/* Record Modal */}
            <div className='flex justify-end items-start mb-4'>
              <AlertDialog>
                <div className='flex justify-between items-center w-full'>
                  <h2 className='text-base font-semibold'>Undergraduate Qualification Information</h2>
                  <AlertDialogTrigger className='btn-default-sm'>Add Record</AlertDialogTrigger>
                </div>
                <AlertDialogContent className='lg:w-[700px] lg:max-w-none'>
                  <TutorQualificationForm isUndergraduate />
                  <AlertDialogCancel className='border-0 absolute rounded-none rounded-tr-md right-0 top-0 bg-primaryColor-50 hover:bg-primaryColor-100 flex-shrink-0 p-2 tracking-wider cursor-pointer group overflow-hidden'>
                    <p className='text-primaryColor'>
                      <X />
                    </p>
                  </AlertDialogCancel>
                </AlertDialogContent>
              </AlertDialog>
            </div>
            {/* Record Table */}
            <SimpleTable
              headers={['Class', 'Branch', 'Start Year', 'End Year/Expected Year', 'Percentage', 'Action']}
              rows={[
                ['10th', 'Science', 2015, 2018, '85%', <TutorDashActionLinks basePath='' id='' edit delete />],
                ['11th', 'Commerce', 2016, 2019, '88%', <TutorDashActionLinks basePath='' id='' edit delete />],
                ['12th', 'Engineering', 2014, 2018, '90%', <TutorDashActionLinks basePath='' id='' edit delete />],
                ['12th', 'Arts', 2013, 2017, '82%', <TutorDashActionLinks basePath='' id='' edit delete />],
                ['11th', 'Education', 2012, 2016, '87%', <TutorDashActionLinks basePath='' id='' edit delete />],
                ['10th', 'Science', 2015, 2018, '-', <TutorDashActionLinks basePath='' id='' edit delete />],
              ]}
            />
          </div>
        )}
        {selectedMainTab === 'graduate' && (
          <div>
            {/* Record Modal */}
            <div className='flex justify-end items-start mb-4'>
              <AlertDialog>
                <div className='flex justify-between items-center w-full'>
                  <h2 className='text-base font-semibold'>Graduate Qualification Information</h2>
                  <AlertDialogTrigger className='btn-default-sm'>Add Record</AlertDialogTrigger>
                </div>
                <AlertDialogContent className='lg:w-[700px] lg:max-w-none'>
                  <TutorQualificationForm />
                  <AlertDialogCancel className='border-0 absolute rounded-none rounded-tr-md right-0 top-0 bg-primaryColor-50 hover:bg-primaryColor-100 flex-shrink-0 p-2 tracking-wider cursor-pointer group overflow-hidden'>
                    <p className='text-primaryColor'>
                      <X />
                    </p>
                  </AlertDialogCancel>
                </AlertDialogContent>
              </AlertDialog>
            </div>
            {/* Record Table */}
            <SimpleTable
              headers={['Degree', 'Branch', 'Start Year', 'End Year/Expected Year', 'Percentage', 'Action']}
              rows={[
                ['B.Sc', 'Science', 2015, 2018, '85%', <TutorDashActionLinks basePath='' id='' edit delete />],
                ['B.Com', 'Commerce', 2016, 2019, '88%', <TutorDashActionLinks basePath='' id='' edit delete />],
                ['B.Tech', 'Engineering', 2014, 2018, '90%', <TutorDashActionLinks basePath='' id='' edit delete />],
                ['B.A', 'Arts', 2013, 2017, '82%', <TutorDashActionLinks basePath='' id='' edit delete />],
                ['B.Ed', 'Education', 2012, 2016, '87%', <TutorDashActionLinks basePath='' id='' edit delete />],
                ['M.Sc', 'Science', 2015, 2017, '-', <TutorDashActionLinks basePath='' id='' edit delete />],
              ]}
            />
          </div>
        )}
        {selectedMainTab === 'other' && <div>Content for any other qualifications.</div>}
      </div>
      <TabsMobileFilterWithBadge
        selectedTab={selectedMainTab}
        setSelectedTab={setSelectedMainTab}
        tabCounts={mainTabCounts}
        showNumbers={false}
        labels={mainTabLabels.mobile}
      />
    </div>
  );
};

export default TutorQualificationHelper;
