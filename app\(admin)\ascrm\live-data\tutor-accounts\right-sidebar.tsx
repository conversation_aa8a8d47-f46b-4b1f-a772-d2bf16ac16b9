import { AdminDashLink } from '@/components/dashboard/ascrm/misc';
import { CircularProgressBar } from '@/components/dashboard/shared/misc';
import { Mail, Phone, MapPin } from 'lucide-react';
import Image from 'next/image';
import { FaWhatsapp } from 'react-icons/fa';

export const RightSideBar = () => (
  <div>
    <div className='bg-gradient-to-r from-primaryColor-400 to-primaryColor-600 p-4 text-white'>
      <div className='flex items-center gap-x-4'>
        <div className='size-16 bg-white rounded-full overflow-hidden'>
          <Image src='/icons/avatar-male.webp' alt='John <PERSON>' width={80} height={80} className='object-cover' />
        </div>
        <div>
          <h2 className='text-2xl font-bold'>John <PERSON></h2>
          <p className='text-primaryColor-100'>Tutor - #123</p>
        </div>
      </div>
    </div>

    <div className='p-6 space-y-6'>
      <section>
        <h3 className='text-lg font-semibold text-gray-900 mb-4'>Contact Info</h3>
        <div className='space-y-1'>
          <ContactItem icon={<Mail size={18} />} text='<EMAIL>' />
          <ContactItem icon={<Phone size={18} />} text='+****************' />
          <ContactItem icon={<FaWhatsapp size={18} />} text='+****************' />
          <ContactItem icon={<MapPin size={18} />} text='123 Tutor Street, Eduville' />
        </div>
      </section>

      <section>
        <h3 className='text-lg font-semibold text-gray-900 mb-4'>Progress</h3>
        <div className='grid grid-cols-2 gap-4'>
          <CircularProgressBar percentage={70} label='KYC' colorClass='text-yellow-500' />
          <CircularProgressBar percentage={60} label='Verification' colorClass='text-primaryColor-500' />
          <CircularProgressBar percentage={80} label='Profile' colorClass='text-green-500' />
          <CircularProgressBar percentage={90} label='Account Status' colorClass='text-red-500' />
        </div>
      </section>

      <AdminDashLink href='/live-data/tutor-accounts/1' className='btn-default__outline-sm w-full text-center py-2 rounded-full'>
        View Complete Details
      </AdminDashLink>
    </div>
  </div>
);

const ContactItem = ({ icon, text }: { icon: React.ReactNode; text: string }) => (
  <div className='flex items-center gap-3 p-2 rounded-lg hover:bg-primaryColor-50 transition-colors duration-200'>
    <div className='flex items-center justify-center size-8 rounded-full bg-primaryColor-100 text-primaryColor-600'>{icon}</div>
    <p className='text-sm font-medium text-gray-700 hover:text-primaryColor-600 transition-colors duration-200'>{text}</p>
  </div>
);
