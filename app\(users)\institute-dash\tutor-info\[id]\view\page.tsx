import Image from 'next/image';
import { KeyValueDisplay } from '@/components/dashboard/shared/misc';

const TutorTuitionLeadViewPage = () => {
  return (
    <section className='flex gap-8 justify-start items-start'>
      <div className='w-3/4'>
        <TeacherProfiles />
      </div>

      {/* Right Column */}
      <div className='w-1/4 flex flex-col items-start gap-8'>
        {/* User Info */}
        <div className='bg-white rounded-3xl p-6 w-full'>
          <h2 className='text-xl font-semibold mb-4'>About #14571</h2>
          <div className='grid grid-cols-1 gap-4'>
            <KeyValueDisplay label='Name' value='Gaurav' />
            <KeyValueDisplay label='Enquiry For' value='CBSE/Class 10' />
            <KeyValueDisplay label='Location' value='India' />
            <KeyValueDisplay label='Gender' value='Male' />
            <KeyValueDisplay label='Mode of Delivery' value='Online' />
            <KeyValueDisplay label='Subjects' value='A,B,C' />
            <KeyValueDisplay label='Distance' value='Approx. 5 km' />
          </div>
        </div>
      </div>
    </section>
  );
};

import { Briefcase, MapPin, Star, ScrollText, ScanEye, IndianRupee } from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { ParentDashLink } from '@/components/dashboard/parent-dash/misc';

const teachers = [
  {
    id: 2,
    name: 'Pooja Singh',
    subjects: 'Physics, Chemistry, Mathematics',
    rating: 4.7,
    reviews: 20,
    experience: '7 years',
    salary: '₹60,000/month',
    location: 'Mumbai',
    description: 'Dedicated Physics and Chemistry teacher with extensive experience in preparing students for competitive exams.',
    tags: ['Physics', 'Chemistry', 'Mathematics'],
    date: '2023-06-20',
    avatar: '/temp/avatar.jpg',
    qualifications: ['B.Sc', 'M.Sc'],
  },
  {
    id: 3,
    name: 'Amit Sharma',
    subjects: 'Biology, Computer Science',
    rating: 4.2,
    reviews: 18,
    experience: '6 years',
    salary: '₹55,000/month',
    location: 'Bangalore',
    description: 'Passionate Biology and Computer Science teacher with a focus on practical learning and student engagement.',
    tags: ['Biology', 'Computer Science'],
    date: '2023-06-18',
    avatar: '/temp/avatar.jpg',
  },
  {
    id: 4,
    name: 'Neha Patel',
    subjects: 'Mathematics, Biology',
    rating: 4.6,
    reviews: 15,
    experience: '8 years',
    salary: '₹70,000/month',
    location: 'Ahmedabad',
    description: 'Expert Mathematics and Biology teacher with a knack for making complex concepts easy to understand.',
    tags: ['Mathematics', 'Biology'],
    date: '2023-06-15',
    avatar: '/temp/avatar.jpg',
  },
  {
    id: 5,
    name: 'Mehul Verma',
    subjects: 'English, History',
    rating: 4.4,
    reviews: 10,
    experience: '4 years',
    salary: '₹45,000/month',
    location: 'Pune',
    description: 'Enthusiastic English and History teacher with a love for storytelling and student interaction.',
    tags: ['English', 'History'],
    date: '2023-06-10',
    avatar: '/temp/avatar.jpg',
  },
];

const TeacherCard = ({ teacher }: { teacher: any }) => (
  <div className='bg-white p-6 rounded-3xl grid grid-cols-1 md:grid-cols-5 gap-2'>
    <div className='md:col-span-4'>
      <div className='flex justify-between'>
        <div className='flex items-center gap-2'>
          <h3 className='text-lg font-semibold'>{teacher.name}</h3>
          <div className='flex items-center'>
            {Array.from({ length: 5 }, (_, index) => (
              <Star size={18} key={index} fill='#f8910c' strokeWidth={0} />
            ))}
          </div>
        </div>

        <Image src={teacher.avatar} alt={`${teacher.name} avatar`} className='size-12 md:hidden' height={40} width={40} />
      </div>
      <div className='flex items-center text-gray-600 font-medium text-sm mb-2'>{teacher.qualifications?.join(', ')} </div>
      <div className='flex gap-2 items-center text-sm flex-wrap'>
        <p className='flex gap-1 items-start md:items-center'>
          <Briefcase size={16} className='shrink-0' /> <span>{teacher.experience}</span>
        </p>
        <span className='text-gray-400'>|</span>
        <p className='flex gap-1 items-start md:items-center'>
          <MapPin size={16} className='shrink-0' /> <span>{teacher.location}</span>
        </p>
      </div>
      <div className='text-gray-500 my-2'>
        <p className='flex gap-2 items-start md:items-center'>
          <ScrollText size={18} className='shrink-0' />
          <span className='line-clamp-2 md:line-clamp-1'>{teacher.description}</span>
        </p>
      </div>
      <div className='flex flex-wrap gap-2 mb-4'>
        {teacher.tags.map((tag: any, index: any) => (
          <span key={index} className='bg-gray-100 text-gray-700 px-4 py-1 rounded capitalize'>
            {tag}
          </span>
        ))}
      </div>
      <p className='font-medium text-xs hidden md:block'>{formatDistanceToNow(new Date(teacher.date))} ago</p>
    </div>
    <div className='flex justify-between md:flex-col md:justify-center items-center md:col-span-1 gap-4 flex-wrap'>
      <p className='font-medium text-xs md:hidden'>{formatDistanceToNow(new Date(teacher.date))} ago</p>
      <Image
        src={teacher.avatar}
        alt={`${teacher.name} avatar`}
        className='size-24 hidden md:block rounded-full ring-4 ring-offset-4 ring-primaryColor'
        height={100}
        width={100}
      />
      <ParentDashLink href={`/teachers/${teacher.id}`} className='btn-default__outline-sm gap-2 mt-auto flex items-center'>
        <ScanEye size={18} /> <span>Contact No.</span>
      </ParentDashLink>
    </div>
  </div>
);

const TeacherProfiles = () => (
  <section className='space-y-6'>
    {teachers.map((teacher) => (
      <TeacherCard key={teacher.id} teacher={teacher} />
    ))}
  </section>
);

export default TutorTuitionLeadViewPage;
