'use client';

import { useState } from 'react';
import { MobileTabsFilter, TabsFilter } from '@/components/dashboard/shared/misc';
import TutorDashActionLinks from '@/components/dashboard/tutor-dash/misc/TutorDashActionLinks';
import { MobileLeadItemCard, SimpleTable } from '@/components/dashboard/tutor-dash/misc';
import { leads } from '@/constants';
import { BookA } from 'lucide-react';

const TuitionLeadsHelper = () => {
  const [selectedTab, setSelectedTab] = useState('all');

  const filteredLeads = leads.filter((lead) => {
    return selectedTab === 'all' || lead.status === selectedTab;
  });

  const tabCounts = {
    all: leads.length,
    filter: leads.filter((lead) => lead.status === 'filter').length,
    approached: leads.filter((lead) => lead.status === 'approached').length,
    favorite: leads.filter((lead) => lead.status === 'favorite').length,
    contacted: leads.filter((lead) => lead.status === 'contacted').length,
    other: leads.filter((lead) => lead.status === 'other').length,
  };

  const headers = ['SN', 'Name', 'Enquiry For', 'Location', 'Distance', 'Action'];

  const rows = filteredLeads.map((lead, index) => [
    index + 1,
    <div>
      <p>{lead.name}</p>
      <p>{lead.id}000</p>
    </div>,
    <div>
      <p>{lead.enquiryFor}</p>
      <p>{['B.Sc', 'M.Sc'].includes(lead.enquiryFor) ? 'Computer' : 'CBSE'}</p>
    </div>,
    lead.address,
    lead.distance,
    <TutorDashActionLinks basePath='leads' id={lead.id.toString()} view />,
  ]);

  return (
    <div className='bg-white p-6 rounded-3xl relative min-h-[650px] flex flex-col lg:flex-row items-start justify-start gap-4'>
      <div className='lg:w-1/4 w-full'>
        <TabsFilter label='Leads' selectedTab={selectedTab} setSelectedTab={setSelectedTab} tabCounts={tabCounts} />
      </div>
      <div className='lg:w-3/4 w-full'>
        <h2 className='text-lg font-semibold mb-4 capitalize'>Recent {selectedTab} Leads For You</h2>
        <div className='flex flex-col gap-4 md:hidden'>
          {filteredLeads.map((lead, index) => (
            <MobileLeadItemCard key={index} lead={lead} />
          ))}
        </div>
        <div className='max-md:hidden'>
          <SimpleTable headers={headers} rows={rows} />
        </div>
        <MobileTabsFilter
          label='Leads'
          icon={BookA}
          mobileTabs={['all', 'filter', 'approached', 'contacted']}
          selectedTab={selectedTab}
          setSelectedTab={setSelectedTab}
          tabCounts={tabCounts}
        />
      </div>
    </div>
  );
};

export default TuitionLeadsHelper;
