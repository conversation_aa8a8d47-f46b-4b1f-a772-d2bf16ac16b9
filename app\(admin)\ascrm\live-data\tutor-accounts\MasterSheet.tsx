'use client';

import type React from 'react';
import { useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X } from 'lucide-react';
import { cn } from '@/lib/utils';

type SheetSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl';

interface SheetState {
  isOpen: boolean;
  open: () => void;
  close: () => void;
  toggle: () => void;
}

interface MasterSheetProps {
  sheet: SheetState;
  isOpen?: boolean;
  onClose?: () => void;
  title: string;
  subtitle?: string;
  size?: SheetSize;
  children: React.ReactNode;
}

const sizeClasses: Record<SheetSize, string> = {
  xs: 'max-w-[500px]',
  sm: 'max-w-[650px]',
  md: 'max-w-[900px]',
  lg: 'max-w-[1100px]',
  xl: 'max-w-[1400px]',
  '2xl': 'max-w-[1600px]',
};

const MasterSheet: React.FC<MasterSheetProps> = ({ sheet, isOpen: isOpenProp, onClose: onCloseProp, title, subtitle, size = 'sm', children }) => {
  const isOpen = isOpenProp !== undefined ? isOpenProp : sheet.isOpen;
  const onClose = onCloseProp !== undefined ? onCloseProp : sheet.close;

  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') onClose();
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, onClose]);

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          <motion.div
            className='fixed inset-0 bg-black/20 z-50'
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={onClose}
          />
          <motion.div
            className={cn('fixed right-0 top-0 h-full w-full bg-white shadow-xl z-50', sizeClasses[size])}
            initial={{ x: '100%' }}
            animate={{ x: 0 }}
            exit={{ x: '100%' }}
            transition={{ type: 'spring', damping: 30, stiffness: 300 }}
          >
            <div className='h-full flex flex-col'>
              <div className='sticky top-0 bg-white/95 backdrop-blur-sm z-10'>
                <div className='px-6 py-5 border-b border-gray-100 flex items-center justify-between'>
                  <div className='flex-1 space-y-1'>
                    <h2 className='text-2xl font-semibold text-gray-800 tracking-tight'>{title}</h2>
                    {subtitle && <p className='text-sm text-gray-500 font-medium'>{subtitle}</p>}
                  </div>
                  <button
                    onClick={onClose}
                    className='ml-4 p-2.5 rounded-full transition-all duration-300
                    bg-gray-700 text-white hover:bg-gray-800
                    hover:text-white hover:scale-105 active:scale-95
                    focus:outline-none focus:ring-2 focus:ring-black/20
                    shadow-sm hover:shadow-md'
                    aria-label='Close panel'
                  >
                    <X size={18} strokeWidth={2.5} />
                  </button>
                </div>
              </div>

              {children && <div className='flex-1 overflow-y-auto'>{children}</div>}
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
};

export default MasterSheet;
