'use client';

import React, { useState } from 'react';
import { Switch } from '@/components/ui/switch';
import { toast } from 'react-toastify';
import { cn } from '@/lib/utils';
import { useMutation } from '@tanstack/react-query';
import { queryClient } from '@/lib/react-query/queryClient';

interface StatusToggleProps {
  id: string;
  isActive: boolean;
  updateFn: (id: string, data: { isActive: boolean }) => Promise<any>;
  queryKey: string | string[] | (string | { [key: string]: any })[];
  entityName?: string;
  disabled?: boolean;
  className?: string;
  showLabel?: boolean;
  labelPosition?: 'left' | 'right';
  showToast?: boolean;
  onSuccess?: (data: any, newStatus: boolean) => void;
  onError?: (error: Error) => void;
}

const StatusToggle: React.FC<StatusToggleProps> = ({
  id,
  isActive,
  updateFn,
  queryKey,
  entityName = 'Item',
  disabled = false,
  className = '',
  showLabel = false,
  labelPosition = 'right',
  showToast = true,
  onSuccess,
  onError,
}) => {
  const [isUpdating, setIsUpdating] = useState(false);

  const mutation = useMutation({
    mutationFn: (newStatus: boolean) => updateFn(id, { isActive: newStatus }),
    onSuccess: (data, newStatus) => {
      if (Array.isArray(queryKey)) {
        queryKey.forEach((key) => {
          if (Array.isArray(key)) {
            queryClient.invalidateQueries({ queryKey: key });
          } else {
            queryClient.invalidateQueries({ queryKey: [key] });
          }
        });
      } else {
        queryClient.invalidateQueries({ queryKey: [queryKey] });
      }

      if (showToast) {
        toast.success(`${entityName} ${newStatus ? 'activated' : 'deactivated'} successfully`);
      }

      if (onSuccess) {
        onSuccess(data, newStatus);
      }

      setIsUpdating(false);
    },
    onError: (error: any) => {
      if (showToast) {
        toast.error(error.message || `Failed to update ${entityName.toLowerCase()} status`);
      }

      if (onError) {
        onError(error);
      }

      setIsUpdating(false);
    },
  });

  const handleToggle = async () => {
    if (disabled || isUpdating) return;

    setIsUpdating(true);
    mutation.mutate(!isActive);
  };

  const label = isActive ? 'Active' : 'Inactive';
  const labelClasses = 'text-sm font-medium';

  return (
    <div className={`flex items-center gap-2 ${isUpdating ? 'opacity-70' : ''}`}>
      {showLabel && labelPosition === 'left' && <span className={labelClasses}>{label}</span>}

      <Switch
        checked={isActive}
        onCheckedChange={handleToggle}
        disabled={disabled || isUpdating}
        className={cn('data-[state=checked]:bg-primaryColor-500', className)}
      />

      {showLabel && labelPosition === 'right' && <span className={labelClasses}>{label}</span>}
    </div>
  );
};

export default StatusToggle;
