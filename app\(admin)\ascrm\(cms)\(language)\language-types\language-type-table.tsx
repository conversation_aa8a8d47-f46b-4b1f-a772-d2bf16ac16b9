'use client';

import { useState, useEffect, useCallback } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { AdminDashActionLinks, HeadingBar, LimitFilter, MasterFilter, MasterTable, SectionWrapper } from '@/components/dashboard/ascrm/misc';
import { ErrorState } from '@/components/dashboard/ascrm/misc/StateUI';
import { filterTypeMaps, FilterTypeKey } from '@/constants';
import { FilterValues, FilterOption } from '@/types/api';
import { useGetAllLanguageTypes, useDeleteLanguageType, useGetLanguageTypeById } from '@/hooks/education/language.hooks';
import { AdminViewButton, PrimaryFormModal, useModal, StatusToggle } from '@/components/dashboard/shared/misc';
import LanguageTypeForm from './language-type-form';
import { languageTypeService } from '@/server/services/education/language.service';
import { QUERY_KEYS } from '@/lib/react-query/queryKeys';

const LanguageTypeTable: React.FC = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const addLanguageTypeModal = useModal();
  const editLanguageTypeModal = useModal();

  const [selectedLanguageTypeId, setSelectedLanguageTypeId] = useState<string | null>(null);
  const [filters, setFilters] = useState<FilterValues>({});

  const updateFiltersFromParams = useCallback(() => {
    const initialFilters: FilterValues = {};
    searchParams.forEach((value, key) => {
      if (key !== 'page') {
        initialFilters[key] = value;
      }
    });
    setFilters(initialFilters);
  }, [searchParams]);

  useEffect(() => {
    updateFiltersFromParams();
  }, [updateFiltersFromParams]);

  const page = Number(searchParams.get('page')) || 1;

  const queryParams = { page, ...filters };

  const { data: languageTypesResponse, isLoading, error } = useGetAllLanguageTypes(queryParams);
  const deleteItem = useDeleteLanguageType();
  const {
    data: selectedLanguageTypeData,
    isLoading: isLoadingSelectedLanguageType,
    error: selectedLanguageTypeError,
  } = useGetLanguageTypeById(selectedLanguageTypeId || '');

  const languageTypes = languageTypesResponse?.data?.languageTypes || [];
  const pagination = languageTypesResponse?.data?.pagination;

  const handleEditLanguageType = (id: string) => {
    setSelectedLanguageTypeId(id);
    editLanguageTypeModal.open();
  };

  if (error) {
    return <ErrorState message={error?.message || 'An unexpected error occurred.'} />;
  }

  const headers = ['Name', 'Status', 'Actions'];

  const rows = languageTypes.map((languageType) => {
    return {
      id: languageType._id,
      values: [
        <p className='font-medium'>{languageType.name}</p>,
        <StatusToggle
          id={languageType._id}
          isActive={languageType.isActive}
          updateFn={languageTypeService.updateLanguageType}
          queryKey={[QUERY_KEYS.EDUCATION.LANGUAGE_TYPES, [QUERY_KEYS.EDUCATION.LANGUAGE_TYPES, filters]]}
          entityName='Language Type'
        />,
        <AdminDashActionLinks
          delete
          editAction={() => handleEditLanguageType(languageType._id)}
          basePath='/language-types'
          id={languageType._id}
          itemName={languageType.name}
          deleteItem={() => deleteItem.mutateAsync(languageType._id)}
          manualAction={[
            <AdminViewButton variant='icon' href={`/languages?languageType=${languageType._id}`} label={`View ${languageType.name} Languages`} />,
          ]}
        />,
      ],
    };
  });

  const filterOptions: FilterOption[] = [
    {
      type: filterTypeMaps.input.key as FilterTypeKey,
      label: 'Language Type Name',
      key: 'name',
    },
  ];

  const handleApplyFilters = (newFilters: FilterValues) => {
    const params = new URLSearchParams(searchParams);
    Object.entries(newFilters).forEach(([key, value]) => {
      if (value && typeof value === 'string') {
        params.set(key, value);
      } else {
        params.delete(key);
      }
    });
    params.set('page', '1');
    router.push(`?${params.toString()}`);
  };

  const handleResetFilters = () => {
    router.push(`?page=1`);
  };

  return (
    <SectionWrapper>
      <HeadingBar
        title='Language Types'
        elements={[
          <LimitFilter
            key='limit'
            value={(filters.limit as string) || '10'}
            onChange={(value: string) => {
              handleApplyFilters({ limit: value });
            }}
          />,
          <MasterFilter
            key='filter'
            filterOptions={filterOptions}
            onApplyFilters={handleApplyFilters}
            onResetFilters={handleResetFilters}
            initialFilters={filters}
          />,
        ]}
        addAction={addLanguageTypeModal.open}
      />
      <MasterTable headers={headers} rows={rows} isLoading={isLoading} pagination={pagination} />

      {/* Add Language Type Modal */}
      <PrimaryFormModal
        isOpen={addLanguageTypeModal.isOpen}
        onClose={addLanguageTypeModal.close}
        title='Add New Language Type'
        subtitle='Create a new language type for your platform'
        size='sm'
        showSparkles={true}
      >
        <LanguageTypeForm onSuccess={addLanguageTypeModal.close} />
      </PrimaryFormModal>

      {/* Edit Language Type Modal */}
      <PrimaryFormModal
        isOpen={editLanguageTypeModal.isOpen}
        onClose={() => {
          editLanguageTypeModal.close();
          setSelectedLanguageTypeId(null);
        }}
        title='Edit Language Type'
        subtitle='Update existing language type details'
        size='sm'
        isLoading={isLoadingSelectedLanguageType}
        loadingTitle='Loading Language Type Data'
        loadingMessage='Please wait while we fetch the language type details...'
        isError={!!selectedLanguageTypeError}
        errorMessage={selectedLanguageTypeError?.message}
      >
        {selectedLanguageTypeData?.data?.languageType && (
          <LanguageTypeForm data={selectedLanguageTypeData.data.languageType} onSuccess={editLanguageTypeModal.close} />
        )}
      </PrimaryFormModal>
    </SectionWrapper>
  );
};

export default LanguageTypeTable;
