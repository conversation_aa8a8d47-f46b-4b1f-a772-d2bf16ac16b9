'use client';

import { Form } from '@/components/ui/form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import { toast } from 'react-toastify';
import { PrimaryInput, PrimarySwitchInput, SubmitButton } from '@/components/forms';
import { IAddUpdateFormProps } from '@/types/api';
import { useCreateLanguage, useUpdateLanguage } from '@/hooks/education/language.hooks';
import { ILanguageDocument } from '@/server/services/education/language.service';
import { createLanguageSchema } from '@/validation/schemas/education/language.schema';
import { extractId } from '@/validation/utils/form.utils';

interface ILanguageFormProps extends IAddUpdateFormProps<ILanguageDocument> {
  onSuccess: () => void;
  languageTypeId?: string | null;
}

const LanguageForm: React.FC<ILanguageFormProps> = ({ data, onSuccess, languageTypeId }) => {
  const { mutateAsync: createLanguage, isPending: isCreating } = useCreateLanguage();
  const { mutateAsync: updateLanguage, isPending: isUpdating } = useUpdateLanguage();

  const languageId = data?._id;
  const isSubmitting = isCreating || isUpdating;

  const form = useForm<z.infer<typeof createLanguageSchema>>({
    resolver: zodResolver(createLanguageSchema),
    defaultValues: {
      name: data?.name || '',
      languageType: extractId(data?.languageType) || languageTypeId || '',
      isActive: data?.isActive !== undefined ? data.isActive : true,
    },
  });

  const onSubmit = async (values: z.infer<typeof createLanguageSchema>) => {
    try {
      const result = languageId 
        ? await updateLanguage({ id: languageId, data: values }) 
        : await createLanguage(values);
        
      if (!result.success) throw new Error(result.message || 'Operation failed');

      toast.success(result.message);
      onSuccess();
    } catch (error: any) {
      toast.error(error.message || 'An error occurred');
    }
  };

  return (
    <section className='relative'>
      <div className='bg-white rounded-lg shadow-sm p-6 max-w-2xl'>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className='grid grid-cols-1 gap-6 items-start justify-start'>
            {/* Name field */}
            <PrimaryInput form={form} name='name' label='Language Name' placeholder='Enter language name' required />

            {/* Is Active */}
            <PrimarySwitchInput
              form={form}
              name='isActive'
              label='Is this language active?'
              description='Making it inactive will hide it from the website.'
            />

            <SubmitButton
              submittingLabel={languageId ? 'Updating...' : 'Creating...'}
              label={languageId ? 'Update Language' : 'Create Language'}
              isSubmitting={isSubmitting}
              disabled={isSubmitting}
              className='w-fit'
            />
          </form>
        </Form>
      </div>
    </section>
  );
};

export default LanguageForm;
