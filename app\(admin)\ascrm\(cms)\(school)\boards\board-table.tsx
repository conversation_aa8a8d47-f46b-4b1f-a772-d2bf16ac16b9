'use client';

import { useState, useEffect, useCallback } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { AdminDashActionLinks, HeadingBar, LimitFilter, MasterFilter, MasterTable, SectionWrapper } from '@/components/dashboard/ascrm/misc';
import { ErrorState } from '@/components/dashboard/ascrm/misc/StateUI';
import { filterTypeMaps, FilterTypeKey } from '@/constants';
import { FilterValues, FilterOption } from '@/types/api';
import { useGetAllBoards, useDeleteBoard, useGetBoardById } from '@/hooks/education/school.hooks';
import BoardForm from './board-form';
import { AdminViewButton, PrimaryFormModal, useModal, StatusToggle } from '@/components/dashboard/shared/misc';
import { boardService } from '@/server/services/education/school.service';
import { QUERY_KEYS } from '@/lib/react-query/queryKeys';

const BoardTable: React.FC = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const addBoardModal = useModal();
  const editBoardModal = useModal();

  const [selectedBoardId, setSelectedBoardId] = useState<string | null>(null);
  const [filters, setFilters] = useState<FilterValues>({});

  const updateFiltersFromParams = useCallback(() => {
    const initialFilters: FilterValues = {};
    searchParams.forEach((value, key) => {
      if (key !== 'page') {
        initialFilters[key] = value;
      }
    });
    setFilters(initialFilters);
  }, [searchParams]);

  useEffect(() => {
    updateFiltersFromParams();
  }, [updateFiltersFromParams]);

  const page = Number(searchParams.get('page')) || 1;

  const queryParams = { page, ...filters };

  const { data: boardsResponse, isLoading, error } = useGetAllBoards(queryParams);
  const deleteItem = useDeleteBoard();
  const { data: selectedBoardData, isLoading: isLoadingSelectedBoard, error: selectedBoardError } = useGetBoardById(selectedBoardId || '');

  const boards = boardsResponse?.data?.boards || [];
  const pagination = boardsResponse?.data?.pagination;

  const handleEditBoard = (id: string) => {
    setSelectedBoardId(id);
    editBoardModal.open();
  };

  if (error) {
    return <ErrorState message={error.message} />;
  }

  const headers = ['Name', 'Status', 'Actions'];

  const rows = boards.map((board) => {
    return {
      id: board._id,
      values: [
        <p className='font-medium'>{board.name}</p>,
        <StatusToggle
          id={board._id}
          isActive={board.isActive}
          updateFn={boardService.updateBoard}
          queryKey={[QUERY_KEYS.EDUCATION.BOARDS, [QUERY_KEYS.EDUCATION.BOARDS, filters]]}
          entityName='Board'
        />,
        <AdminDashActionLinks
          delete
          editAction={() => handleEditBoard(board._id)}
          basePath='/boards'
          id={board._id}
          itemName={board.name}
          deleteItem={() => deleteItem.mutateAsync(board._id)}
          manualAction={[<AdminViewButton variant='icon' href={`/classes?board=${board._id}`} label={`${board.name} Classes`} />]}
        />,
      ],
    };
  });

  const filterOptions: FilterOption[] = [
    {
      type: filterTypeMaps.input.key as FilterTypeKey,
      label: 'Board Name',
      key: 'name',
    },
  ];

  const handleApplyFilters = (newFilters: FilterValues) => {
    const params = new URLSearchParams(searchParams);
    Object.entries(newFilters).forEach(([key, value]) => {
      if (value && typeof value === 'string') {
        params.set(key, value);
      } else {
        params.delete(key);
      }
    });
    params.set('page', '1');
    router.push(`?${params.toString()}`);
  };

  const handleResetFilters = () => {
    router.push(`?page=1`);
  };

  return (
    <SectionWrapper>
      <HeadingBar
        title='School Boards'
        elements={[
          <LimitFilter
            key='limit'
            value={(filters.limit as string) || '10'}
            onChange={(value: string) => {
              handleApplyFilters({ limit: value });
            }}
          />,
          <MasterFilter
            key='filter'
            filterOptions={filterOptions}
            onApplyFilters={handleApplyFilters}
            onResetFilters={handleResetFilters}
            initialFilters={filters}
          />,
        ]}
        addAction={addBoardModal.open}
      />
      <MasterTable headers={headers} rows={rows} isLoading={isLoading} pagination={pagination} />

      {/* Add Board Modal */}
      <PrimaryFormModal
        isOpen={addBoardModal.isOpen}
        onClose={addBoardModal.close}
        title='Add New School Board'
        subtitle='Create a new school board for your platform'
        size='sm'
        showSparkles={true}
      >
        <BoardForm onSuccess={addBoardModal.close} />
      </PrimaryFormModal>

      {/* Edit Board Modal */}
      <PrimaryFormModal
        isOpen={editBoardModal.isOpen}
        onClose={() => {
          editBoardModal.close();
          setSelectedBoardId(null);
        }}
        title='Edit School Board'
        subtitle='Update existing school board details'
        size='sm'
        isLoading={isLoadingSelectedBoard}
        loadingTitle='Loading Board Data'
        loadingMessage='Please wait while we fetch the board details...'
        isError={!!selectedBoardError}
        errorMessage={selectedBoardError?.message}
      >
        {selectedBoardData?.data?.board && <BoardForm data={selectedBoardData.data.board} onSuccess={editBoardModal.close} />}
      </PrimaryFormModal>
    </SectionWrapper>
  );
};

export default BoardTable;
