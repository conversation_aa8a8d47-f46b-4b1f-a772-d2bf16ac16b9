import type { Config } from 'tailwindcss';

const config = {
  darkMode: ['class'],
  content: ['./pages/**/*.{ts,tsx}', './components/**/*.{ts,tsx}', './app/**/*.{ts,tsx}', './src/**/*.{ts,tsx}'],
  prefix: '',
  theme: {
    container: {
      center: true,
      padding: '2rem',
      screens: {
        '2xl': '1400px',
      },
    },
    extend: {
      colors: {
        primaryColor: {
          '50': 'hsl(var(--primary-color-50))',
          '100': 'hsl(var(--primary-color-100))',
          '200': 'hsl(var(--primary-color-200))',
          '300': 'hsl(var(--primary-color-300))',
          '400': 'hsl(var(--primary-color-400))',
          '500': 'hsl(var(--primary-color-500))',
          '600': 'hsl(var(--primary-color-600))',
          '700': 'hsl(var(--primary-color-700))',
          '800': 'hsl(var(--primary-color-800))',
          '900': 'hsl(var(--primary-color-900))',
          '950': 'hsl(var(--primary-color-950))',
          DEFAULT: 'hsl(var(--primary-color-500))',
        },
        secondaryColor: {
          '50': 'hsl(var(--secondary-color-50))',
          '100': 'hsl(var(--secondary-color-100))',
          '200': 'hsl(var(--secondary-color-200))',
          '300': 'hsl(var(--secondary-color-300))',
          '400': 'hsl(var(--secondary-color-400))',
          '500': 'hsl(var(--secondary-color-500))',
          '600': 'hsl(var(--secondary-color-600))',
          '700': 'hsl(var(--secondary-color-700))',
          '800': 'hsl(var(--secondary-color-800))',
          '900': 'hsl(var(--secondary-color-900))',
          '950': 'hsl(var(--secondary-color-950))',
          DEFAULT: 'hsl(var(--secondary-color-500))',
        },
        border: 'hsl(var(--border))',
        input: 'hsl(var(--input))',
        ring: 'hsl(var(--ring))',
        background: 'hsl(var(--background))',
        foreground: 'hsl(var(--foreground))',
        primary: {
          DEFAULT: 'hsl(var(--primary))',
          foreground: 'hsl(var(--primary-foreground))',
        },
        secondary: {
          DEFAULT: 'hsl(var(--secondary))',
          foreground: 'hsl(var(--secondary-foreground))',
        },
        destructive: {
          DEFAULT: 'hsl(var(--destructive))',
          foreground: 'hsl(var(--destructive-foreground))',
        },
        muted: {
          DEFAULT: 'hsl(var(--muted))',
          foreground: 'hsl(var(--muted-foreground))',
        },
        accent: {
          DEFAULT: 'hsl(var(--accent))',
          foreground: 'hsl(var(--accent-foreground))',
        },
        popover: {
          DEFAULT: 'hsl(var(--popover))',
          foreground: 'hsl(var(--popover-foreground))',
        },
        card: {
          DEFAULT: 'hsl(var(--card))',
          foreground: 'hsl(var(--card-foreground))',
        },
      },
      fontSize: {
        xs: '0.8rem',
      },
      backgroundImage: {
        'my-gradient-1': 'linear-gradient(to right, hsl(var(--primary-color-500)), hsl(var(--secondary-color-500)))',
      },
      borderRadius: {
        lg: 'var(--radius)',
        md: 'calc(var(--radius) - 2px)',
        sm: 'calc(var(--radius) - 4px)',
      },
      keyframes: {
        'accordion-down': {
          from: { height: '0' },
          to: { height: 'var(--radix-accordion-content-height)' },
        },
        'accordion-up': {
          from: { height: 'var(--radix-accordion-content-height)' },
          to: { height: '0' },
        },
      },
      animation: {
        'accordion-down': 'accordion-down 0.2s ease-out',
        'accordion-up': 'accordion-up 0.2s ease-out',
      },
    },
  },
  plugins: [require('tailwindcss-animate')],
} satisfies Config;

export default config;
