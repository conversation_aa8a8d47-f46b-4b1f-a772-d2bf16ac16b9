import { z } from 'zod';

export const addStudentEducationSchema = z.object({
  studentId: z.string().min(1, { message: 'Please select a student.' }),
  dob: z.string().min(1, { message: 'Please enter the date of birth.' }),
  gender: z.enum(['Male', 'Female', 'Other'], { message: 'Please select a valid gender.' }),
  board: z.string().min(1, { message: 'Please enter the board.' }),
  branch: z.string().min(1, { message: 'Please enter the branch.' }),
  class: z.string().min(1, { message: 'Please enter the class.' }),
  degree: z.string().min(1, { message: 'Please enter the degree.' }),
  schoolName: z.string().min(1, { message: 'Please enter the school name.' }),
  schoolLocation: z.string().min(1, { message: 'Please enter the school location.' }),
  startYear: z
    .number()
    .int()
    .min(1900, { message: 'Please enter a valid start year.' })
    .max(new Date().getFullYear(), { message: 'Start year cannot be in the future.' }),
  endYear: z
    .number()
    .int()
    .min(1900, { message: 'Please enter a valid end year.' })
    .max(new Date().getFullYear(), { message: 'End year cannot be in the future.' }),
  scoreType: z.enum(['In Marks', 'In Percentage', 'In CGPA'], { message: 'Please select a valid score type.' }),
  obtainedValue: z.string().refine(
    (val) => {
      const obtained = parseFloat(val);
      return !isNaN(obtained) && obtained >= 0;
    },
    { message: 'Please enter a valid obtained value.' }
  ),
  maxValue: z.string().refine(
    (val) => {
      const max = parseFloat(val);
      return !isNaN(max) && max >= 0;
    },
    { message: 'Please enter a valid maximum value.' }
  ),
});
