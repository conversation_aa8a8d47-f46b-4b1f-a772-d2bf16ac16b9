import { z } from 'zod';
import { dateOfBirthSchema, genderSchema, profilePictureSchema } from '../common.schema';

export const createChildProfileSchema = z.object({
  fullName: z.string().min(1, 'Full name is required'),
  gender: genderSchema,
  dateOfBirth: dateOfBirthSchema,
  avatar: profilePictureSchema,
});

export type CreateChildProfileInput = z.infer<typeof createChildProfileSchema>;
export type UpdateChildProfileInput = Partial<CreateChildProfileInput>;
