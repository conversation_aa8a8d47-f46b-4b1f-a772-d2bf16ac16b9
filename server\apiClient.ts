import axios, { AxiosError, AxiosInstance, AxiosResponse, InternalAxiosRequestConfig } from 'axios';
import { IAPIResponse } from '@/types/api';
import { baseAPIURL } from './axiosInstance';

class ApiClient {
  private instance: AxiosInstance;

  constructor(baseURL: string) {
    this.instance = axios.create({ baseURL, withCredentials: true, timeout: 500000 });

    this.initializeInterceptors();
  }

  private initializeInterceptors() {
    this.instance.interceptors.request.use(this.handleRequest, this.handleRequestError);

    this.instance.interceptors.response.use(this.handleResponse, this.handleResponseError);
  }

  private handleRequest = (config: InternalAxiosRequestConfig) => {
    if (config.data instanceof FormData) {
      config.headers['Content-Type'] = 'multipart/form-data';
    } else {
      config.headers['Content-Type'] = 'application/json';
    }
    return config;
  };

  private handleRequestError = (error: AxiosError) => Promise.reject(error);

  private handleResponse = (response: AxiosResponse<IAPIResponse<any>>): AxiosResponse<IAPIResponse<any>> => {
    if (response.data.success) {
      return response;
    }
    throw new Error(response.data.message);
  };

  private handleResponseError = (error: AxiosError<IAPIResponse<null>>) => {
    const apiErrorResponse: IAPIResponse<null> = {
      success: false,
      message: this.extractErrorMessage(error),
      statusCode: error.response?.status || 500,
      data: null,
    };
    return Promise.reject(apiErrorResponse);
  };

  private extractErrorMessage(error: AxiosError<IAPIResponse<null>>): string {
    return error.response?.data?.message || error.message || 'An unexpected error occurred. Please try again.';
  }

  public async get<T>(url: string, params?: Record<string, unknown>): Promise<IAPIResponse<T>> {
    const response = await this.instance.get<IAPIResponse<T>>(url, { params });
    return response.data;
  }

  public async post<T>(url: string, data: unknown): Promise<IAPIResponse<T>> {
    const response = await this.instance.post<IAPIResponse<T>>(url, data);
    return response.data;
  }

  public async patch<T>(url: string, data: unknown): Promise<IAPIResponse<T>> {
    const response = await this.instance.patch<IAPIResponse<T>>(url, data);
    return response.data;
  }

  public async delete<T>(url: string): Promise<IAPIResponse<T>> {
    const response = await this.instance.delete<IAPIResponse<T>>(url);
    return response.data;
  }
}

const apiClient = new ApiClient(baseAPIURL);

export default apiClient;
