import { FormField, FormItem, FormControl, FormLabel, FormMessage } from '@/components/ui/form';
import { Textarea } from '@/components/ui/textarea';
import type { UseFormReturn, FieldValues, Path } from 'react-hook-form';
import { inputClassName } from './form.constants';

interface PrimaryTextareaProps<TFieldValues extends FieldValues> {
  form: UseFormReturn<TFieldValues>;
  name: Path<TFieldValues>;
  label: string;
  placeholder?: string;
  required?: boolean;
  rows?: number;
  variant?: 'primary' | 'secondary' | 'green' | 'purple';
}

const PrimaryTextarea = <TFieldValues extends FieldValues>({
  form,
  name,
  label,
  placeholder,
  required = false,
  rows = 3,
  variant = 'primary',
}: PrimaryTextareaProps<TFieldValues>) => {
  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => (
        <FormItem className='w-full'>
          <FormLabel className='primary-label'>
            <span>{label}</span>
            {required && <span className='text-primaryColor'>*</span>}
          </FormLabel>
          <FormControl>
            <Textarea
              className={`primary-textarea focus-visible:!ring-offset-2 ${inputClassName[variant]}`}
              placeholder={placeholder}
              rows={rows}
              {...field}
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
};

export default PrimaryTextarea;
