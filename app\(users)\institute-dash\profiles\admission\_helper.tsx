'use client';

import { useState } from 'react';
import TeachingExperienceForm from '@/components/dashboard/institute-dash/forms/TeachingExperienceForm';
import TutionInfoForm from '@/components/dashboard/institute-dash/forms/TuitionInfoForm';
import { Clock, Home, MapPin, School, X } from 'lucide-react';
import { AlertDialog, AlertDialogCancel, AlertDialogContent, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { SimpleTable } from '@/components/dashboard/institute-dash/misc';

import WhatDoYouTeachForm from '@/components/dashboard/institute-dash/forms/WhatDoYouTeachForm';
import StudySectionTable from '@/components/dashboard/institute-dash/misc/StudySectionTable';
import { TabsFilterWithBadge, TabsMobileFilterWithBadge } from '@/components/dashboard/shared/misc/TabsFilterWithBadge';
import { studySectionMap } from '@/constants';
import TutorDashActionLinks from '@/components/dashboard/institute-dash/misc/InstiuteDashActionLinks';

const TutorTutionProfileHelper = () => {
  const [selectedMainTab, setSelectedMainTab] = useState('info');
  const [selectedSubTab, setSelectedSubTab] = useState(studySectionMap.school.key);

  const mainTabCounts = { info: 1, experience: 2, 'subjects-classes': 0 };

  const subTabCounts = Object.entries(studySectionMap).reduce((acc, [key, { key: tabKey }]) => {
    acc[tabKey] = Math.floor(Math.random() * 10) + 1;
    return acc;
  }, {} as { [key: string]: number });

  const mainTabLabels = {
    large: {
      info: 'Admission Information',
      experience: 'Teaching Experience',
      'subjects-classes': 'What Do You Teach',
    },
    mobile: {
      info: 'Tuition Info',
      experience: 'Experience',
      'subjects-classes': 'What Do You Teach',
    },
  };

  const isSecondLevelVisible = selectedMainTab === 'subjects-classes';

  return (
    <div className='bg-white p-6 rounded-3xl relative lg:min-h-[650px] flex md:flex-wrap lg:flex-nowrap gap-6 lg:gap-12 items-start'>
      <div className='flex flex-col gap-6 max-md:hidden max-lg:flex-row max-lg:w-full'>
        <TabsFilterWithBadge
          selectedTab={selectedMainTab}
          setSelectedTab={setSelectedMainTab}
          tabCounts={mainTabCounts}
          labels={mainTabLabels.large}
        />
        {isSecondLevelVisible && (
          <div className='md:w-full lg:w-auto'>
            <TabsFilterWithBadge
              displayInFullWidth
              selectedTab={selectedSubTab}
              setSelectedTab={setSelectedSubTab}
              secondLevelLabels={studySectionMap}
              tabCounts={subTabCounts}
              showNumbers
            />
          </div>
        )}
      </div>
      <div className='w-full'>
        {selectedMainTab === 'info' && (
          <div className='flex flex-col gap-6 w-full'>
            <h2 className='text-base font-semibold'>Admission Information</h2>
            <TutionInfoForm two />
          </div>
        )}
        {selectedMainTab === 'experience' && (
          <div>
            <div className='flex justify-end items-start mb-4'>
              <AlertDialog>
                <div className='flex justify-between items-center w-full flex-wrap gap-2'>
                  <h2 className='text-base font-semibold'>Teaching Experience Info</h2>
                  <AlertDialogTrigger className='btn-default-sm'>Add Experience</AlertDialogTrigger>
                </div>

                <AlertDialogContent className='lg:w-[700px] lg:max-w-none'>
                  <TeachingExperienceForm />
                  <AlertDialogCancel className='border-0 absolute rounded-none rounded-tr-md right-0 top-0 bg-primaryColor-50 hover:bg-primaryColor-100 flex-shrink-0 p-2 tracking-wider cursor-pointer group overflow-hidden'>
                    <p className='text-primaryColor'>
                      <X />
                    </p>
                  </AlertDialogCancel>
                </AlertDialogContent>
              </AlertDialog>
            </div>

            <div className='max-md:hidden'>
              <SimpleTable
                headers={['Tuition Type', 'Exp. (M)', 'Place Name', 'Location']}
                rows={[
                  ['Private Tuition', 16, 'Self', 'Self', <TutorDashActionLinks basePath='/' id='' edit delete />],
                  ['Institute Tuition', 24, 'Sherpur Public School', 'Sherpur, Bangladesh', <TutorDashActionLinks basePath='/' id='' edit delete />],
                  [
                    'College Tuition',
                    12,
                    'Mujibur Rahman Public School',
                    'Mujibur Rahman, Bangladesh',
                    <TutorDashActionLinks basePath='/' id='' edit delete />,
                  ],
                  [
                    'School Tuition',
                    6,
                    'Online Tutoring Platform',
                    'Online Tutoring Platform, Bangladesh',
                    <TutorDashActionLinks basePath='/' id='' edit delete />,
                  ],
                ]}
              />
            </div>

            <div className='md:hidden'>
              {[
                ['Private Tuition', 16, 'Self', 'Self'],
                ['Institute Tuition', 24, 'Sherpur Public School', 'Sherpur, Bangladesh'],
                ['College Tuition', 12, 'Mujibur Rahman Public School', 'Mujibur Rahman, Bangladesh'],
                ['School Tuition', 6, 'Online Tutoring Platform', 'Online Tutoring Platform, Bangladesh'],
              ].map((row, rowIndex) => (
                <div key={rowIndex} className='border rounded-lg p-4 mb-4 text-sm'>
                  {row.map((cell, cellIndex) => (
                    <div key={cellIndex} className='mb-2 flex items-center gap-2 flex-wrap'>
                      {cellIndex === 0 && <Home strokeWidth={1.5} className='text-primaryColor' />}
                      {cellIndex === 1 && <Clock strokeWidth={1.5} className='text-primaryColor' />}
                      {cellIndex === 2 && <MapPin strokeWidth={1.5} className='text-primaryColor' />}
                      {cellIndex === 3 && <School strokeWidth={1.5} className='text-primaryColor' />}

                      <span className='font-medium'>{['Tuition Type', 'Exp. (M)', 'Place Name', 'Location'][cellIndex]}:</span>
                      <span className='text-gray-600'>{cell}</span>
                    </div>
                  ))}
                  <div className='flex justify-end'>
                    <TutorDashActionLinks edit delete basePath='/' id='' />
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
        {selectedMainTab === 'subjects-classes' && (
          <div>
            <div className='flex justify-end items-start mb-4'>
              <AlertDialog>
                <div className='flex justify-between items-center w-full'>
                  <h2 className='text-base font-semibold'>
                    {studySectionMap[selectedSubTab as keyof typeof studySectionMap].label} Section Information
                  </h2>
                  <AlertDialogTrigger className='btn-default-sm'>Add Record</AlertDialogTrigger>
                </div>
                <AlertDialogContent className='!w-[700px] !max-w-none'>
                  <WhatDoYouTeachForm />
                  <AlertDialogCancel className='border-0 absolute rounded-none rounded-tr-md right-0 top-0 bg-primaryColor-50 hover:bg-primaryColor-100 flex-shrink-0 p-2 tracking-wider cursor-pointer group overflow-hidden'>
                    <p className='text-primaryColor'>
                      <X />
                    </p>
                  </AlertDialogCancel>
                </AlertDialogContent>
              </AlertDialog>
            </div>
            <StudySectionTable
              headers={['SN', 'Board', 'Class', 'Subjects', 'Budget']}
              rows={[
                [1, 'CBSE', '10th', 'All Subjects', '5000/hourly'],
                [2, 'ICSE', '11th', 'Mathematics, Science, English', '7000/hourly'],
                [3, 'State Board', '12th', 'Mathematics, Science, English', '9000/hourly'],
                [4, 'International Board', '12th', 'Mathematics, Science, English', '11000/hourly'],
                [5, 'ICSE', '11th', 'Mathematics, Science, English', '7000/hourly'],
              ]}
            />
          </div>
        )}
      </div>
      <TabsMobileFilterWithBadge
        selectedTab={selectedMainTab}
        setSelectedTab={setSelectedMainTab}
        tabCounts={mainTabCounts}
        showNumbers={false}
        labels={mainTabLabels.mobile}
        showSecondLevel={isSecondLevelVisible}
        secondLevelTabCounts={subTabCounts}
        secondLevelLabels={studySectionMap}
        selectedSecondTab={selectedSubTab}
        secondLevelShowNumbers={true}
        setSelectedSecondTab={setSelectedSubTab}
      />
    </div>
  );
};

export default TutorTutionProfileHelper;
