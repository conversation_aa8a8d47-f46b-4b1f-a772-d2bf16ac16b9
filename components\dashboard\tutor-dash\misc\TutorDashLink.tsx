import Link, { LinkProps } from 'next/link';
import { TUTOR_DASH_PATH } from '@/constants/tutor-dash';

interface ITutorDashLink extends LinkProps {
  children: React.ReactNode;
  className?: string;
}

const TutorDashLink = ({ children, href, className, ...props }: ITutorDashLink) => (
  <Link href={`${TUTOR_DASH_PATH}${href}`} className={className} {...props}>
    {children}
  </Link>
);

export default TutorDashLink;
