'use client';

import { FormField, FormItem, FormControl, Form, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { jobTypesMap, preferredJobModesMap, languagesMap, languageLevelsMap, keySkillsMap, subjectsSpecializationMap } from '@/constants';
import { zodResolver } from '@hookform/resolvers/zod';
import { Loader2, X } from 'lucide-react';
import { useForm, useFieldArray } from 'react-hook-form';
import * as z from 'zod';
import { tutorJobSchema } from '@/lib/validations/tutor-dash/zod';
import { useState } from 'react';
import { Textarea } from '@/components/ui/textarea';
import MultiSelectWithSearch from './MultiSelectWithSearch';

const TutorJobProfileBasicForm = () => {
  const languageItem = { language: '', level: '' } as any;

  const form = useForm<z.infer<typeof tutorJobSchema>>({
    resolver: zodResolver(tutorJobSchema),
    defaultValues: {
      currentPackage: '',
      preferredJobLocation: '',
      languages: [languageItem],
      description: '',
      keySkills: [],
      subjectsSpecialization: [],
    },
  });

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: 'languages',
  });

  const preferredJobMode = form.watch('preferredJobMode');

  async function onSubmit(values: z.infer<typeof tutorJobSchema>) {
    console.log(values);
  }

  return (
    <div>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className='grid grid-cols-1 md:grid-cols-2 items-start gap-6'>
          {/* Job Type */}
          <FormField
            control={form.control}
            name='jobType'
            render={({ field }) => (
              <FormItem className='w-full'>
                <FormLabel className='primary-label'>
                  <span>Job Type</span> <span className='text-primaryColor'>*</span>
                </FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger className='primary-select'>
                      <SelectValue placeholder='Select a job type' />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {Object.entries(jobTypesMap).map(([key, { label }]) => (
                      <SelectItem key={key} value={key}>
                        {label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Current Package */}
          <FormField
            control={form.control}
            name='currentPackage'
            render={({ field }) => (
              <FormItem className='w-full'>
                <FormLabel className='primary-label'>
                  <span>Current Package (CTC in INR)</span> <span className='text-primaryColor'>*</span>
                </FormLabel>
                <FormControl>
                  <Input className='primary-input' type='number' placeholder='Enter your current package in INR' {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Preferred Job Type */}
          <FormField
            control={form.control}
            name='preferredJobMode'
            render={({ field }) => (
              <FormItem className='w-full'>
                <FormLabel className='primary-label'>
                  <span>Preferred Job Type</span> <span className='text-primaryColor'>*</span>
                </FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger className='primary-select'>
                      <SelectValue placeholder='Select a preferred job type' />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {Object.entries(preferredJobModesMap).map(([key, { label }]) => (
                      <SelectItem key={key} value={key}>
                        {label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Preferred Job Location */}
          {preferredJobMode !== 'online' && (
            <FormField
              control={form.control}
              name='preferredJobLocation'
              render={({ field }) => (
                <FormItem className='w-full'>
                  <FormLabel className='primary-label'>
                    <span>Preferred Job Location</span>
                  </FormLabel>
                  <FormControl>
                    <Input className='primary-input' type='text' placeholder='Enter your preferred job location' {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          )}

          {/* Subjects Specialization */}
          <FormField
            control={form.control}
            name='subjectsSpecialization'
            render={({ field }) => (
              <FormItem className='w-full'>
                <FormLabel className='primary-label'>
                  <span>Subjects Specialization</span> <span className='text-primaryColor'>*</span>
                </FormLabel>
                <FormControl>
                  <MultiSelectWithSearch
                    selectedOptions={field.value}
                    onChange={field.onChange}
                    options={Object.values(subjectsSpecializationMap).map((subject) => ({ label: subject.label, value: subject.key }))}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Key Skills */}
          <FormField
            control={form.control}
            name='keySkills'
            render={({ field }) => (
              <FormItem className='w-full'>
                <FormLabel className='primary-label'>
                  <span>Key Skills</span> <span className='text-primaryColor'>*</span>
                </FormLabel>
                <FormControl>
                  <MultiSelectWithSearch
                    selectedOptions={field.value}
                    onChange={field.onChange}
                    options={Object.values(keySkillsMap).map((keySkill) => ({ label: keySkill.label, value: keySkill.key }))}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Languages */}
          <FormLabel className='primary-label md:col-span-2'>
            <span>Languages</span> <span className='text-primaryColor'>*</span>
          </FormLabel>
          {fields.map((item, index) => (
            <div key={item.id} className='grid grid-cols-2 gap-4 items-start'>
              <FormField
                control={form.control}
                name={`languages.${index}.language`}
                render={({ field }) => (
                  <FormItem className='w-full'>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger className='primary-select'>
                          <SelectValue placeholder='Select a language' />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {Object.entries(languagesMap).map(([key, { label }]) => (
                          <SelectItem key={key} value={key}>
                            {label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name={`languages.${index}.level`}
                render={({ field }) => (
                  <FormItem className='w-full relative'>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger className='primary-select'>
                          <SelectValue placeholder='Select a level' />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {Object.entries(languageLevelsMap).map(([key, { label }]) => (
                          <SelectItem key={key} value={key}>
                            {label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                    {index > 0 && (
                      <button
                        type='button'
                        onClick={() => remove(index)}
                        className='bg-red-50 text-red-500 py-0.5 px-1 rounded absolute -bottom-5 right-0'
                      >
                        <X size={12} />
                      </button>
                    )}
                  </FormItem>
                )}
              />
            </div>
          ))}
          <button type='button' onClick={() => append(languageItem)} className='btn-default__outline-sm col-span-2 w-fit self-center'>
            Add New Language
          </button>

          {/* Description */}
          <FormField
            control={form.control}
            name='description'
            render={({ field }) => (
              <FormItem className='w-full md:col-span-2'>
                <FormLabel className='primary-label'>
                  <span>Describe Yourself</span> <span className='text-primaryColor'>*</span>
                </FormLabel>
                <FormControl>
                  <Textarea className='primary-input' placeholder='Describe yourself' {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <button type='submit' disabled={form.formState.isSubmitting} className='btn-default-md md:col-span-2 w-fit'>
            {form.formState.isSubmitting ? (
              <>
                <Loader2 size={20} className='animate-spin mr-3' />
                <span>Saving...</span>
              </>
            ) : (
              <span>Submit</span>
            )}
          </button>
        </form>
      </Form>
    </div>
  );
};

export default TutorJobProfileBasicForm;
