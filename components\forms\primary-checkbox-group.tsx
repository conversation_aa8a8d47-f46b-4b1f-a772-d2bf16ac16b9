'use client';

import React from 'react';
import type { UseFormReturn, FieldValues, Path } from 'react-hook-form';
import { FormField, FormItem, FormControl, FormLabel, FormMessage } from '@/components/ui/form';
import { Checkbox } from '@/components/ui/checkbox';

interface PrimaryCheckboxGroupProps<TFieldValues extends FieldValues> {
  form: UseFormReturn<TFieldValues>;
  name: Path<TFieldValues>;
  label?: string;
  options: { label: string; value: string }[];
  required?: boolean;
}

const PrimaryCheckboxGroup = <TFieldValues extends FieldValues>({
  form,
  name,
  label,
  options,
  required = false,
}: PrimaryCheckboxGroupProps<TFieldValues>) => {
  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => (
        <FormItem className='w-full'>
          {label && (
            <FormLabel className='primary-label'>
              <span>{label}</span>
              {required && <span className='text-primaryColor'>*</span>}
            </FormLabel>
          )}
          <FormControl>
            <div className='flex flex-wrap gap-4'>
              {options.map((option) => (
                <label key={option.value} className='flex items-center space-x-2'>
                  <Checkbox
                    checked={field.value?.includes(option.value)}
                    onCheckedChange={(checked) => {
                      const updatedValue = checked
                        ? [...(field.value || []), option.value]
                        : field.value?.filter((value: string) => value !== option.value);
                      field.onChange(updatedValue);
                    }}
                  />
                  <span className='text-sm'>{option.label}</span>
                </label>
              ))}
            </div>
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
};

export default PrimaryCheckboxGroup;
