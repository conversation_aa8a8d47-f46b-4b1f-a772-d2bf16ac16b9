'use client';

import { useEffect, useState } from 'react';
import Image from 'next/image';
import { useRouter } from 'next/navigation';

interface IRedirectAfterKyc {
  redirectUrl: string;
}

const RedirectAfterKyc: React.FC<IRedirectAfterKyc> = ({ redirectUrl }) => {
  const [countdown, setCountdown] = useState(3);
  const router = useRouter();

  useEffect(() => {
    const interval = setInterval(() => {
      setCountdown((prev) => {
        if (prev === 1) {
          clearInterval(interval);
          router.push(redirectUrl);
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(interval);
  }, [router, redirectUrl]);

  return (
    <div className='relative'>
      <div className='w-full flex flex-col gap-4 items-center justify-center text-center'>
        <Image src='/icons/kyc-welcome.webp' alt='Redirect Arrow' width={450} height={450} className='max-w-full h-auto' />
        <div className='space-y-2'>
          <h2 className='text-2xl font-semibold text-primaryColor'>Hold Tight!</h2>
          <p>Your KYC document is submitted.</p>
          <p className='text-gray-600'>
            You're being redirected to the previous page.
            <br /> in just {countdown} seconds...
          </p>
        </div>
      </div>
    </div>
  );
};

export default RedirectAfterKyc;