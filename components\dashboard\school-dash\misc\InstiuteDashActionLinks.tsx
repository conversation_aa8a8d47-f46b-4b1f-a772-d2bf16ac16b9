import { Edit, Eye, Trash2 } from 'lucide-react';
import InstiuteDashLink from './InstiuteDashLink';

interface IInstiuteDashActionLinks {
  view?: boolean;
  edit?: boolean;
  delete?: boolean;
  all?: boolean;
  basePath: string;
  id: string;
}

const InstiuteDashActionLinks: React.FC<IInstiuteDashActionLinks> = ({
  basePath,
  id,
  view = false,
  edit = false,
  delete: del = false,
  all = false,
}) => {
  return (
    <div className='flex gap-2'>
      {(all || view) && (
        <InstiuteDashLink href={`/${basePath}/${id}/view`}>
          <div className='bg-green-100 text-green-700 hover:bg-green-200 rounded size-7 flex items-center justify-center'>
            <Eye size={16} strokeWidth={1.5} />
          </div>
        </InstiuteDashLink>
      )}
      {(all || edit) && (
        <InstiuteDashLink href={`/${basePath}/${id}/edit`}>
          <div className='bg-blue-100 text-blue-700 hover:bg-blue-200 rounded size-7 flex items-center justify-center'>
            <Edit size={16} strokeWidth={1.5} />
          </div>
        </InstiuteDashLink>
      )}
      {(all || del) && (
        <InstiuteDashLink href={`/${basePath}/${id}/delete`}>
          <div className='bg-red-100 text-red-700 hover:bg-red-200 rounded size-7 flex items-center justify-center'>
            <Trash2 size={16} strokeWidth={1.5} />
          </div>
        </InstiuteDashLink>
      )}
    </div>
  );
};

export default InstiuteDashActionLinks;
