'use client';

import { useState, useEffect, useCallback } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { AdminDashActionLinks, HeadingBar, LimitFilter, MasterFilter, MasterTable, SectionWrapper } from '@/components/dashboard/ascrm/misc';
import { ErrorState } from '@/components/dashboard/ascrm/misc/StateUI';
import { filterTypeMaps, FilterTypeKey } from '@/constants';
import { FilterValues, FilterOption } from '@/types/api';
import { useGetAllHobbies, useDeleteHobby, useGetHobbyById, useGetHobbyTypeById } from '@/hooks/education/hobby.hooks';
import { PrimaryFormModal, useModal, StatusToggle } from '@/components/dashboard/shared/misc';
import HobbyForm from './hobby-form';
import { hobbyService } from '@/server/services/education/hobby.service';
import { QUERY_KEYS } from '@/lib/react-query/queryKeys';

const HobbyTable: React.FC = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const addHobbyModal = useModal();
  const editHobbyModal = useModal();

  const [selectedHobbyId, setSelectedHobbyId] = useState<string | null>(null);
  const [filters, setFilters] = useState<FilterValues>({});
  const [hobbyTypeName, setHobbyTypeName] = useState<string>('');

  const hobbyTypeId = searchParams.get('hobbyType');

  if (!hobbyTypeId) return <ErrorState message='You are not authorized to view this page.' />;

  const { data: hobbyTypeData, error: hobbyTypeError } = useGetHobbyTypeById(hobbyTypeId || '');

  useEffect(() => {
    if (hobbyTypeData?.data?.hobbyType) {
      setHobbyTypeName(hobbyTypeData.data.hobbyType.name);
    }
  }, [hobbyTypeData]);

  const updateFiltersFromParams = useCallback(() => {
    const initialFilters: FilterValues = {};
    searchParams.forEach((value, key) => {
      if (key !== 'page' && key !== 'hobbyType') {
        initialFilters[key] = value;
      }
    });
    setFilters(initialFilters);
  }, [searchParams]);

  useEffect(() => {
    updateFiltersFromParams();
  }, [updateFiltersFromParams]);

  const page = Number(searchParams.get('page')) || 1;

  const queryParams = { page, hobbyType: hobbyTypeId, ...filters };

  const { data: hobbiesResponse, isLoading, error } = useGetAllHobbies(queryParams);
  const deleteItem = useDeleteHobby();
  const { data: selectedHobbyData, isLoading: isLoadingSelectedHobby, error: selectedHobbyError } = useGetHobbyById(selectedHobbyId || '');

  const hobbies = hobbiesResponse?.data?.hobbies || [];
  const pagination = hobbiesResponse?.data?.pagination;

  const handleEditHobby = (id: string) => {
    setSelectedHobbyId(id);
    editHobbyModal.open();
  };

  if (error || hobbyTypeError) {
    return <ErrorState message={error?.message || hobbyTypeError?.message || 'An unexpected error occurred.'} />;
  }

  const headers = ['Name', 'Status', 'Actions'];

  const rows = hobbies.map((hobby) => {
    return {
      id: hobby._id,
      values: [
        <p className='font-medium'>{hobby.name}</p>,
        <StatusToggle
          id={hobby._id}
          isActive={hobby.isActive}
          updateFn={hobbyService.updateHobby}
          queryKey={[QUERY_KEYS.EDUCATION.HOBBIES, [QUERY_KEYS.EDUCATION.HOBBIES, { page, hobbyType: hobbyTypeId, ...filters }]]}
          entityName='Hobby'
        />,
        <AdminDashActionLinks
          delete
          editAction={() => handleEditHobby(hobby._id)}
          basePath='/hobbies'
          id={hobby._id}
          itemName={hobby.name}
          deleteItem={() => deleteItem.mutateAsync(hobby._id)}
        />,
      ],
    };
  });

  const filterOptions: FilterOption[] = [
    {
      type: filterTypeMaps.input.key as FilterTypeKey,
      label: 'Hobby Name',
      key: 'name',
    },
  ];

  const handleApplyFilters = (newFilters: FilterValues) => {
    const params = new URLSearchParams(searchParams);
    Object.entries(newFilters).forEach(([key, value]) => {
      if (value && typeof value === 'string') {
        params.set(key, value);
      } else {
        params.delete(key);
      }
    });
    params.set('page', '1');
    router.push(`?${params.toString()}`);
  };

  const handleResetFilters = () => {
    const params = new URLSearchParams();
    params.set('hobbyType', hobbyTypeId);
    params.set('page', '1');
    router.push(`?${params.toString()}`);
  };

  return (
    <SectionWrapper>
      <HeadingBar
        title={hobbyTypeName ? `Hobbies of ${hobbyTypeName}` : 'Hobbies'}
        elements={[
          <LimitFilter
            key='limit'
            value={(filters.limit as string) || '10'}
            onChange={(value: string) => {
              handleApplyFilters({ limit: value });
            }}
          />,
          <MasterFilter
            key='filter'
            filterOptions={filterOptions}
            onApplyFilters={handleApplyFilters}
            onResetFilters={handleResetFilters}
            initialFilters={filters}
          />,
        ]}
        addAction={addHobbyModal.open}
      />
      <MasterTable headers={headers} rows={rows} isLoading={isLoading} pagination={pagination} />

      {/* Add Hobby Modal */}
      <PrimaryFormModal
        isOpen={addHobbyModal.isOpen}
        onClose={addHobbyModal.close}
        title={hobbyTypeName ? `Add New ${hobbyTypeName} Hobby` : 'Add New Hobby'}
        subtitle='Create a new hobby for your platform'
        size='sm'
        showSparkles={true}
      >
        <HobbyForm onSuccess={addHobbyModal.close} hobbyTypeId={hobbyTypeId} />
      </PrimaryFormModal>

      {/* Edit Hobby Modal */}
      <PrimaryFormModal
        isOpen={editHobbyModal.isOpen}
        onClose={() => {
          editHobbyModal.close();
          setSelectedHobbyId(null);
        }}
        title={hobbyTypeName ? `Edit ${hobbyTypeName} Hobby` : 'Edit Hobby'}
        subtitle='Update existing hobby details'
        size='sm'
        isLoading={isLoadingSelectedHobby}
        loadingTitle='Loading Hobby Data'
        loadingMessage='Please wait while we fetch the hobby details...'
        isError={!!selectedHobbyError}
        errorMessage={selectedHobbyError?.message}
      >
        {selectedHobbyData?.data?.hobby && (
          <HobbyForm data={selectedHobbyData.data.hobby} onSuccess={editHobbyModal.close} hobbyTypeId={hobbyTypeId} />
        )}
      </PrimaryFormModal>
    </SectionWrapper>
  );
};

export default HobbyTable;
