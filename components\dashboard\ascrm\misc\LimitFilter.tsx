import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface LimitFilterProps {
  value: string;
  onChange: (value: string) => void;
  options?: string[];
  label?: string;
}

const LimitFilter: React.FC<LimitFilterProps> = ({ value, onChange, options = ['10', '25', '50', '100'], label = 'Items per page' }) => {
  return (
    <div className='flex items-center gap-3'>
      <span className='text-sm text-gray-500 font-medium whitespace-nowrap'>{label}</span>
      <Select onValueChange={onChange} value={value}>
        <SelectTrigger className='w-[80px] h-9 bg-white border-gray-200 rounded-md text-sm font-medium shadow-sm hover:bg-gray-50 transition-colors'>
          <SelectValue placeholder='Select limit' />
        </SelectTrigger>
        <SelectContent className='min-w-[80px]'>
          {options.map((option) => (
            <SelectItem key={option} value={option} className='text-sm cursor-pointer'>
              {option}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
};

export default LimitFilter;
