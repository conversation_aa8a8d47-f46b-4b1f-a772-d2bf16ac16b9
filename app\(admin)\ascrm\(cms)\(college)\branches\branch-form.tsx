'use client';

import { Form } from '@/components/ui/form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import { toast } from 'react-toastify';
import { PrimaryInput, PrimarySwitchInput, SubmitButton } from '@/components/forms';
import { IAddUpdateFormProps } from '@/types/api';
import { useCreateBranch, useUpdateBranch } from '@/hooks/education/college.hooks';
import { IBranchDocument } from '@/server/services/education/college.service';
import { createBranchSchema } from '@/validation/schemas/education/college.schema';
import { extractId } from '@/validation/utils/form.utils';

interface IBranchFormProps extends IAddUpdateFormProps<IBranchDocument> {
  onSuccess: () => void;
  degreeId?: string | null;
}

const BranchForm: React.FC<IBranchFormProps> = ({ data, onSuccess, degreeId }) => {
  const { mutateAsync: createBranch, isPending: isCreating } = useCreateBranch();
  const { mutateAsync: updateBranch, isPending: isUpdating } = useUpdateBranch();

  const branchId = data?._id;
  const isSubmitting = isCreating || isUpdating;

  const form = useForm<z.infer<typeof createBranchSchema>>({
    resolver: zodResolver(createBranchSchema),
    defaultValues: {
      name: data?.name || '',
      degree: extractId(data?.degree) || degreeId || '',
      isActive: data?.isActive !== undefined ? data.isActive : true,
    },
  });

  const onSubmit = async (values: z.infer<typeof createBranchSchema>) => {
    try {
      const result = branchId 
        ? await updateBranch({ id: branchId, data: values }) 
        : await createBranch(values);
        
      if (!result.success) throw new Error(result.message || 'Operation failed');

      toast.success(result.message);
      onSuccess();
    } catch (error: any) {
      toast.error(error.message || 'An error occurred');
    }
  };

  return (
    <section className='relative'>
      <div className='bg-white rounded-lg shadow-sm p-6 max-w-2xl'>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className='grid grid-cols-1 gap-6 items-start justify-start'>
            {/* Name field */}
            <PrimaryInput form={form} name='name' label='Branch Name' placeholder='Enter branch name' required />

            {/* Is Active */}
            <PrimarySwitchInput
              form={form}
              name='isActive'
              label='Is this branch active?'
              description='Making it inactive will hide it from the website.'
            />

            <SubmitButton
              submittingLabel={branchId ? 'Updating...' : 'Creating...'}
              label={branchId ? 'Update Branch' : 'Create Branch'}
              isSubmitting={isSubmitting}
              disabled={isSubmitting}
              className='w-fit'
            />
          </form>
        </Form>
      </div>
    </section>
  );
};

export default BranchForm;
