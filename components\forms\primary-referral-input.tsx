'use client';

import { referralSourcesOptions, referralSourcesWhichRequiresOther, IReferralSourcesWhichRequiresOther } from '@/validation/schemas/maps';
import { useState, useEffect } from 'react';
import { UseFormReturn } from 'react-hook-form';
import { PrimarySelect, PrimaryInput } from '.';

interface PrimaryReferralInputProps {
  form: UseFormReturn<any>;
  selectName?: string;
  otherInputName?: string;
  selectLabel?: string;
  otherInputLabel?: string;
  selectPlaceholder?: string;
  otherInputPlaceholder?: string;
  required?: boolean;
}

const PrimaryReferralInput = ({
  form,
  selectName = 'referralSource',
  otherInputName = 'otherReferralSource',
  selectLabel = 'How did you hear about us?',
  otherInputLabel = 'Please specify',
  selectPlaceholder = 'Select an option',
  otherInputPlaceholder = 'Tell us how you heard about us',
  required = false,
}: PrimaryReferralInputProps) => {
  const requiresOtherInput = (source: IReferralSourcesWhichRequiresOther) =>
    referralSourcesWhichRequiresOther.includes(source as IReferralSourcesWhichRequiresOther);

  const [showOtherInput, setShowOtherInput] = useState(requiresOtherInput(form.watch(selectName) || ''));

  useEffect(() => {
    const subscription = form.watch((value, { name }) => {
      if (name === selectName) {
        const selectedValue = value[selectName] || '';
        const needsOtherInput = requiresOtherInput(selectedValue);

        setShowOtherInput(needsOtherInput);

        if (!needsOtherInput) {
          form.setValue(otherInputName, '');
        }
      }
    });

    return () => subscription.unsubscribe();
  }, [form, selectName, otherInputName]);

  return (
    <>
      <PrimarySelect
        form={form}
        name={selectName}
        label={selectLabel}
        placeholder={selectPlaceholder}
        options={referralSourcesOptions}
        required={required}
      />

      {showOtherInput && (
        <PrimaryInput form={form} name={otherInputName} label={otherInputLabel} placeholder={otherInputPlaceholder} required={required} />
      )}
    </>
  );
};

export default PrimaryReferralInput;
