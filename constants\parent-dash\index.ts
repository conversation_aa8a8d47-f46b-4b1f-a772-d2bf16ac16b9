export const PARENT_DASH_PATH = '/parent-dash';

import {
  LayoutDashboard,
  Book,
  Clipboard,
  Calendar,
  HelpCircle,
  Bell,
  LogOut,
  User,
  FileIcon,
  FileText,
  Folder,
  Home,
  Package,
  UserCircle,
  Users,
} from 'lucide-react';

export const gendersMap = { male: { label: 'Male', key: 'male' }, female: { label: 'Female', key: 'female' } };

export const navigationLinks = [
  {
    id: 'home',
    label: 'Dashboard',
    links: [
      { title: 'Dashboard', href: '/', icon: LayoutDashboard },
      { title: 'Tuition Enquiries', href: '/leads', icon: Book },
      { title: 'Admission Enquiries', href: '/admission-enquiries', icon: Book },
    ],
  },
  {
    id: 'profiles',
    label: 'Profiles',
    links: [
      { title: 'Profile', href: '/profiles/basic', icon: User },
      { title: 'KYC', href: '/profiles/kyc', icon: FileIcon },
      { title: 'Education Profile', href: '/profiles/education', icon: Clipboard },
    ],
  },
  {
    id: 'batches',
    label: 'Batches',
    links: [
      { title: 'Batches', href: '/batches', icon: Calendar },
      { title: 'Wallet', href: '/wallet', icon: Folder },
    ],
  },
  {
    id: 'support',
    label: 'Support',
    links: [
      { title: 'Complaints', href: '/support/complaints', icon: HelpCircle },
      { title: 'Notifications', href: '/support/notifications', icon: Bell },
    ],
  },
  {
    id: 'logout',
    label: 'Logout',
    links: [{ title: 'Logout', href: '/logout', icon: LogOut }],
  },
];

export const bottomBarLinks = [
  {
    id: 'home',
    label: 'Home',
    icon: Home,
    links: [
      { title: 'Dashboard', href: '/', icon: LayoutDashboard },
      { title: 'Tuition Leads', href: 'leads', icon: Book },
      { title: 'Jobs', href: 'jobs', icon: Package },
    ],
  },
  {
    id: 'profiles',
    label: 'Profiles',
    icon: UserCircle,
    links: [
      { title: 'Basic', href: 'profiles/basic', icon: User },
      { title: 'Tuition', href: 'profiles/tuition', icon: Users },
      { title: 'KYC', href: 'profiles/kyc', icon: FileIcon },
      { title: 'Qualification', href: 'profiles/qualification', icon: Clipboard },
      { title: 'Jobs', href: 'profiles/jobs', icon: Package },
    ],
  },
  {
    id: 'batches',
    label: 'Batches',
    icon: Calendar,
    links: [
      { title: 'Batches', href: 'batches', icon: Calendar },
      { title: 'Wallet', href: 'wallet', icon: Folder },
      { title: 'Plans', href: 'plans', icon: FileText },
    ],
  },
  {
    id: 'support',
    label: 'Support',
    icon: HelpCircle,

    links: [
      { title: 'Complaints', href: 'support/complaints', icon: HelpCircle },
      { title: 'Notifications', href: 'support/notifications', icon: Bell },
    ],
  },
];
