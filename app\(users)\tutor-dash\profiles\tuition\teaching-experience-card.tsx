'use client';

import { Clock, MapPin, School, Edit, Trash2, Briefcase } from 'lucide-react';
import { ITeachingExperienceDocument } from '@/server/services/tutor/tuition-profile.service';
import { tuitionTypeMap } from '@/validation/schemas/tutor/tuition.maps';
import { formatExperienceByMonth } from '@/lib/date.utils';

interface TeachingExperienceCardProps {
  experience: ITeachingExperienceDocument;
  onEdit: (experience: ITeachingExperienceDocument) => void;
  onDelete: () => void;
}

const TeachingExperienceCard = ({ experience, onEdit, onDelete }: TeachingExperienceCardProps) => {
  const tuitionTypeInfo = tuitionTypeMap[experience.tuitionType];

  const getColorScheme = (type: string) => {
    switch (type) {
      case 'private':
        return {
          bgGradient: 'from-blue-50 to-blue-100',
          bgSolid: 'bg-blue-50',
          border: 'border-blue-200',
          text: 'text-blue-600',
          badge: 'bg-blue-100 text-blue-700',
          icon: 'text-blue-500',
        };
      case 'school':
        return {
          bgGradient: 'from-green-50 to-green-100',
          bgSolid: 'bg-green-50',
          border: 'border-green-200',
          text: 'text-green-600',
          badge: 'bg-green-100 text-green-700',
          icon: 'text-green-500',
        };
      case 'college':
        return {
          bgGradient: 'from-purple-50 to-purple-100',
          bgSolid: 'bg-purple-50',
          border: 'border-purple-200',
          text: 'text-purple-600',
          badge: 'bg-purple-100 text-purple-700',
          icon: 'text-purple-500',
        };
      case 'institute':
        return {
          bgGradient: 'from-orange-50 to-orange-100',
          bgSolid: 'bg-orange-50',
          border: 'border-orange-200',
          text: 'text-orange-600',
          badge: 'bg-orange-100 text-orange-700',
          icon: 'text-orange-500',
        };
      default:
        return {
          bgGradient: 'from-gray-50 to-gray-100',
          bgSolid: 'bg-gray-50',
          border: 'border-gray-200',
          text: 'text-gray-600',
          badge: 'bg-gray-100 text-gray-700',
          icon: 'text-gray-500',
        };
    }
  };

  const colorScheme = getColorScheme(experience.tuitionType);

  return (
    <div className={`bg-white rounded-2xl border ${colorScheme.border} overflow-hidden shadow-sm hover:shadow-md transition-all duration-300 group`}>
      <div className={`h-1.5 bg-gradient-to-r ${colorScheme.bgGradient}`}></div>

      <div className='p-6'>
        <div className='flex justify-between items-start mb-4'>
          <div className='flex items-start gap-3'>
            <div className={`relative w-12 h-12 rounded-lg bg-gradient-to-br ${colorScheme.bgGradient} p-0.5 shadow-sm`}>
              <div className={`absolute inset-0 rounded-lg bg-gradient-to-br ${colorScheme.bgGradient} opacity-20`}></div>
              <div className='w-full h-full rounded-lg bg-white flex items-center justify-center'>
                <Briefcase size={22} className={colorScheme.icon} />
              </div>
            </div>

            <div className='flex-1'>
              <div className='flex items-center gap-2 mb-1'>
                <h3 className='font-bold text-gray-800'>{experience.placeName}</h3>
                <span className={`px-2 py-0.5 text-xs font-medium rounded-full ${colorScheme.badge}`}>
                  {tuitionTypeInfo?.label || experience.tuitionType}
                </span>
              </div>

              {/* Edit/Delete Actions */}
              <div className='flex gap-2'>
                <button
                  onClick={() => onEdit(experience)}
                  className='inline-flex items-center gap-1.5 px-3 py-1.5 text-xs font-medium text-blue-600 bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors duration-200'
                >
                  <Edit size={14} />
                  Edit
                </button>
                <button
                  onClick={onDelete}
                  className='inline-flex items-center gap-1.5 px-3 py-1.5 text-xs font-medium text-red-600 bg-red-50 hover:bg-red-100 rounded-lg transition-colors duration-200'
                >
                  <Trash2 size={14} />
                  Delete
                </button>
              </div>
            </div>
          </div>
        </div>

        <div className='space-y-3'>
          <div className='flex items-center gap-2.5 group/item'>
            <div
              className={`w-8 h-8 rounded-lg ${colorScheme.bgSolid} flex items-center justify-center group-hover/item:bg-opacity-80 transition-colors`}
            >
              <Clock size={16} className={`${colorScheme.icon} group-hover/item:scale-110 transition-transform`} />
            </div>
            <div>
              <p className='text-xs text-gray-500'>Experience Duration</p>
              <p className='text-sm font-medium text-gray-800'>{formatExperienceByMonth(experience.experienceMonths)}</p>
            </div>
          </div>

          <div className='flex items-center gap-2.5 group/item'>
            <div
              className={`w-8 h-8 rounded-lg ${colorScheme.bgSolid} flex items-center justify-center group-hover/item:bg-opacity-80 transition-colors`}
            >
              <School size={16} className={`${colorScheme.icon} group-hover/item:scale-110 transition-transform`} />
            </div>
            <div>
              <p className='text-xs text-gray-500'>Institution Type</p>
              <p className='text-sm font-medium text-gray-800'>{tuitionTypeInfo?.label || experience.tuitionType}</p>
            </div>
          </div>

          <div className='flex items-center gap-2.5 group/item'>
            <div
              className={`w-8 h-8 rounded-lg ${colorScheme.bgSolid} flex items-center justify-center group-hover/item:bg-opacity-80 transition-colors`}
            >
              <MapPin size={16} className={`${colorScheme.icon} group-hover/item:scale-110 transition-transform`} />
            </div>
            <div>
              <p className='text-xs text-gray-500'>Location</p>
              <p className='text-sm font-medium text-gray-800'>{experience.location}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TeachingExperienceCard;
