import { UseFormReturn } from 'react-hook-form';
import { ISearchResponse } from '@/server/services/enquiry.service';

export class EducationalFormPopulator {
  private form: UseFormReturn<any>;

  constructor(form: UseFormReturn<any>) {
    this.form = form;
  }

  populateTeachingSubjectForm(match: ISearchResponse['matches'][0]) {
    const matchDetails = match.details || {};

    this.resetSubjectArrays();

    switch (match.type) {
      case 'schools':
        this.form.setValue('serviceCategory', 'schools');
        this.form.setValue('boardId', matchDetails.board?.id || '');
        this.form.setValue('classId', matchDetails.class?.id || '');
        break;

      case 'colleges':
        this.form.setValue('serviceCategory', 'colleges');
        this.form.setValue('streamId', matchDetails.stream?.id || '');
        this.form.setValue('degreeLevelId', matchDetails.degreeLevel?.id || '');
        this.form.setValue('degreeId', matchDetails.degree?.id || '');
        this.form.setValue('branchId', matchDetails.branch?.id || '');
        break;

      case 'hobbies':
        this.form.setValue('serviceCategory', 'hobbies');
        this.form.setValue('hobbyTypeId', matchDetails.hobbyType?.id || '');
        this.form.setValue('hobbyId', matchDetails.hobby?.id || '');
        break;

      case 'languages':
        this.form.setValue('serviceCategory', 'languages');
        this.form.setValue('languageTypeId', matchDetails.languageType?.id || '');
        this.form.setValue('languageId', matchDetails.language?.id || '');
        break;

      case 'it_courses':
        this.form.setValue('serviceCategory', 'it_courses');
        this.form.setValue('courseTypeId', matchDetails.courseCategory?.id || '');
        this.form.setValue('courseId', matchDetails.course?.id || '');
        break;

      case 'exams':
        this.form.setValue('serviceCategory', 'exams');
        this.form.setValue('examCategoryId', matchDetails.examCategory?.id || '');
        this.form.setValue('examId', matchDetails.exam?.id || '');
        break;
    }
  }

  populateEnquiryForm(match: ISearchResponse['matches'][0]) {
    const matchDetails = match.details || {};

    switch (match.type) {
      case 'schools':
        this.form.setValue('category', 'schools');
        this.form.setValue('boardId', matchDetails.board?.id || '');
        this.form.setValue('classId', matchDetails.class?.id || '');
        this.form.setValue('subjectIds', []);
        break;

      case 'colleges':
        this.form.setValue('category', 'colleges');
        this.form.setValue('streamId', matchDetails.stream?.id || '');
        this.form.setValue('degreeLevelId', matchDetails.degreeLevel?.id || '');
        this.form.setValue('degreeId', matchDetails.degree?.id || '');
        this.form.setValue('branchId', matchDetails.branch?.id || '');
        this.form.setValue('collegeSubjectIds', []);
        break;

      case 'hobbies':
        this.form.setValue('category', 'hobbies');
        this.form.setValue('hobbyTypeId', matchDetails.hobbyType?.id || '');
        this.form.setValue('hobbyId', matchDetails.hobby?.id || '');
        break;

      case 'languages':
        this.form.setValue('category', 'languages');
        this.form.setValue('languageTypeId', matchDetails.languageType?.id || '');
        this.form.setValue('languageId', matchDetails.language?.id || '');
        break;

      case 'it_courses':
        this.form.setValue('category', 'it_courses');
        this.form.setValue('courseTypeId', matchDetails.courseCategory?.id || '');
        this.form.setValue('courseId', matchDetails.course?.id || '');
        break;

      case 'exams':
        this.form.setValue('category', 'exams');
        this.form.setValue('examCategoryId', matchDetails.examCategory?.id || '');
        this.form.setValue('examId', matchDetails.exam?.id || '');
        this.form.setValue('examSubjectIds', []);
        break;
    }
  }

  private resetSubjectArrays() {
    this.form.setValue('subjectIds', []);
    this.form.setValue('collegeSubjectIds', []);
    this.form.setValue('examSubjectIds', []);
  }

  static isFieldDisabled(fieldName: string, selectedMatch: ISearchResponse['matches'][0] | null, matchType: string): boolean {
    if (!selectedMatch || selectedMatch.type !== matchType) return false;

    const matchDetails = selectedMatch.details || {};

    const fieldMappings: Record<string, any> = {
      boardId: matchDetails.board,
      classId: matchDetails.class,
      streamId: matchDetails.stream,
      degreeLevelId: matchDetails.degreeLevel,
      degreeId: matchDetails.degree,
      branchId: matchDetails.branch,
      hobbyTypeId: matchDetails.hobbyType,
      hobbyId: matchDetails.hobby,
      languageTypeId: matchDetails.languageType,
      languageId: matchDetails.language,
      courseTypeId: matchDetails.courseCategory,
      courseId: matchDetails.course,
      examCategoryId: matchDetails.examCategory,
      examId: matchDetails.exam,
    };

    return !!fieldMappings[fieldName];
  }
}

export const createFormPopulator = (form: UseFormReturn<any>) => {
  return new EducationalFormPopulator(form);
};
