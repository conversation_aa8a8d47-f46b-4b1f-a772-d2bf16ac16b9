'use client';

import { Search } from 'lucide-react';
import { <PERSON><PERSON>, TinyLoader } from '@/components/dashboard/shared/misc';
import { ISearchResponse } from '@/server/services/enquiry.service';
import { IAPIResponse } from '@/types/api';

interface EducationalSearchInputProps {
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  selectedMatch: ISearchResponse['matches'][0] | null;
  setSelectedMatch: (match: ISearchResponse['matches'][0] | null) => void;
  searchResults: IAPIResponse<ISearchResponse> | undefined;
  isSearching: boolean;
  onSelectMatch: (match: ISearchResponse['matches'][0]) => void;
  placeholder?: string;
  title?: string;
  subtitle?: string;
  minSearchLength?: number;
  className?: string;
  disabled?: boolean;
}

const EducationalSearchInput = ({
  searchTerm,
  setSearchTerm,
  selectedMatch,
  setSelectedMatch,
  searchResults,
  isSearching,
  onSelectMatch,
  placeholder = 'Type your Class, Degree, Hobby, Language, IT Course or Exam...',
  title = 'Quick Search',
  subtitle = '(Optional)',
  minSearchLength = 2,
  className = '',
  disabled = false,
}: EducationalSearchInputProps) => {
  return (
    <div className={`p-4 bg-white rounded-xl border border-gray-200 shadow-sm ${className}`}>
      <h3 className='text-xl font-semibold text-gray-900 mb-4 flex items-center gap-3'>
        <div className='p-2 bg-gray-100 rounded-lg'>
          <Search className='h-5 w-5 text-gray-700' />
        </div>
        <span>{title}</span>
        <span className='text-sm font-normal text-gray-500'>{subtitle}</span>
      </h3>

      <div className='relative'>
        <input
          type='text'
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          placeholder={placeholder}
          disabled={disabled}
          className='w-full px-5 py-3 pl-12 text-sm border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-primaryColor-500 focus:border-transparent shadow-sm bg-white placeholder:text-gray-400 disabled:bg-gray-50 disabled:cursor-not-allowed'
        />
        <Search className='absolute left-4 top-3.5 text-gray-400' size={20} />
      </div>

      {searchTerm.length > 0 && searchTerm.length < minSearchLength && (
        <Alert className='mt-3' type='info' message={`Please enter at least ${minSearchLength} characters to search`} />
      )}

      {/* Search Results */}
      {searchTerm.length >= minSearchLength && (
        <div className='mt-4'>
          <div className='border border-gray-200 rounded-xl overflow-hidden bg-white shadow-sm'>
            {isSearching ? (
              <div className='p-4 flex items-center justify-center'>
                <TinyLoader message='Searching...' className='min-h-[50px] flex flex-col items-center justify-center' />
              </div>
            ) : searchResults?.data?.matches && searchResults.data.matches.length < 1 ? (
              <div className='p-3 text-center text-gray-500 text-sm'>No results found</div>
            ) : (
              <div className='max-h-[280px] overflow-y-auto scrollbar-thin scrollbar-thumb-gray-200 scrollbar-track-transparent'>
                {searchResults?.data?.matches &&
                  searchResults.data.matches.map((result, index: number) => (
                    <div
                      key={index}
                      className='px-4 py-2.5 border-b border-gray-100 last:border-b-0 hover:bg-gray-50 cursor-pointer transition-all group'
                      onClick={() => onSelectMatch(result)}
                    >
                      <div className='font-medium text-sm text-gray-700 group-hover:text-primaryColor-700'>{result.displayText}</div>
                    </div>
                  ))}
              </div>
            )}
          </div>
        </div>
      )}

      {/* Selected Match Display */}
      {selectedMatch && (
        <div className='mt-4 px-3 py-2 bg-primaryColor-50 border border-primaryColor-200 rounded-xl'>
          <div className='flex items-center gap-3'>
            <div className='w-1.5 h-1.5 bg-primaryColor-500 rounded-full'></div>
            <span className='font-medium text-sm text-primaryColor-800'>Selected: {selectedMatch.displayText}</span>
            <button
              type='button'
              onClick={() => setSelectedMatch(null)}
              className='ml-auto text-primaryColor-600 hover:text-primaryColor-800 text-sm bg-white px-3 py-1.5 rounded-lg border border-primaryColor-200 shadow-sm transition-all hover:shadow flex items-center gap-1'
            >
              Clear
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default EducationalSearchInput;
