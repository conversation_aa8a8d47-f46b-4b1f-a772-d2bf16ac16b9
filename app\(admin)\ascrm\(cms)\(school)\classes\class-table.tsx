'use client';

import { useState, useEffect, useCallback } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { AdminDashActionLinks, HeadingBar, LimitFilter, MasterFilter, MasterTable, SectionWrapper } from '@/components/dashboard/ascrm/misc';
import { ErrorState } from '@/components/dashboard/ascrm/misc/StateUI';
import { filterTypeMaps, FilterTypeKey } from '@/constants';
import { FilterValues, FilterOption } from '@/types/api';
import { useGetAllClasses, useDeleteClass, useGetClassById, useGetBoardById } from '@/hooks/education/school.hooks';
import { AdminViewButton, PrimaryFormModal, useModal, StatusToggle } from '@/components/dashboard/shared/misc';
import ClassForm from './class-form';
import { classService } from '@/server/services/education/school.service';
import { QUERY_KEYS } from '@/lib/react-query/queryKeys';

const ClassTable: React.FC = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const addClassModal = useModal();
  const editClassModal = useModal();

  const [selectedClassId, setSelectedClassId] = useState<string | null>(null);
  const [filters, setFilters] = useState<FilterValues>({});
  const [boardName, setBoardName] = useState<string>('');

  const boardId = searchParams.get('board');
  if (!boardId) return <ErrorState message='You are not authorized to view this page.' />;

  const { data: boardData, error: boardError } = useGetBoardById(boardId || '');

  useEffect(() => {
    if (boardData?.data?.board) {
      const board = boardData.data.board;
      setBoardName(board.name);
    }
  }, [boardData]);

  const updateFiltersFromParams = useCallback(() => {
    const initialFilters: FilterValues = {};
    searchParams.forEach((value, key) => {
      if (key !== 'page') {
        initialFilters[key] = value;
      }
    });
    setFilters(initialFilters);
  }, [searchParams]);

  useEffect(() => {
    updateFiltersFromParams();
  }, [updateFiltersFromParams]);

  const page = Number(searchParams.get('page')) || 1;

  const queryParams = { page, board: boardId, ...filters };

  const { data: classesResponse, isLoading, error } = useGetAllClasses(queryParams);
  const deleteItem = useDeleteClass();
  const { data: selectedClassData, isLoading: isLoadingSelectedClass, error: selectedClassError } = useGetClassById(selectedClassId || '');

  const classes = classesResponse?.data?.classes || [];
  const pagination = classesResponse?.data?.pagination;

  const handleEditClass = (id: string) => {
    setSelectedClassId(id);
    editClassModal.open();
  };

  if (error || boardError) {
    return <ErrorState message={error?.message || boardError?.message || 'An unexpected error occurred.'} />;
  }

  const headers = ['Name', 'Display Order', 'Status', 'Actions'];

  const rows = classes.map((classItem) => {
    return {
      id: classItem._id,
      values: [
        <p className='font-medium'>{classItem.name}</p>,
        <p>{classItem.displayOrder}</p>,
        <StatusToggle
          id={classItem._id}
          isActive={classItem.isActive}
          updateFn={classService.updateClass}
          queryKey={[
            QUERY_KEYS.EDUCATION.CLASSES,
            [QUERY_KEYS.EDUCATION.CLASSES, { page, board: boardId, ...filters }],
            [QUERY_KEYS.EDUCATION.CLASSES_BY_BOARD, boardId],
          ]}
          entityName='Class'
        />,
        <AdminDashActionLinks
          delete
          editAction={() => handleEditClass(classItem._id)}
          basePath='/classes'
          id={classItem._id}
          itemName={classItem.name}
          deleteItem={() => deleteItem.mutateAsync(classItem._id)}
          manualAction={[<AdminViewButton variant='icon' href={`/subjects?class=${classItem._id}`} label={`View ${classItem.name} Subjects`} />]}
        />,
      ],
    };
  });

  const filterOptions: FilterOption[] = [
    {
      type: filterTypeMaps.input.key as FilterTypeKey,
      label: 'Class Name',
      key: 'name',
    },
    {
      type: filterTypeMaps.input.key as FilterTypeKey,
      label: 'Board',
      key: 'board',
    },
  ];

  const handleApplyFilters = (newFilters: FilterValues) => {
    const params = new URLSearchParams(searchParams);
    Object.entries(newFilters).forEach(([key, value]) => {
      if (value && typeof value === 'string') {
        params.set(key, value);
      } else {
        params.delete(key);
      }
    });
    params.set('page', '1');
    router.push(`?${params.toString()}`);
  };

  const handleResetFilters = () => {
    router.push(`?page=1`);
  };

  return (
    <SectionWrapper>
      <HeadingBar
        title={boardName ? `School Classes of ${boardName} Board` : 'School Classes'}
        elements={[
          <LimitFilter
            key='limit'
            value={(filters.limit as string) || '10'}
            onChange={(value: string) => {
              handleApplyFilters({ limit: value });
            }}
          />,
          <MasterFilter
            key='filter'
            filterOptions={filterOptions}
            onApplyFilters={handleApplyFilters}
            onResetFilters={handleResetFilters}
            initialFilters={filters}
          />,
        ]}
        addAction={addClassModal.open}
      />
      <MasterTable headers={headers} rows={rows} isLoading={isLoading} pagination={pagination} />

      {/* Add Class Modal */}
      <PrimaryFormModal
        isOpen={addClassModal.isOpen}
        onClose={addClassModal.close}
        title={boardName ? `Add New Class for ${boardName} Board` : 'Add New School Class'}
        subtitle='Create a new school class for your platform'
        size='sm'
        showSparkles={true}
      >
        <ClassForm onSuccess={addClassModal.close} boardId={boardId} />
      </PrimaryFormModal>

      {/* Edit Class Modal */}
      <PrimaryFormModal
        isOpen={editClassModal.isOpen}
        onClose={() => {
          editClassModal.close();
          setSelectedClassId(null);
        }}
        title={boardName ? `Edit ${boardName} Board Class` : 'Edit School Class'}
        subtitle='Update existing school class details'
        size='sm'
        isLoading={isLoadingSelectedClass}
        loadingTitle='Loading Class Data'
        loadingMessage='Please wait while we fetch the class details...'
        isError={!!selectedClassError}
        errorMessage={selectedClassError?.message}
      >
        {selectedClassData?.data?.class && <ClassForm data={selectedClassData.data.class} onSuccess={editClassModal.close} />}
      </PrimaryFormModal>
    </SectionWrapper>
  );
};

export default ClassTable;
