import React from 'react';
import { FormField, FormItem, FormControl, FormLabel, FormMessage } from '@/components/ui/form';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import type { UseFormReturn, FieldValues, Path } from 'react-hook-form';
import { Loader2 } from 'lucide-react';
import { inputClassName } from './form.constants';

interface PrimarySelectProps<TFieldValues extends FieldValues> {
  form: UseFormReturn<TFieldValues>;
  name: Path<TFieldValues>;
  label: string;
  placeholder?: string;
  required?: boolean;
  options: { value: string; label: string }[];
  isLoading?: boolean;
  disabled?: boolean;
  variant?: 'primary' | 'secondary' | 'green' | 'purple';
}

const PrimarySelect = <TFieldValues extends FieldValues>({
  form,
  name,
  label,
  placeholder = 'Select an option',
  required = false,
  options,
  isLoading = false,
  disabled = false,
  variant = 'primary',
}: PrimarySelectProps<TFieldValues>) => {
  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => (
        <FormItem className='w-full'>
          <FormLabel className='primary-label'>
            <span>{label}</span>
            {required && <span className='text-primaryColor'>*</span>}
          </FormLabel>
          {isLoading ? (
            <div className='flex items-center justify-center h-12 bg-gray-100 rounded-md'>
              <Loader2 className='w-5 h-5 animate-spin text-gray-500' />
            </div>
          ) : (
            <Select onValueChange={field.onChange} value={field.value} disabled={disabled}>
              <FormControl>
                <SelectTrigger className={`${inputClassName[variant]}`}>
                  <SelectValue placeholder={placeholder} />
                </SelectTrigger>
              </FormControl>
              <SelectContent>
                {options.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          )}
          <FormMessage />
        </FormItem>
      )}
    />
  );
};

export default PrimarySelect;
