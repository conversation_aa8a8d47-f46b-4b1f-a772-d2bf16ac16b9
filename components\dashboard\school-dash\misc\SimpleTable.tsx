import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';

interface ISimpleTable {
  headers: string[];
  rows: (string | number | React.ReactNode)[][];
}

const SimpleTable: React.FC<ISimpleTable> = ({ headers, rows }) => {
  const extendedHeaders = ['SN', ...headers];

  return (
    <Table>
      <TableHeader>
        <TableRow>
          {extendedHeaders.map((column, index) => (
            <TableHead key={index}>{column}</TableHead>
          ))}
        </TableRow>
      </TableHeader>
      <TableBody>
        {rows.map((row, rowIndex) => (
          <TableRow key={rowIndex}>
            <TableCell>{rowIndex + 1}</TableCell>
            {row.map((cell, cellIndex) => (
              <TableCell key={cellIndex}>{cell}</TableCell>
            ))}
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );
};

export default SimpleTable;
