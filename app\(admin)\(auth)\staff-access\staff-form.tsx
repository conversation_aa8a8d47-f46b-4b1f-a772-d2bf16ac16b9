'use client';

import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import type * as z from 'zod';
import { useRouter } from 'next/navigation';
import { toast } from 'react-toastify';
import { staffLoginSchema } from '@/validation/schemas/staff.schema';
import { useStaffLogin } from '@/hooks/staff/staff.hooks';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { LockKeyhole, Mail, Loader2, ShieldCheck } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';

const StaffLoginForm = () => {
  const router = useRouter();
  const { mutateAsync: loginStaff, isPending: isLoading } = useStaffLogin();

  const form = useForm<z.infer<typeof staffLoginSchema>>({
    resolver: zodResolver(staffLoginSchema),
    defaultValues: {
      email: '',
      password: '',
    },
  });

  async function onSubmit(data: z.infer<typeof staffLoginSchema>) {
    try {
      const response = await loginStaff(data);

      if (response.success) {
        toast.success(response.message || 'Login successful');
        router.push('/ascrm');
      } else {
        throw new Error(response.message);
      }
    } catch (error: any) {
      toast.error(error.message || 'An error occurred during login');
    }
  }

  return (
    <div className='flex min-h-screen items-center justify-center bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900'>
      {/* Animated background elements */}
      <div className='absolute inset-0 overflow-hidden'>
        <div className='absolute -left-40 -top-40 h-80 w-80 rounded-full bg-red-500/10 blur-3xl'></div>
        <div className='absolute bottom-20 right-20 h-60 w-60 rounded-full bg-red-600/10 blur-3xl'></div>
        <div className='absolute bottom-40 left-1/2 h-40 w-40 -translate-x-1/2 rounded-full bg-red-700/10 blur-3xl'></div>
      </div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className='relative z-10 w-full max-w-xl overflow-hidden rounded-2xl bg-white/10 p-8 backdrop-blur-xl sm:p-10'
      >
        <div className='mx-auto'>
          {/* Logo/Brand */}
          <div className='flex justify-center'>
            <motion.div
              initial={{ scale: 0.8 }}
              animate={{ scale: 1 }}
              transition={{
                type: 'spring',
                stiffness: 200,
                damping: 10,
                delay: 0.2,
              }}
              className='relative flex h-20 w-20 items-center justify-center'
            >
              <img src='/images/logo.png' alt='Logo' className='w-full h-full object-contain' />
            </motion.div>
          </div>

          {/* Header */}
          <div className='mb-8 text-center'>
            <h1 className='mb-2 text-3xl font-bold text-white'>Staff Login</h1>
            <p className='text-slate-300'>Enter your credentials to access the admin dashboard</p>
          </div>

          {/* Form */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
            className='mb-8 space-y-6'
          >
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-5'>
                <FormField
                  control={form.control}
                  name='email'
                  render={({ field }) => (
                    <FormItem className='space-y-1'>
                      <FormLabel className='text-sm font-medium text-slate-300'>Email</FormLabel>
                      <div className='relative'>
                        <div className='pointer-events-none absolute left-4 top-3.5 text-slate-400'>
                          <Mail className='h-5 w-5' />
                        </div>
                        <FormControl>
                          <Input
                            placeholder='Enter your email'
                            className='border-slate-700/50 bg-slate-800/50 pl-12 text-white placeholder:text-slate-500 focus-visible:ring-2 focus-visible:ring-red-500 focus-visible:ring-offset-0 py-6'
                            {...field}
                          />
                        </FormControl>
                      </div>
                      <FormMessage className='text-xs font-medium text-red-500' />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name='password'
                  render={({ field }) => (
                    <FormItem className='space-y-1'>
                      <FormLabel className='text-sm font-medium text-slate-300'>Password</FormLabel>
                      <div className='relative'>
                        <div className='pointer-events-none absolute left-4 top-3.5 text-slate-400'>
                          <LockKeyhole className='h-5 w-5' />
                        </div>
                        <FormControl>
                          <Input
                            type='password'
                            placeholder='Enter your password'
                            className='border-slate-700/50 bg-slate-800/50 pl-12 text-white placeholder:text-slate-500 focus-visible:ring-2 focus-visible:ring-red-500 focus-visible:ring-offset-0 py-6'
                            {...field}
                          />
                        </FormControl>
                      </div>
                      <FormMessage className='text-xs font-medium text-red-500' />
                    </FormItem>
                  )}
                />

                <div className='flex items-center justify-between'>
                  <div className='flex items-center space-x-2'>
                    <Checkbox
                      id='remember-me'
                      className='border-slate-700 bg-slate-800/70 data-[state=checked]:bg-red-600 data-[state=checked]:text-white'
                    />
                    <label
                      htmlFor='remember-me'
                      className='text-sm font-medium leading-none text-slate-300 peer-disabled:cursor-not-allowed peer-disabled:opacity-70'
                    >
                      Remember me
                    </label>
                  </div>
                  <div className='text-sm'>
                    <Link href='/forgot-password' className='font-medium text-red-400 transition-colors hover:text-red-300'>
                      Forgot password?
                    </Link>
                  </div>
                </div>

                <div className='pt-3'>
                  <Button
                    type='submit'
                    disabled={isLoading}
                    className='relative w-full overflow-hidden rounded-lg bg-gradient-to-r from-red-600 to-red-700 py-6 text-base font-medium text-white shadow-lg shadow-red-600/20 transition-all hover:from-red-500 hover:to-red-600 hover:shadow-xl hover:shadow-red-600/30 focus:ring-2 focus:ring-red-500/50'
                  >
                    {isLoading ? (
                      <>
                        <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                        Signing in...
                      </>
                    ) : (
                      'Sign In'
                    )}
                  </Button>
                </div>
              </form>
            </Form>
          </motion.div>

          {/* Divider */}
          <div className='relative my-6'>
            <div className='absolute inset-0 flex items-center'>
              <div className='w-full border-t border-slate-700'></div>
            </div>
            <div className='relative flex justify-center text-sm'>
              <span className='bg-slate-900/50 px-2 text-slate-400 backdrop-blur-sm'>Or continue with</span>
            </div>
          </div>

          {/* Social Login */}
          <div className='grid grid-cols-2 gap-4'>
            {/* Google Login Button */}
            <button
              type='button'
              onClick={() => toast.info('Google login would be implemented here')}
              className='flex items-center justify-center rounded-lg border border-slate-700 bg-slate-800/50 px-4 py-3 text-slate-300 transition-colors hover:bg-slate-700/50'
            >
              <svg className='mr-2 h-5 w-5' viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg'>
                <path
                  d='M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z'
                  fill='#4285F4'
                />
                <path
                  d='M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z'
                  fill='#34A853'
                />
                <path
                  d='M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z'
                  fill='#FBBC05'
                />
                <path
                  d='M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z'
                  fill='#EA4335'
                />
              </svg>
              <span>Google</span>
            </button>

            {/* Facebook Login Button */}
            <button
              type='button'
              onClick={() => toast.info('Facebook login would be implemented here')}
              className='flex items-center justify-center rounded-lg border border-slate-700 bg-slate-800/50 px-4 py-3 text-slate-300 transition-colors hover:bg-slate-700/50'
            >
              <svg className='mr-2 h-5 w-5 text-[#1877F2]' fill='currentColor' viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg'>
                <path d='M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z' />
              </svg>
              <span>Facebook</span>
            </button>
          </div>

          {/* Footer */}
          <div className='mt-8 flex items-center justify-center space-x-2 text-center'>
            <ShieldCheck className='h-4 w-4 text-slate-500' />
            <p className='text-xs text-slate-400'>This login is for authorized staff members only</p>
          </div>
        </div>
      </motion.div>
    </div>
  );
};

export default StaffLoginForm;
