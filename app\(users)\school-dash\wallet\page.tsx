'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { format } from 'date-fns';
import { Select, SelectContent, SelectGroup, SelectItem, SelectLabel, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { KeyValueDisplay } from '@/components/dashboard/shared/misc';
import { cn } from '@/lib/utils';
import { CalendarIcon, Coins, Gem, HandCoins, MenuSquare, ScanEye, Wallet, Wallet2 } from 'lucide-react';

const DatePickerDemo = () => {
  const [date, setDate] = useState<Date>();

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button variant='outline' className={cn('w-[280px] justify-start text-left font-normal', !date && 'text-muted-foreground')}>
          <CalendarIcon className='mr-2 h-4 w-4' />
          {date ? format(date, 'PPP') : <span>Pick a date</span>}
        </Button>
      </PopoverTrigger>
      <PopoverContent className='w-auto p-0'>
        <Calendar mode='single' selected={date} onSelect={setDate} initialFocus />
      </PopoverContent>
    </Popover>
  );
};

const TutorWallet = () => {
  const [selectedTab, setSelectedTab] = useState('all');
  const transactions = [
    { id: 1, type: 'credit', title: 'Plan Purchased - Bronze', coins: '700', date: '20 August 2019', status: 'Completed' },
    { id: 2, type: 'debit', title: 'Coins Deducted - Enquiry ID 1432', coins: '50', date: '25 September 2021', status: 'Pending' },
    { id: 3, type: 'credit', title: 'Coin Reimbursed - Tutor Data 1432', coins: '50', date: '26 September 2021', status: 'Completed' },
    { id: 4, type: 'debit', title: 'Coins Deducted - Admission Leads 1532', coins: '70', date: '26 October 2021', status: 'Failed' },
  ];

  const filteredTransactions = transactions.filter((transaction) =>
    selectedTab === 'all' ? true : transaction.status.toLowerCase() === selectedTab
  );

  return (
    <div className='flex flex-col gap-4'>
      <HeaderSection />
      <div className='flex flex-col lg:flex-row items-start justify-start gap-4'>
        <div className='lg:w-3/4 grid grid-cols-1 gap-4 w-full'>
          {filteredTransactions.map((transaction) => (
            <TransactionBox key={transaction.id} transaction={transaction} />
          ))}
        </div>
        <div className='lg:w-1/4 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-1 gap-4 items-start w-full'>
          <Status />
        </div>
      </div>
    </div>
  );
};

const HeaderSection = () => (
  <div className='rounded-3xl bg-white p-6 flex flex-col md:flex-row justify-between items-center gap-4 lg:gap-0'>
    <div className='flex gap-2 items-start'>
      <Wallet size={50} strokeWidth={1.25} />
      <div className='flex flex-col items-start'>
        <h2 className='md:text-xl font-semibold'>Institute Wallet</h2>
        <p className='text-gray-500 text-sm'>View your transactions</p>
      </div>
    </div>
    <div className='flex gap-2'>
      <div className='flex gap-4 items-center'>
        <p className='text-base font-semibold bg-gray-50 text-gray-600 py-3 px-6 rounded'>Sort By</p>
        <Select>
          <SelectTrigger className='w-[180px] primary-select'>
            <SelectValue placeholder='Select a Filter' />
          </SelectTrigger>
          <SelectContent>
            <SelectGroup>
              <SelectLabel>Transaction Filter</SelectLabel>
              <SelectItem value='date'>Date</SelectItem>
              <SelectItem value='type'>Transaction Type</SelectItem>
            </SelectGroup>
          </SelectContent>
        </Select>
      </div>
    </div>
  </div>
);

const Status = () => (
  <div className='bg-white rounded-3xl p-6 w-full'>
    <h2 className='text-xl font-semibold mb-4'>Wallet Summary</h2>
    <div className='space-y-2'>
      <KeyValueDisplay label='Plan' value='Bronze' />
      <KeyValueDisplay label='Coins Utilized' value='70' />
      <KeyValueDisplay label='Coins Left' value='630' />
      <KeyValueDisplay label='Plan Purchased on' value='20 Dec 2021' />
      <KeyValueDisplay label='Plan Expiry' value='26 Dec 2021' />
    </div>
  </div>
);

const TransactionBox = ({ transaction }: { transaction: any }) => (
  <div className='rounded-3xl bg-white p-6 lg:min-h-24 flex flex-col justify-start items-start md:flex-row md:justify-between md:items-center'>
    <div className='flex gap-4 items-start'>
      <div className='bg-primaryColor-50 text-primaryColor size-12 flex items-center justify-center rounded'>
        <Coins size={25} />
      </div>
      <div className='flex flex-col'>
        <h3 className='text-base font-semibold'>{transaction.title}</h3>
        <p className='text-gray-400 text-[15px]'>{transaction.date}</p>
      </div>
    </div>
    <Link href='/tutor-dash/support/transactions/1' className='flex max-md:justify-between max-md:w-full items-center gap-12 mt-4 lg:mt-0'>
      {transaction.type === 'credit' ? (
        <p className='text-sm font-semibold text-primaryColor'>+{transaction.coins} Coins</p>
      ) : (
        <p className='text-sm font-semibold text-red-500'>-{transaction.coins} Coins</p>
      )}
      <button className='text-primaryColor'>
        <ScanEye />
      </button>
    </Link>
  </div>
);

export default TutorWallet;
