'use client';

import { Form } from '@/components/ui/form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import { toast } from 'react-toastify';
import { PrimaryInput, PrimarySwitchInput, SubmitButton } from '@/components/forms';
import { IAddUpdateFormProps } from '@/types/api';
import { useCreateCollegeSubject, useUpdateCollegeSubject } from '@/hooks/education/college.hooks';
import { ICollegeSubjectDocument } from '@/server/services/education/college.service';
import { createCollegeSubjectSchema } from '@/validation/schemas/education/college.schema';
import { extractId } from '@/validation/utils/form.utils';

interface ICollegeSubjectFormProps extends IAddUpdateFormProps<ICollegeSubjectDocument> {
  onSuccess: () => void;
  branchId?: string | null;
}

const CollegeSubjectForm: React.FC<ICollegeSubjectFormProps> = ({ data, onSuccess, branchId }) => {
  const { mutateAsync: createCollegeSubject, isPending: isCreating } = useCreateCollegeSubject();
  const { mutateAsync: updateCollegeSubject, isPending: isUpdating } = useUpdateCollegeSubject();

  const subjectId = data?._id;
  const isSubmitting = isCreating || isUpdating;

  const form = useForm<z.infer<typeof createCollegeSubjectSchema>>({
    resolver: zodResolver(createCollegeSubjectSchema),
    defaultValues: {
      name: data?.name || '',
      branch: extractId(data?.branch) || branchId || '',
      isActive: data?.isActive !== undefined ? data.isActive : true,
    },
  });

  const onSubmit = async (values: z.infer<typeof createCollegeSubjectSchema>) => {
    try {
      const result = subjectId 
        ? await updateCollegeSubject({ id: subjectId, data: values }) 
        : await createCollegeSubject(values);
        
      if (!result.success) throw new Error(result.message || 'Operation failed');

      toast.success(result.message);
      onSuccess();
    } catch (error: any) {
      toast.error(error.message || 'An error occurred');
    }
  };

  return (
    <section className='relative'>
      <div className='bg-white rounded-lg shadow-sm p-6 max-w-2xl'>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className='grid grid-cols-1 gap-6 items-start justify-start'>
            {/* Name field */}
            <PrimaryInput form={form} name='name' label='Subject Name' placeholder='Enter subject name' required />

            {/* Is Active */}
            <PrimarySwitchInput
              form={form}
              name='isActive'
              label='Is this subject active?'
              description='Making it inactive will hide it from the website.'
            />

            <SubmitButton
              submittingLabel={subjectId ? 'Updating...' : 'Creating...'}
              label={subjectId ? 'Update Subject' : 'Create Subject'}
              isSubmitting={isSubmitting}
              disabled={isSubmitting}
              className='w-fit'
            />
          </form>
        </Form>
      </div>
    </section>
  );
};

export default CollegeSubjectForm;
