'use client';

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { AlertTriangle, X, Trash2, ArrowRight } from 'lucide-react';
import { IAPIResponse } from '@/types/api';
import { toast } from 'react-toastify';

interface VisitorsDeleteModal {
  isOpen: boolean;
  onClose: () => void;
  onDelete: () => Promise<IAPIResponse>;
  itemName: string;
}

const VisitorsDeleteModal: React.FC<VisitorsDeleteModal> = ({ isOpen, onClose, onDelete, itemName }) => {
  const [isDeleting, setIsDeleting] = useState(false);
  const [deleteStep, setDeleteStep] = useState<'confirm' | 'processing' | 'success' | 'error'>('confirm');

  const handleDelete = async () => {
    setIsDeleting(true);
    setDeleteStep('processing');

    try {
      const response = await onDelete();
      if (response.success) {
        setDeleteStep('success');
        toast.success(response.message);
        // Auto close after success and reset states
        setTimeout(() => {
          onClose();
          setDeleteStep('confirm');
          setIsDeleting(false);
        }, 1500);
      } else {
        throw new Error(response.message);
      }
    } catch (error: any) {
      setDeleteStep('error');
      toast.error(error.message || 'An error occurred while deleting the item');
      // Reset to confirm state after error
      setTimeout(() => {
        setDeleteStep('confirm');
        setIsDeleting(false);
      }, 2000);
    }
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.2 }}
          className='fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50'
          onClick={() => {
            if (!isDeleting) {
              onClose();
              // Reset state when modal is closed
              setTimeout(() => {
                setDeleteStep('confirm');
                setIsDeleting(false);
              }, 300);
            }
          }}
        >
          <motion.div
            initial={{ scale: 0.95, y: 20, opacity: 0 }}
            animate={{ scale: 1, y: 0, opacity: 1 }}
            exit={{ scale: 0.95, y: 20, opacity: 0 }}
            transition={{ type: 'spring', damping: 25, stiffness: 300 }}
            className='bg-white rounded-xl overflow-hidden w-full max-w-md shadow-2xl'
            onClick={(e) => e.stopPropagation()}
          >
            {/* Red warning header */}
            <div className='bg-gradient-to-r from-red-500 to-red-600 p-6 text-white relative overflow-hidden'>
              {/* Decorative elements */}
              <div className='absolute -right-8 -top-8 w-24 h-24 rounded-full bg-white/10'></div>
              <div className='absolute right-12 top-12 w-6 h-6 rounded-full bg-white/20'></div>

              <div className='flex items-center gap-4 relative z-10'>
                <div className='w-12 h-12 rounded-full bg-white/20 flex items-center justify-center'>
                  <AlertTriangle className='text-white' size={24} />
                </div>
                <div>
                  <h2 className='text-xl font-bold'>Delete Confirmation</h2>
                  <p className='text-white/80 text-sm'>This action cannot be undone</p>
                </div>
              </div>

              {/* Close button */}
              <button
                onClick={() => {
                  if (!isDeleting) {
                    onClose();
                    // Reset state when modal is closed
                    setTimeout(() => {
                      setDeleteStep('confirm');
                      setIsDeleting(false);
                    }, 300);
                  }
                }}
                disabled={isDeleting}
                className='absolute top-4 right-4 w-8 h-8 rounded-full bg-white/20 flex items-center justify-center text-white hover:bg-white/30 transition-colors'
              >
                <X size={16} />
              </button>
            </div>

            {/* Content */}
            <div className='p-6'>
              {deleteStep === 'confirm' && (
                <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} exit={{ opacity: 0 }}>
                  <p className='text-gray-600 mb-6'>
                    Are you sure you want to delete <span className='font-semibold text-gray-800'>{itemName}</span>? This will permanently remove all
                    associated data and cannot be recovered.
                  </p>
                </motion.div>
              )}

              {deleteStep === 'processing' && (
                <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} className='flex flex-col items-center justify-center py-6'>
                  <div className='w-16 h-16 relative mb-4'>
                    <motion.div className='absolute inset-0 rounded-full border-4 border-red-100' />
                    <motion.div
                      className='absolute inset-0 rounded-full border-4 border-transparent border-t-red-500'
                      animate={{ rotate: 360 }}
                      transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
                    />
                    <div className='absolute inset-0 flex items-center justify-center'>
                      <Trash2 className='text-red-500' size={24} />
                    </div>
                  </div>
                  <p className='text-gray-700 font-medium'>Deleting...</p>
                </motion.div>
              )}

              {deleteStep === 'success' && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  className='flex flex-col items-center justify-center py-6'
                >
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ type: 'spring', damping: 10, stiffness: 100 }}
                    className='w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4'
                  >
                    <svg className='w-8 h-8 text-green-500' fill='none' stroke='currentColor' viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg'>
                      <path strokeLinecap='round' strokeLinejoin='round' strokeWidth={3} d='M5 13l4 4L19 7' />
                    </svg>
                  </motion.div>
                  <p className='text-gray-700 font-medium'>Successfully deleted!</p>
                </motion.div>
              )}

              {deleteStep === 'error' && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  className='flex flex-col items-center justify-center py-6'
                >
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ type: 'spring', damping: 10, stiffness: 100 }}
                    className='w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-4'
                  >
                    <svg className='w-8 h-8 text-red-500' fill='none' stroke='currentColor' viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg'>
                      <path strokeLinecap='round' strokeLinejoin='round' strokeWidth={3} d='M6 18L18 6M6 6l12 12' />
                    </svg>
                  </motion.div>
                  <p className='text-gray-700 font-medium'>Failed to delete. Please try again.</p>
                </motion.div>
              )}

              {/* Action buttons */}
              {deleteStep === 'confirm' && (
                <div className='flex justify-end gap-3 mt-6'>
                  <button
                    onClick={() => {
                      onClose();
                      // Reset state when modal is closed
                      setTimeout(() => {
                        setDeleteStep('confirm');
                        setIsDeleting(false);
                      }, 300);
                    }}
                    disabled={isDeleting}
                    className='px-4 py-2 border border-gray-200 text-gray-700 rounded-lg text-sm font-medium hover:bg-gray-50 transition-colors'
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleDelete}
                    disabled={isDeleting}
                    className='px-4 py-2 bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white rounded-lg text-sm font-medium transition-colors flex items-center gap-2'
                  >
                    <Trash2 size={16} />
                    <span>Delete</span>
                  </button>
                </div>
              )}

              {deleteStep === 'success' && (
                <div className='flex justify-center mt-4'>
                  <button
                    onClick={() => {
                      onClose();
                      // Reset state when modal is closed
                      setTimeout(() => {
                        setDeleteStep('confirm');
                        setIsDeleting(false);
                      }, 300);
                    }}
                    className='px-4 py-2 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white rounded-lg text-sm font-medium transition-colors flex items-center gap-2'
                  >
                    <span>Continue</span>
                    <ArrowRight size={16} />
                  </button>
                </div>
              )}

              {deleteStep === 'error' && (
                <div className='flex justify-center mt-4'>
                  <button
                    onClick={() => setDeleteStep('confirm')}
                    className='px-4 py-2 bg-gradient-to-r from-gray-500 to-gray-600 hover:from-gray-600 hover:to-gray-700 text-white rounded-lg text-sm font-medium transition-colors'
                  >
                    Try Again
                  </button>
                </div>
              )}
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default VisitorsDeleteModal;
