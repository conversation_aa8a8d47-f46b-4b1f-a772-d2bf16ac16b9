'use client';

import React, { useEffect, useState, useRef } from 'react';
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Loader2, MapPin } from 'lucide-react';
import { UseFormReturn } from 'react-hook-form';
import Script from 'next/script';

declare global {
  interface Window {
    google: typeof google;
    initAutocomplete: () => void;
  }
}

interface AddressComponent {
  long_name: string;
  short_name: string;
  types: string[];
}

export interface IAddressOutput {
  areaPinCode?: string;
  city?: string;
  district?: string;
  state?: string;
  country?: string;
  lat?: number;
  lng?: number;
}

interface PrimaryLocationInputProps {
  setParsedAddress?: (address: IAddressOutput) => void;
  form: UseFormReturn<any>;
  name: string;
  label?: string;
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  className?: string;
  onFocus?: () => void;
}

const PrimaryLocationInput = ({
  setParsedAddress,
  form,
  name,
  label,
  placeholder = 'Enter your location',
  required = false,
  disabled = false,
  className = '',
  onFocus,
}: PrimaryLocationInputProps) => {
  const [isLoading, setIsLoading] = useState(false);
  const [scriptLoaded, setScriptLoaded] = useState(false);
  const autocompleteRef = useRef<HTMLInputElement>(null);
  const autocompleteInstance = useRef<any>(null);

  const initAutocomplete = () => {
    if (!autocompleteRef.current || !window.google) return;

    try {
      setIsLoading(true);
      const options = {
        componentRestrictions: { country: 'in' }, // Restrict to India
        fields: ['address_components', 'formatted_address', 'geometry', 'name'],
        types: ['geocode', 'establishment'],
      };

      autocompleteInstance.current = new window.google.maps.places.Autocomplete(autocompleteRef.current, options);

      autocompleteInstance.current?.addListener('place_changed', () => {
        const place = autocompleteInstance.current?.getPlace();
        if (!place?.formatted_address) return;

        const { lat, lng } = place.geometry?.location?.toJSON();

        const addressMap = {
          postal_code: 'areaPinCode',
          locality: 'city',
          administrative_area_level_2: 'district',
          administrative_area_level_1: 'state',
          country: 'country',
        } as const;

        const address: IAddressOutput = { lat, lng };

        if (place.address_components) {
          place.address_components.forEach((component: AddressComponent) => {
            for (const [googleType, addressField] of Object.entries(addressMap)) {
              if (component.types.includes(googleType)) {
                address[addressField] = component.long_name;
              }
            }
          });
        }

        setParsedAddress?.(address);
        form.setValue(name, place.formatted_address, { shouldValidate: true, shouldDirty: true, shouldTouch: true });
      });
      setIsLoading(false);
    } catch (error) {
      console.error('Error initializing Google Maps Autocomplete:', error);
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (scriptLoaded || window.google?.maps?.places) {
      initAutocomplete();
    }

    return () => {
      if (autocompleteInstance.current) {
        window.google?.maps?.event?.clearInstanceListeners(autocompleteInstance.current);
      }
    };
  }, [scriptLoaded, name, form, setParsedAddress]);

  useEffect(() => {
    window.initAutocomplete = initAutocomplete;
  }, []);

  return (
    <>
      <Script
        src={`https://maps.googleapis.com/maps/api/js?key=${process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY}&libraries=places&callback=initAutocomplete`}
        onLoad={() => setScriptLoaded(true)}
        strategy='afterInteractive'
        async
      />
      <FormField
        control={form.control}
        name={name}
        render={({ field }) => (
          <FormItem className={className || 'w-full'}>
            {label && (
              <FormLabel className='primary-label'>
                <span>{label}</span>
                {required && <span className='text-primaryColor'>*</span>}
              </FormLabel>
            )}
            <FormControl>
              <div className='relative'>
                <div className='absolute left-3 top-1/2 -translate-y-1/2 pointer-events-none z-10'>
                  <MapPin className='h-5 w-5 text-gray-400' />
                </div>
                <Input
                  {...field}
                  ref={autocompleteRef}
                  placeholder={placeholder}
                  disabled={disabled || isLoading}
                  className='primary-input pl-10'
                  onFocus={onFocus}
                />
                {isLoading && (
                  <div className='absolute right-3 top-1/2 -translate-y-1/2'>
                    <Loader2 className='h-4 w-4 animate-spin text-muted-foreground' />
                  </div>
                )}
              </div>
            </FormControl>
            {field.value && <div className='text-xs text-gray-500 mt-2'>{field.value}</div>}
            <FormMessage />
          </FormItem>
        )}
      />
    </>
  );
};

export default PrimaryLocationInput;
