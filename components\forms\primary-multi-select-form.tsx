'use client';

import React, { useState, useRef, useEffect } from 'react';
import { FormField, FormItem, FormControl, FormLabel, FormMessage } from '@/components/ui/form';
import { createPortal } from 'react-dom';
import { ChevronDown, Check, X, Search } from 'lucide-react';
import type { UseFormReturn, FieldValues, Path } from 'react-hook-form';
import { cn } from '@/lib/utils';
import { VARIANT_THEME_CLASSES } from './form.constants';
import { Input } from '@/components/ui/input';

interface Option {
  value: string;
  label: string;
  id?: string;
}

interface PrimaryMultiSelectFormProps<TFieldValues extends FieldValues> {
  form: UseFormReturn<TFieldValues>;
  name: Path<TFieldValues>;
  label: string;
  options: Option[];
  required?: boolean;
  placeholder?: string;
  disabled?: boolean;
  variant?: 'primary' | 'secondary' | 'green' | 'purple';
  isLoading?: boolean;
  searchPlaceholder?: string;
}

const PrimaryMultiSelectForm = <TFieldValues extends FieldValues>({
  form,
  name,
  label,
  options,
  required = false,
  placeholder = 'Select options',
  disabled = false,
  variant = 'primary',
  isLoading = false,
  searchPlaceholder = 'Search options...',
}: PrimaryMultiSelectFormProps<TFieldValues>) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [portalContainer, setPortalContainer] = useState<HTMLElement | null>(null);
  const triggerRef = useRef<HTMLDivElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const [dropdownPosition, setDropdownPosition] = useState({ top: 0, left: 0, width: 0 });

  useEffect(() => {
    if (typeof window !== 'undefined') {
      let container = document.getElementById('multi-select-dropdown-portal');
      if (!container) {
        container = document.createElement('div');
        container.id = 'multi-select-dropdown-portal';
        document.body.appendChild(container);
      }
      setPortalContainer(container);
    }

    return () => {
      const container = document.getElementById('multi-select-dropdown-portal');
      if (container && container.childNodes.length === 0) {
        document.body.removeChild(container);
      }
    };
  }, []);

  const filteredOptions = options.filter((option) => option.label.toLowerCase().includes(searchTerm.toLowerCase()));

  const toggleOption = (value: string, currentValues: string[]) => {
    return currentValues.includes(value) ? currentValues.filter((v) => v !== value) : [...currentValues, value];
  };

  const findScrollableParents = () => {
    if (!triggerRef.current) return [];

    const scrollableParents: HTMLElement[] = [];
    let parent = triggerRef.current.parentElement;

    while (parent) {
      const style = window.getComputedStyle(parent);
      const overflow = style.getPropertyValue('overflow');
      const overflowY = style.getPropertyValue('overflow-y');

      if (overflow === 'auto' || overflow === 'scroll' || overflowY === 'auto' || overflowY === 'scroll') {
        scrollableParents.push(parent);
      }
      parent = parent.parentElement;
    }

    return scrollableParents;
  };

  useEffect(() => {
    if (isOpen && triggerRef.current) {
      const updatePosition = () => {
        const rect = triggerRef.current?.getBoundingClientRect();
        if (rect) {
          const viewportHeight = window.innerHeight;
          const viewportWidth = window.innerWidth;
          const dropdownHeight = 300;

          let top = rect.bottom;
          if (rect.bottom + dropdownHeight > viewportHeight) {
            top = Math.max(0, rect.top - dropdownHeight);
          }

          let left = rect.left;
          const dropdownWidth = rect.width;
          if (left + dropdownWidth > viewportWidth) {
            left = Math.max(0, viewportWidth - dropdownWidth - 10);
          }

          setDropdownPosition({ top: top, left: left, width: rect.width });
        }
      };

      updatePosition();

      window.addEventListener('scroll', updatePosition, true);
      window.addEventListener('resize', updatePosition);

      const scrollableParents = findScrollableParents();
      scrollableParents.forEach((parent) => {
        parent.addEventListener('scroll', updatePosition, { passive: true });
      });

      return () => {
        window.removeEventListener('scroll', updatePosition, true);
        window.removeEventListener('resize', updatePosition);
        scrollableParents.forEach((parent) => {
          parent.removeEventListener('scroll', updatePosition);
        });
      };
    }
  }, [isOpen]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        triggerRef.current &&
        !triggerRef.current.contains(event.target as Node) &&
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    const handleParentScroll = () => {
      if (isOpen) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);

    const scrollableParents = findScrollableParents();
    scrollableParents.forEach((parent) => {
      parent.addEventListener('scroll', handleParentScroll, { passive: true });
    });

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      scrollableParents.forEach((parent) => {
        parent.removeEventListener('scroll', handleParentScroll);
      });
    };
  }, [isOpen]);

  const handleSelectAll = (currentValues: string[]) => {
    if (currentValues.length === filteredOptions.length) {
      return [];
    } else {
      return filteredOptions.map((option) => option.value);
    }
  };

  const theme = VARIANT_THEME_CLASSES[variant];

  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => {
        const selectedValues: string[] = Array.isArray(field.value) ? field.value : [];
        const selectedOptions = options.filter((option) => selectedValues.includes(option.value));

        return (
          <FormItem className='w-full'>
            <FormLabel className='primary-label'>
              <span>{label}</span>
              {required && <span className='text-primaryColor'>*</span>}
            </FormLabel>
            <FormControl>
              {isLoading ? (
                <div className='flex items-center justify-center h-10 bg-gray-100 rounded-md'>
                  <div className='w-5 h-5 border-2 border-primaryColor-500 border-t-transparent rounded-full animate-spin'></div>
                </div>
              ) : (
                <div className='relative'>
                  <div
                    ref={triggerRef}
                    className={cn(
                      'w-full min-h-10 p-2 !py-3 border rounded-md flex flex-wrap gap-2 cursor-pointer relative',
                      theme.ring,
                      disabled && 'opacity-50 cursor-not-allowed'
                    )}
                    onClick={() => !disabled && setIsOpen(!isOpen)}
                  >
                    {selectedOptions.length > 0 ? (
                      <div className='flex flex-wrap gap-1.5'>
                        {selectedOptions.map((option) => (
                          <div
                            key={option.value}
                            className={cn('flex items-center gap-1 px-2 py-0.5 rounded text-sm', theme.bg, theme.text)}
                            onClick={(e) => e.stopPropagation()}
                          >
                            <span>{option.label}</span>
                            <button
                              type='button'
                              className={cn(theme.check, theme.checkHover)}
                              onClick={(e) => {
                                e.stopPropagation();
                                field.onChange(toggleOption(option.value, selectedValues));
                              }}
                            >
                              <X size={14} />
                            </button>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <span className='text-gray-500 text-sm pl-2'>{placeholder}</span>
                    )}
                    <div className='absolute right-2 top-1/2 -translate-y-1/2'>
                      <ChevronDown size={18} className={`text-gray-500 transition-transform duration-200 ${isOpen ? 'transform rotate-180' : ''}`} />
                    </div>
                  </div>

                  {isOpen &&
                    portalContainer &&
                    createPortal(
                      <div
                        ref={dropdownRef}
                        className='fixed z-[9999] bg-white border border-gray-200 rounded-md shadow-lg overflow-y-auto'
                        style={{
                          position: 'fixed',
                          top: `${dropdownPosition.top}px`,
                          left: `${dropdownPosition.left}px`,
                          width: `${dropdownPosition.width}px`,
                          maxHeight: '300px',
                          maxWidth: '95vw',
                          transform: 'translateZ(0)',
                        }}
                      >
                        {/* Search input */}
                        <div className='p-2 border-b border-gray-200'>
                          <div className='relative'>
                            <Search className='absolute left-2 top-2.5 h-4 w-4 text-gray-400' />
                            <Input
                              className='pl-8 h-9'
                              placeholder={searchPlaceholder}
                              value={searchTerm}
                              onChange={(e: React.ChangeEvent<HTMLInputElement>) => setSearchTerm(e.target.value)}
                              onClick={(e: React.MouseEvent) => e.stopPropagation()}
                            />
                          </div>
                        </div>

                        {/* Select All option */}
                        <div className='p-2 border-b border-gray-200'>
                          <button
                            type='button'
                            className={cn(
                              'w-full flex items-center gap-3 px-3 py-2 rounded-lg text-sm font-medium transition-colors justify-start hover:bg-gray-100 text-gray-700'
                            )}
                            onClick={(e) => {
                              e.stopPropagation();
                              field.onChange(handleSelectAll(selectedValues));
                            }}
                          >
                            <span className='flex items-center gap-2'>
                              <span className='flex items-center justify-center w-5 h-5 rounded-md bg-gray-100 border border-gray-200'>
                                {selectedValues.length === filteredOptions.length ? (
                                  <X className='h-3.5 w-3.5 text-gray-500' />
                                ) : (
                                  <Check className='h-3.5 w-3.5 text-gray-500' />
                                )}
                              </span>
                              <span>{selectedValues.length === filteredOptions.length ? 'Deselect All' : 'Select All'}</span>
                            </span>
                          </button>
                        </div>

                        {/* Options list */}
                        <div className='p-2'>
                          {filteredOptions.length > 0 ? (
                            <div className='flex flex-wrap gap-2'>
                              {filteredOptions.map((option) => (
                                <label
                                  key={option.value}
                                  className={cn(
                                    'flex items-center gap-2 px-3 py-2 rounded-lg border cursor-pointer transition-colors',
                                    selectedValues.includes(option.value)
                                      ? cn(theme.bg, theme.border, theme.text)
                                      : 'bg-gray-50 border-gray-200 text-gray-700 hover:bg-gray-100'
                                  )}
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    field.onChange(toggleOption(option.value, selectedValues));
                                  }}
                                >
                                  <input
                                    type='checkbox'
                                    className={cn('h-4 w-4 rounded border-gray-300', theme.checkbox)}
                                    checked={selectedValues.includes(option.value)}
                                    onChange={() => {}}
                                    onClick={(e) => e.stopPropagation()}
                                  />
                                  <span className='text-sm'>{option.label}</span>
                                </label>
                              ))}
                            </div>
                          ) : (
                            <div className='text-center py-4 text-gray-500'>No options found</div>
                          )}
                        </div>
                      </div>,
                      portalContainer
                    )}
                </div>
              )}
            </FormControl>
            <FormMessage />
          </FormItem>
        );
      }}
    />
  );
};

export default PrimaryMultiSelectForm;
