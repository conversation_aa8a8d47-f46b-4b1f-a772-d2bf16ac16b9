'use client';

import Image from 'next/image';
import { KeyValueDisplay } from '@/components/dashboard/shared/misc';

const TutorTuitionLeadViewPage = () => {
  return (
    <section className='flex gap-8 justify-start items-start'>
      {/* Left Column */}
      <div className='w-[25%] flex flex-col items-start gap-8'>
        {/* Avatar and Details */}
        <div className='flex flex-col justify-start items-start gap-4 bg-white rounded-3xl p-6 relative'>
          <div className='absolute top-0 left-0 transform bg-gradient-1 uppercase tracking-wider text-white text-xs font-semibold px-5 py-1.5 rounded-tl-3xl rounded-br-3xl'>
            #14571
          </div>

          <div className='absolute top-0 right-0 transform bg-gradient-1 uppercase tracking-wider text-white text-xs font-semibold px-5 py-1.5 rounded-bl-3xl rounded-tr-3xl'>
            60 Coins
          </div>
          <div className='flex justify-center mt-4'>
            <Image
              alt='avatar'
              src='/temp/avatar.jpg'
              height={240}
              width={240}
              className='max-w-60 h-auto object-contain object-center rounded-3xl'
            />
          </div>
          <button className='btn-default__outline-sm w-full justify-center !rounded-3xl'>Add to Favorites</button>
          <button className='btn-default-sm w-full justify-center !rounded-3xl'>Get Contact Number</button>
        </div>
      </div>

      {/* Right Column */}
      <div className='w-[75%] flex flex-col items-start gap-8'>
        {/* User Info */}
        <div className='bg-white rounded-3xl p-6 w-full'>
          <div className='flex items-start justify-between mb-4'>
            <h2 className='text-xl font-semibold'>About Gaurav</h2>
            <span className='px-6 py-1.5 rounded-full text-sm bg-teal-500 text-white'>Active</span>
          </div>
          <div className='grid grid-cols-2 gap-4'>
            <KeyValueDisplay label='Enq ID' value='14571' />
            <KeyValueDisplay label='Class' value='Class VIII' />
            <KeyValueDisplay label='Board' value='CBSE' />
            <KeyValueDisplay label='Mobile' value='91 XXXXXXXX94' />
            <KeyValueDisplay label='Location' value='India' />
            <KeyValueDisplay label='Landmark' value='N/A' />
          </div>
        </div>

        {/* General Information */}
        <div className='bg-white rounded-3xl p-6 w-full relative'>
          <h2 className='text-xl font-semibold mb-4'>Enquiry details</h2>
          <div className='grid grid-cols-2 gap-4'>
            <KeyValueDisplay label='Coins Required' value='60' />
            <KeyValueDisplay label='Mode of delivery' value='Online' />
            <KeyValueDisplay label='No. of sessions in a week' value='Two In a Week' />
            <KeyValueDisplay label='Budget' value='400 Per Hours' />
            <KeyValueDisplay label='Gender preference' value='Any' />
            <KeyValueDisplay label='When to start' value='Immediately' />
            <KeyValueDisplay label='Distance' value='0 km approx.' />
            <KeyValueDisplay label='Subjects' value='Computers' />
          </div>
        </div>
      </div>
    </section>
  );
};

export default TutorTuitionLeadViewPage;
