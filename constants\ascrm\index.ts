export const ADMIN_DASHBOARD_PATH = '/ascrm';

export const gendersMap = { male: { label: 'Male', key: 'male' }, female: { label: 'Female', key: 'female' } };

import type { IconType } from 'react-icons';
import {
  RiDashboardLine,
  RiMailLine,
  RiFileTextLine,
  RiUserLine,
  RiBuildingLine,
  RiSchoolLine,
  RiUserAddLine,
  RiFileList3Line,
  RiMailAddLine,
  RiBriefcaseLine,
  RiUserSettingsLine,
  RiWalletLine,
  RiMessage3Line,
  RiTeamLine,
  RiUpload2Line,
  RiGroupLine,
  RiLockPasswordLine,
  RiStarLine,
  RiShieldUserLine,
  RiCheckboxCircleLine,
  RiBarChartLine,
  RiUserStarLine,
  RiGalleryLine,
  RiLayoutLine,
  RiStackLine,
  RiBookmarkLine,
  RiLinkM,
  RiCoupon3Line,
  RiDatabase2Line,
  RiBellLine,
  RiCalendarLine,
  RiAlarmWarningLine,
  RiLogoutBoxRLine,
  RiMoneyDollarCircleLine,
  RiBookOpenLine,
  RiListCheck2,
  RiBookReadLine,
  RiGraduationCapLine,
  RiGitBranchLine,
} from 'react-icons/ri';

import { MdOutlineTableChart } from 'react-icons/md';

import {
  Bell,
  Book,
  Calendar,
  ClipboardIcon,
  FileIcon,
  FileText,
  Folder,
  HelpCircle,
  Home,
  LayoutDashboard,
  Package,
  User,
  UserCircle,
  Users,
} from 'lucide-react';

export interface NavigationLinkProps {
  title: string;
  href: string;
  icon: IconType;
}

export interface NavigationLinksGroupProps {
  id: string;
  label: string;
  links: NavigationLinkProps[];
}

export const navigationLinks: NavigationLinksGroupProps[] = [
  {
    id: 'dashboard',
    label: 'Dashboard',
    links: [{ title: 'Dashboard', href: '/', icon: RiDashboardLine }],
  },
  {
    id: 'live-data',
    label: 'Live Data',
    links: [
      { title: 'Contact Us', href: '/live-data/contact-us', icon: RiMailLine },
      { title: 'Blogs & Forms', href: '/live-data/blogs-forms', icon: RiFileTextLine },
      { title: 'Tutor Accounts', href: '/live-data/tutor-accounts', icon: RiUserLine },
      { title: 'Institutes', href: '/live-data/institutes', icon: RiBuildingLine },
      { title: 'Schools/Colleges', href: '/live-data/schools-colleges', icon: RiSchoolLine },
      { title: 'Students', href: '/live-data/students', icon: RiUserAddLine },
      { title: 'Student Enquiries', href: '/live-data/student-enquiries', icon: RiFileList3Line },
      { title: 'Admission Enquiries', href: '/live-data/admission-enquiries', icon: RiBarChartLine },
      { title: 'Email Subscribers', href: '/live-data/email-subscribers', icon: RiMailAddLine },
      { title: 'Jobs', href: '/live-data/jobs', icon: RiBriefcaseLine },
      { title: 'Tutor Profile Enquiries', href: '/live-data/tutor-profile-enquiries', icon: RiUserSettingsLine },
    ],
  },
  {
    id: 'billing',
    label: 'Billing',
    links: [
      { title: 'User Registrations', href: '/billing/user-registrations', icon: RiUserAddLine },
      { title: 'Fees Collections', href: '/billing/fees-collections', icon: RiMoneyDollarCircleLine },
    ],
  },
  {
    id: 'wallets',
    label: 'Wallets',
    links: [
      { title: 'Tutor Wallet', href: '/wallets/tutor', icon: RiWalletLine },
      { title: 'Institute Wallet', href: '/wallets/institute', icon: RiWalletLine },
      { title: 'School/College Wallet', href: '/wallets/school-college', icon: RiWalletLine },
    ],
  },
  {
    id: 'complaints',
    label: 'Complaints',
    links: [
      { title: 'Tutor Complaints', href: '/complaints/tutor', icon: RiMessage3Line },
      { title: 'Student/Parent Complaints', href: '/complaints/student-parent', icon: RiMessage3Line },
      { title: 'School/College Complaints', href: '/complaints/school-college', icon: RiMessage3Line },
      { title: 'Institute Complaints', href: '/complaints/institute', icon: RiMessage3Line },
    ],
  },
  {
    id: 'batches',
    label: 'Batches',
    links: [
      { title: 'Tutor & Student 1-on-1', href: '/batches/tutor-student', icon: RiTeamLine },
      { title: 'Institute Classroom', href: '/batches/institute', icon: RiTeamLine },
      { title: 'School/College Classroom', href: '/batches/school-college', icon: RiTeamLine },
    ],
  },
  {
    id: 'approvals',
    label: 'Approvals',
    links: [
      { title: 'User Account Status', href: '/approvals/user-account', icon: RiCheckboxCircleLine },
      { title: 'Plan Activation/Changes', href: '/approvals/plan', icon: RiBarChartLine },
      { title: 'Batch Status', href: '/approvals/batch', icon: RiBarChartLine },
      { title: 'User Raised Approvals', href: '/approvals/user-raised', icon: RiUserStarLine },
    ],
  },
  {
    id: 'upload-data',
    label: 'Upload Bulk Data',
    links: [
      { title: 'Tutor Data', href: '/upload-data/tutor', icon: RiUpload2Line },
      { title: 'Parent Data', href: '/upload-data/parent', icon: RiUpload2Line },
      { title: 'Institute Data', href: '/upload-data/institute', icon: RiUpload2Line },
      { title: 'School/College Data', href: '/upload-data/school-college', icon: RiUpload2Line },
    ],
  },
  {
    id: 'reviews',
    label: 'Reviews',
    links: [
      { title: 'Perfect Tutor Reviews', href: '/reviews/perfect-tutor', icon: RiStarLine },
      { title: 'Parent-Tutor Reviews', href: '/reviews/parent-tutor', icon: RiStarLine },
      { title: 'Tutor-Parent Reviews', href: '/reviews/tutor-parent', icon: RiStarLine },
      { title: 'Parent-School/College/Institute Reviews', href: '/reviews/parent-institution', icon: RiStarLine },
    ],
  },
  {
    id: 'fraud-users',
    label: 'Fraud Users',
    links: [
      { title: 'Tutor Fraud', href: '/fraud-users/tutor', icon: RiShieldUserLine },
      { title: 'Parent Fraud', href: '/fraud-users/parent', icon: RiShieldUserLine },
      { title: 'Institute Fraud', href: '/fraud-users/institute', icon: RiShieldUserLine },
      { title: 'School/College Fraud', href: '/fraud-users/school-college', icon: RiShieldUserLine },
    ],
  },
  {
    id: 'manage-staff',
    label: 'Manage Staff',
    links: [{ title: 'Manage Staff', href: '/manage-staff', icon: RiGroupLine }],
  },
  {
    id: 'group-permissions',
    label: 'Group Permissions',
    links: [{ title: 'Manage Permissions & Roles', href: '/group-permissions', icon: RiLockPasswordLine }],
  },
  {
    id: 'education',
    label: 'Education',
    links: [
      { title: 'Service Categories', href: '/service-categories', icon: RiBookOpenLine },
      // School
      { title: 'Boards', href: '/boards', icon: RiListCheck2 },
      { title: 'Classes', href: '/classes', icon: RiBookReadLine },
      { title: 'Subjects', href: '/subjects', icon: RiGraduationCapLine },
      // College
      { title: 'Streams', href: '/streams', icon: RiStackLine },
      { title: 'Degree Levels', href: '/degree-levels', icon: RiGraduationCapLine },
      { title: 'Degrees', href: '/degrees', icon: RiBookmarkLine },
      { title: 'Branches', href: '/branches', icon: RiGitBranchLine },
      { title: 'College Subjects', href: '/college-subjects', icon: RiBookOpenLine },
    ],
  },
  {
    id: 'cms',
    label: 'CMS',
    links: [
      { title: 'Blogs', href: '/cms/blogs', icon: RiFileTextLine },
      { title: 'Gallery', href: '/cms/gallery', icon: RiGalleryLine },
      { title: 'Landing Pages', href: '/cms/landing-pages', icon: RiLayoutLine },
      { title: 'S1-S4 Pages', href: '/cms/s1-s4', icon: RiStackLine },
      { title: 'Job Skills', href: '/cms/job-skills', icon: RiBookmarkLine },
      { title: 'Social Links', href: '/cms/social-links', icon: RiLinkM },
      { title: 'Coupons', href: '/cms/coupons', icon: RiCoupon3Line },
    ],
  },
  {
    id: 'data-allotment',
    label: 'Data Allotment',
    links: [{ title: 'Staff Data Allotment', href: '/data-allotment', icon: RiDatabase2Line }],
  },
  {
    id: 'reminders',
    label: 'Reminders',
    links: [
      { title: 'Admin Reminders', href: '/reminders/admin', icon: RiBellLine },
      { title: 'Staff Personal Reminders', href: '/reminders/staff-personal', icon: RiCalendarLine },
      { title: 'Staff Work Reminders', href: '/reminders/staff-work', icon: RiAlarmWarningLine },
    ],
  },
  {
    id: 'staff-roles',
    label: 'Staff Roles',
    links: [
      { title: 'Sales Team', href: '/staff-roles/sales', icon: RiTeamLine },
      { title: 'Coordination Team', href: '/staff-roles/coordination', icon: RiTeamLine },
      { title: 'Data Entry Team', href: '/staff-roles/data-entry', icon: MdOutlineTableChart },
    ],
  },
  {
    id: 'logout',
    label: 'Logout',
    links: [{ title: 'Logout', href: '/logout', icon: RiLogoutBoxRLine }],
  },
];

export const bottomBarLinks = [
  {
    id: 'home',
    label: 'Home',
    icon: Home,
    links: [
      { title: 'Dashboard', href: '/', icon: LayoutDashboard },
      { title: 'Tuition Leads', href: 'leads', icon: Book },
      { title: 'Jobs', href: 'jobs', icon: Package },
    ],
  },
  {
    id: 'profiles',
    label: 'Profiles',
    icon: UserCircle,
    links: [
      { title: 'Basic', href: 'profiles/basic', icon: User },
      { title: 'Tuition', href: 'profiles/tuition', icon: Users },
      { title: 'KYC', href: 'profiles/kyc', icon: FileIcon },
      { title: 'Qualification', href: 'profiles/qualification', icon: ClipboardIcon },
      { title: 'Jobs', href: 'profiles/jobs', icon: Package },
    ],
  },
  {
    id: 'batches',
    label: 'Batches',
    icon: Calendar,
    links: [
      { title: 'Batches', href: 'batches', icon: Calendar },
      { title: 'Wallet', href: 'wallet', icon: Folder },
      { title: 'Plans', href: 'plans', icon: FileText },
    ],
  },
  {
    id: 'support',
    label: 'Support',
    icon: HelpCircle,

    links: [
      { title: 'Complaints', href: 'support/complaints', icon: HelpCircle },
      { title: 'Notifications', href: 'support/notifications', icon: Bell },
    ],
  },
];
