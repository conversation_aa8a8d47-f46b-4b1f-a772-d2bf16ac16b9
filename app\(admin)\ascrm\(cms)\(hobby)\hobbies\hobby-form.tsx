'use client';

import { Form } from '@/components/ui/form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import { toast } from 'react-toastify';
import { PrimaryInput, PrimarySwitchInput, SubmitButton } from '@/components/forms';
import { IAddUpdateFormProps } from '@/types/api';
import { useCreateHobby, useUpdateHobby } from '@/hooks/education/hobby.hooks';
import { IHobbyDocument } from '@/server/services/education/hobby.service';
import { createHobbySchema } from '@/validation/schemas/education/hobby.schema';
import { extractId } from '@/validation/utils/form.utils';

interface IHobbyFormProps extends IAddUpdateFormProps<IHobbyDocument> {
  onSuccess: () => void;
  hobbyTypeId?: string | null;
}

const HobbyForm: React.FC<IHobbyFormProps> = ({ data, onSuccess, hobbyTypeId }) => {
  const { mutateAsync: createHobby, isPending: isCreating } = useCreateHobby();
  const { mutateAsync: updateHobby, isPending: isUpdating } = useUpdateHobby();

  const hobbyId = data?._id;
  const isSubmitting = isCreating || isUpdating;

  const form = useForm<z.infer<typeof createHobbySchema>>({
    resolver: zodResolver(createHobbySchema),
    defaultValues: {
      name: data?.name || '',
      hobbyType: extractId(data?.hobbyType) || hobbyTypeId || '',
      isActive: data?.isActive !== undefined ? data.isActive : true,
    },
  });

  const onSubmit = async (values: z.infer<typeof createHobbySchema>) => {
    try {
      const result = hobbyId 
        ? await updateHobby({ id: hobbyId, data: values }) 
        : await createHobby(values);
        
      if (!result.success) throw new Error(result.message || 'Operation failed');

      toast.success(result.message);
      onSuccess();
    } catch (error: any) {
      toast.error(error.message || 'An error occurred');
    }
  };

  return (
    <section className='relative'>
      <div className='bg-white rounded-lg shadow-sm p-6 max-w-2xl'>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className='grid grid-cols-1 gap-6 items-start justify-start'>
            {/* Name field */}
            <PrimaryInput form={form} name='name' label='Hobby Name' placeholder='Enter hobby name' required />

            {/* Is Active */}
            <PrimarySwitchInput
              form={form}
              name='isActive'
              label='Is this hobby active?'
              description='Making it inactive will hide it from the website.'
            />

            <SubmitButton
              submittingLabel={hobbyId ? 'Updating...' : 'Creating...'}
              label={hobbyId ? 'Update Hobby' : 'Create Hobby'}
              isSubmitting={isSubmitting}
              disabled={isSubmitting}
              className='w-fit'
            />
          </form>
        </Form>
      </div>
    </section>
  );
};

export default HobbyForm;
