import { filterTypeMaps } from '@/constants';
import { IGenderMap, IAccountStatusMap, IAdminRolesMap, IUserTypeMap } from '@/validation/schemas/maps';
import { DateRange } from 'react-day-picker';

export interface IAddUpdateFormProps<T> {
  data?: T;
}

export interface IPagination {
  totalResults: number;
  totalPages: number;
  currentPage: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
}

export interface ISessionUser {
  userId: string;
  userType: IUserTypeMap;
  fullName: string;
  profilePicture?: string;
  dateOfBirth?: Date;
  gender?: IGenderMap;
  email?: string;
  phone?: string;
  accountStatus: IAccountStatusMap;
}

export interface ISessionStaff {
  staffId: string;
  fullName: string;
  email: string;
  role: IAdminRolesMap;
  profilePicture?: string;
  phone?: string;
  isActive: boolean;
}

export interface IAPIResponse<T = any> {
  success: boolean;
  message: string;
  statusCode: number;
  data?: T;
}

export interface APIQueryParams {
  page?: number;
  limit?: number;
  [key: string]: any;
}

export interface FilterOption {
  type: keyof typeof filterTypeMaps;
  label: string;
  key: string;
  options?: { value: string; label: string }[];
}

export interface FilterValues {
  [key: string]: string | DateRange | string[] | undefined;
}

export interface MasterFilterProps {
  filterOptions: FilterOption[];
  onApplyFilters: (filters: FilterValues) => void;
  onResetFilters: () => void;
  initialFilters?: FilterValues;
}

export interface ITable {
  headers: string[];
  rows: Array<{
    id: string;
    values: React.ReactNode[];
  }>;
  pagination?: IPagination;
  isLoading?: boolean;
}
