import React from 'react';
import { KeyValueDisplay } from '@/components/dashboard/shared/misc';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';

interface KeyValueGridProps {
  data?: Record<string, any>;
  rows?: number;
  className?: string;
}

const keyMap: Record<string, string> = {
  dateOfBirth: 'Date of Birth',
  primaryWhatsApp: 'Primary WhatsApp',
  alternativeWhatsApp: 'Alternative WhatsApp',
  alternativeMobile: 'Alternative Mobile',
  'User ID/Referral Code': 'User ID/Referral Code',
};

const formatKey = (key: string): string => keyMap[key] || key.charAt(0).toUpperCase() + key.slice(1).replace(/([A-Z])/g, ' $1');

const formatDateOfBirth = (date: Date): string => format(date, 'PPP');

const formatValue = (key: string, value: any): string => {
  if (key === 'dateOfBirth') return formatDateOfBirth(value);
  if (typeof value === 'string' && (value.toLowerCase() === 'student' || value.includes('_'))) {
    return value
      .split('_')
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ');
  }

  if (typeof value === 'string' && /phone|mobile|whatsapp/i.test(key)) {
    return value.startsWith('+') ? value : `+${value}`;
  }

  if (typeof value === 'number') {
    return String(value);
  }

  if (typeof value === 'boolean') {
    return value ? 'Yes' : 'No';
  }

  if (typeof value !== 'string') {
    return 'Something not this';
  }

  return value;
};

const KeyValueGrid: React.FC<KeyValueGridProps> = ({ data, rows = 2, className = '' }) => {
  if (!data) return null;

  const entries = Object.entries(data).filter(([_, value]) => value !== undefined && value !== null);

  if (entries.length === 0) {
    return <div className='text-gray-500 italic py-4'>No data available</div>;
  }

  return (
    <div className={cn(`grid gap-4`, rows === 1 ? 'grid-cols-1' : 'grid-cols-2', className)}>
      {entries.map(([key, value]) => (
        <KeyValueDisplay key={key} label={formatKey(key)} value={formatValue(key, value)} />
      ))}
    </div>
  );
};

export default KeyValueGrid;
