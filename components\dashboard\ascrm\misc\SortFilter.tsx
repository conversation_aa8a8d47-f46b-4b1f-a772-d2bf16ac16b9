import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface SortOption {
  value: string;
  label: string;
}

interface SortFilterProps {
  options: SortOption[];
  value: string;
  onChange: (value: string) => void;
  label?: string;
}

const SortFilter: React.FC<SortFilterProps> = ({ options, value, onChange, label = 'Sort By' }) => {
  return (
    <div className='flex items-center gap-3'>
      <span className='text-sm text-gray-500 font-medium whitespace-nowrap'>{label}</span>
      <Select value={value} onValueChange={onChange}>
        <SelectTrigger className='w-[180px] h-9 bg-white border-gray-200 rounded-md text-sm font-medium shadow-sm hover:bg-gray-50 transition-colors'>
          <SelectValue placeholder='Select option' />
        </SelectTrigger>
        <SelectContent className='min-w-[180px]'>
          {options.map((option) => (
            <SelectItem key={option.value} value={option.value} className='text-sm cursor-pointer'>
              {option.label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
};

export default SortFilter;
