import { <PERSON><PERSON><PERSON>, <PERSON> } from 'lucide-react';
import { cn } from '@/lib/utils';
import Link from 'next/link';

const GoPro = () => {
  return (
    <div className='bg-gradient-to-br from-slate-800 via-slate-800 to-slate-900 p-4 rounded-2xl relative border border-slate-700/50'>
      {/* backgrounds */}
      <div className='absolute inset-0 bg-gradient-to-br from-emerald-600/10 via-teal-600/10 to-cyan-600/10 opacity-50'></div>
      <div className='absolute top-0 right-0 w-24 h-24 bg-gradient-to-br from-emerald-500/20 to-teal-500/20 rounded-full blur-3xl float-animation'></div>
      <div
        className='absolute bottom-0 left-0 w-16 h-16 bg-gradient-to-tr from-teal-500/20 to-cyan-500/20 rounded-full blur-2xl float-animation'
        style={{ animationDelay: '1.5s' }}
      ></div>

      {/* Images */}
      <img src='/icons/fancy-arrow.webp' alt='Arrow' className='h-16 w-auto absolute -top-2 -left-6 transform rotate-[-135deg] z-10 opacity-80' />
      <div className='flex -space-x-3 absolute -top-4 left-6 z-20'>
        <img
          src='/icons/avatar-female.webp'
          alt='Avatar Female'
          className='h-10 w-10 rounded-full border-2 border-emerald-500/50 shadow-lg shadow-emerald-500/20'
        />
        <img
          src='/icons/avatar-male.webp'
          alt='Avatar Male'
          className='h-10 w-10 rounded-full border-2 border-teal-500/50 shadow-lg shadow-teal-500/20'
        />
      </div>
      <img src='/icons/rocket.webp' alt='Rocket' className='h-28 w-auto absolute -top-12 -right-2 transform rotate-12 z-10' />

      {/* Content */}
      <div className='mt-6 relative z-10'>
        <div className='flex items-center justify-center mb-2'>
          <Sparkles size={16} className='text-emerald-400 mr-2' />
          <h3 className='text-base font-bold text-white tracking-wide'>GO PRO</h3>
          <Sparkles size={16} className='text-teal-400 ml-2' />
        </div>
        <p className='text-xs text-white/70 text-center my-3 px-1'>Unlock premium features to enhance your tutoring experience</p>
        <Link href='/tutor-dash/upgrade' passHref>
          <button
            className={cn(
              'w-full py-2 px-4 bg-gradient-to-r from-primaryColor-600 to-gray-950',
              'text-white text-xs font-medium rounded-xl shadow-lg shadow-primaryColor-500/25',
              'flex items-center justify-center gap-2 hover:shadow-xl hover:shadow-primaryColor-500/30',
              'transform transition-all duration-300 hover:scale-[1.02]'
            )}
          >
            <Star size={14} className='fill-yellow-300' />
            <span>Upgrade to PRO</span>
            <Star size={14} className='fill-yellow-300' />
          </button>
        </Link>
      </div>
    </div>
  );
};

export default GoPro;
