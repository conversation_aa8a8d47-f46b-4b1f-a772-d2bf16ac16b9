import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { AlertDialog, AlertDialogCancel, AlertDialogContent, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { Edit, Trash2, X } from 'lucide-react';

interface ISimpleTable {
  headers: string[];
  rows: (string | number | React.ReactNode)[][];
}

const StudySectionTable: React.FC<ISimpleTable> = ({ headers, rows }) => {
  return (
    <Table>
      <TableHeader>
        <TableRow>
          {headers.map((column, index) => (
            <TableHead key={index}>{column}</TableHead>
          ))}
          <TableHead>Action</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {rows.map((row, rowIndex) => (
          <TableRow key={rowIndex}>
            {row.map((cell, cellIndex) => (
              <TableCell key={cellIndex}>
                {cellIndex === 3 && typeof cell === 'string' && cell.length >= 20 ? (
                  <AlertDialog>
                    <AlertDialogTrigger>{cell.slice(0, 20)}...</AlertDialogTrigger>
                    <AlertDialogContent className='lg:w-[700px] lg:max-w-none'>
                      {renderAlertDialogContent(cell)}
                      <AlertDialogCancel className='border-0 absolute rounded-none rounded-tr-md right-0 top-0 bg-primaryColor-50 hover:bg-primaryColor-100 flex-shrink-0 p-2 tracking-wider cursor-pointer group overflow-hidden'>
                        <p className='text-primaryColor'>
                          <X />
                        </p>
                      </AlertDialogCancel>
                    </AlertDialogContent>
                  </AlertDialog>
                ) : (
                  cell
                )}
              </TableCell>
            ))}
            <TableCell className='flex gap-2'>
              <div className='bg-blue-100 text-blue-700 hover:bg-blue-200 rounded size-7 flex items-center justify-center cursor-pointer'>
                <Edit size={16} strokeWidth={1.5} />
              </div>
              <div className='bg-red-100 text-red-700 hover:bg-red-200 rounded size-7 flex items-center justify-center cursor-pointer'>
                <Trash2 size={16} strokeWidth={1.5} />
              </div>
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );
};

const renderAlertDialogContent = (cellData: string) => (
  <div className='flex gap-2 flex-wrap border-primaryColor-50'>
    {cellData.split(',').map((option) => (
      <label key={option} className='flex items-center cursor-pointer text-sm p-2 bg-primaryColor-50 rounded'>
        <input type='checkbox' readOnly checked className='mr-2 accent-primaryColor size-4' />
        {option}
      </label>
    ))}
  </div>
);

export default StudySectionTable;
