import { useMutation, useQuery } from '@tanstack/react-query';
import { QUERY_KEYS } from '@/lib/react-query/queryKeys';
import { queryClient } from '@/lib/react-query/queryClient';
import { UpdateUserPasswordInput, GeneralInfoFormValues, AddressFormValues } from '@/validation/schemas/user.schema';
import { userService, addressService, userBusinessLocationService } from '@/server/services/user.service';

export const useCurrentUser = (options = {}) => useQuery({ queryKey: [QUERY_KEYS.USER.PROFILE], queryFn: userService.showCurrentUser, ...options });

export function useUpdatePassword() {
  return useMutation({
    mutationFn: (data: UpdateUserPasswordInput) => userService.updateUserPassword(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.USER.PROFILE] });
    },
  });
}

export function useUpdateProfilePicture() {
  return useMutation({
    mutationFn: (data: FormData) => userService.updateProfilePicture(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.USER.PROFILE] });
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.USER.GENERAL_INFO] });
    },
  });
}

export function useGetGeneralInfo() {
  return useQuery({ queryKey: [QUERY_KEYS.USER.GENERAL_INFO], queryFn: userService.getGeneralInfo });
}

export function useUpdateGeneralInfo() {
  return useMutation({
    mutationFn: (data: GeneralInfoFormValues) => userService.updateGeneralInfo(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.USER.PROFILE] });
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.USER.GENERAL_INFO] });
    },
  });
}

export function useLogout() {
  return useMutation({
    mutationFn: userService.logout,
    onSuccess: () => {
      queryClient.clear();
    },
  });
}

// Address hooks
export function useCreateAddress() {
  return useMutation({
    mutationFn: (data: AddressFormValues) => addressService.createAddress(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.USER.ADDRESSES] });
    },
  });
}

export function useGetUserAddresses() {
  return useQuery({ queryKey: [QUERY_KEYS.USER.ADDRESSES], queryFn: addressService.getUserAddresses });
}

export function useGetAddressById(addressId: string) {
  return useQuery({
    queryKey: [QUERY_KEYS.USER.ADDRESS, addressId],
    queryFn: () => addressService.getAddressById(addressId),
    enabled: !!addressId,
  });
}

export function useUpdateAddress() {
  return useMutation({
    mutationFn: ({ addressId, data }: { addressId: string; data: AddressFormValues }) => addressService.updateAddress(addressId, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.USER.ADDRESSES] });
    },
  });
}

export function useDeleteAddress() {
  return useMutation({
    mutationFn: (addressId: string) => addressService.deleteAddress(addressId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.USER.ADDRESSES] });
    },
  });
}

// User Business location hooks
export function useUserSearchBusinessLocation(query: string) {
  return useQuery({
    queryKey: [QUERY_KEYS.USER.BUSINESS_LOCATIONS, query],
    queryFn: () => userBusinessLocationService.searchLocation(query),
    enabled: !!query && query.length > 1,
  });
}
