import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import '@/app/globals.css';
import { NavBar, SideBar } from '@/components/dashboard/school-dash/misc';
import BottomNavBar from '@/components/dashboard/school-dash/misc/MobileBottomBar';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'Tutor Dashboard',
  description: 'Generated by create next app',
};

export default function RootLayout({ children }: Readonly<{ children: React.ReactNode }>) {
  return (
    <html lang='en'>
      <body className={inter.className}>
        <div className='flex items-start justify-start bg-slate-100 min-h-screen p-4 lg:gap-4'>
          <div className='shrink-0'>
            <SideBar />
          </div>
          <div className='flex flex-col gap-4 flex-1 max-w-full w-full'>
            <NavBar />
            <main className='flex-1 rounded-3xl max-w-full w-full'>
              {children}
              {/* <div className='pt-16'>
                <BottomNavBar />
              </div> */}
            </main>
          </div>
        </div>
      </body>
    </html>
  );
}
