'use client';

import { useState, useEffect, useCallback } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { AdminDashActionLinks, HeadingBar, LimitFilter, MasterFilter, MasterTable, SectionWrapper } from '@/components/dashboard/ascrm/misc';
import { ErrorState } from '@/components/dashboard/ascrm/misc/StateUI';
import { filterTypeMaps, FilterTypeKey } from '@/constants';
import { FilterValues, FilterOption } from '@/types/api';
import { useGetAllDegreeLevels, useDeleteDegreeLevel, useGetDegreeLevelById, useGetStreamById } from '@/hooks/education/college.hooks';
import { AdminViewButton, PrimaryFormModal, useModal, StatusToggle } from '@/components/dashboard/shared/misc';
import DegreeLevelForm from './degree-level-form';
import { degreeLevelService } from '@/server/services/education/college.service';
import { QUERY_KEYS } from '@/lib/react-query/queryKeys';

const DegreeLevelTable: React.FC = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const addDegreeLevelModal = useModal();
  const editDegreeLevelModal = useModal();

  const [selectedDegreeLevelId, setSelectedDegreeLevelId] = useState<string | null>(null);
  const [filters, setFilters] = useState<FilterValues>({});
  const [streamName, setStreamName] = useState<string>('');

  const streamId = searchParams.get('stream');
  if (!streamId) return <ErrorState message='You are not authorized to view this page.' />;

  const { data: streamData, error: streamError } = useGetStreamById(streamId || '');

  useEffect(() => {
    if (streamData?.data?.stream) {
      setStreamName(streamData.data.stream.name);
    }
  }, [streamData]);

  const updateFiltersFromParams = useCallback(() => {
    const initialFilters: FilterValues = {};
    searchParams.forEach((value, key) => {
      if (key !== 'page') {
        initialFilters[key] = value;
      }
    });
    setFilters(initialFilters);
  }, [searchParams]);

  useEffect(() => {
    updateFiltersFromParams();
  }, [updateFiltersFromParams]);

  const page = Number(searchParams.get('page')) || 1;

  const queryParams = { page, stream: streamId, ...filters };

  const { data: degreeLevelsResponse, isLoading, error } = useGetAllDegreeLevels(queryParams);
  const deleteItem = useDeleteDegreeLevel();
  const {
    data: selectedDegreeLevelData,
    isLoading: isLoadingSelectedDegreeLevel,
    error: selectedDegreeLevelError,
  } = useGetDegreeLevelById(selectedDegreeLevelId || '');

  const degreeLevels = degreeLevelsResponse?.data?.degreeLevels || [];
  const pagination = degreeLevelsResponse?.data?.pagination;

  const handleEditDegreeLevel = (id: string) => {
    setSelectedDegreeLevelId(id);
    editDegreeLevelModal.open();
  };

  if (error || streamError) {
    return <ErrorState message={error?.message || streamError?.message || 'An unexpected error occurred.'} />;
  }

  const headers = ['Name', 'Status', 'Actions'];

  const rows = degreeLevels.map((degreeLevel) => {
    return {
      id: degreeLevel._id,
      values: [
        <p className='font-medium'>{degreeLevel.name}</p>,
        <StatusToggle
          id={degreeLevel._id}
          isActive={degreeLevel.isActive}
          updateFn={degreeLevelService.updateDegreeLevel}
          queryKey={[QUERY_KEYS.EDUCATION.DEGREE_LEVELS, [QUERY_KEYS.EDUCATION.DEGREE_LEVELS, { page, ...filters }]]}
          entityName='Degree Level'
        />,
        <AdminDashActionLinks
          delete
          editAction={() => handleEditDegreeLevel(degreeLevel._id)}
          basePath='/degree-levels'
          id={degreeLevel._id}
          itemName={degreeLevel.name}
          deleteItem={() => deleteItem.mutateAsync(degreeLevel._id)}
          manualAction={[
            <AdminViewButton variant='icon' href={`/degrees?degreeLevel=${degreeLevel._id}`} label={`View ${degreeLevel.name} Degrees`} />,
          ]}
        />,
      ],
    };
  });

  const filterOptions: FilterOption[] = [
    {
      type: filterTypeMaps.input.key as FilterTypeKey,
      label: 'Degree Level Name',
      key: 'name',
    },
  ];

  const handleApplyFilters = (newFilters: FilterValues) => {
    const params = new URLSearchParams(searchParams);
    Object.entries(newFilters).forEach(([key, value]) => {
      if (value && typeof value === 'string') {
        params.set(key, value);
      } else {
        params.delete(key);
      }
    });
    params.set('page', '1');
    router.push(`?${params.toString()}`);
  };

  const handleResetFilters = () => {
    const params = new URLSearchParams();
    params.set('stream', streamId);
    params.set('page', '1');
    router.push(`?${params.toString()}`);
  };

  return (
    <SectionWrapper>
      <HeadingBar
        title={streamName ? `Degree Levels of ${streamName} Stream` : 'College Degree Levels'}
        elements={[
          <LimitFilter
            key='limit'
            value={(filters.limit as string) || '10'}
            onChange={(value: string) => {
              handleApplyFilters({ limit: value });
            }}
          />,
          <MasterFilter
            key='filter'
            filterOptions={filterOptions}
            onApplyFilters={handleApplyFilters}
            onResetFilters={handleResetFilters}
            initialFilters={filters}
          />,
        ]}
        addAction={addDegreeLevelModal.open}
      />
      <MasterTable headers={headers} rows={rows} isLoading={isLoading} pagination={pagination} />

      {/* Add Degree Level Modal */}
      <PrimaryFormModal
        isOpen={addDegreeLevelModal.isOpen}
        onClose={addDegreeLevelModal.close}
        title={streamName ? `Add New Degree Level for ${streamName} Stream` : 'Add New College Degree Level'}
        subtitle='Create a new college degree level for your platform'
        size='sm'
        showSparkles={true}
      >
        <DegreeLevelForm onSuccess={addDegreeLevelModal.close} streamId={streamId} />
      </PrimaryFormModal>

      {/* Edit Degree Level Modal */}
      <PrimaryFormModal
        isOpen={editDegreeLevelModal.isOpen}
        onClose={() => {
          editDegreeLevelModal.close();
          setSelectedDegreeLevelId(null);
        }}
        title={streamName ? `Edit ${streamName} Stream Degree Level` : 'Edit College Degree Level'}
        subtitle='Update existing college degree level details'
        size='sm'
        isLoading={isLoadingSelectedDegreeLevel}
        loadingTitle='Loading Degree Level Data'
        loadingMessage='Please wait while we fetch the degree level details...'
        isError={!!selectedDegreeLevelError}
        errorMessage={selectedDegreeLevelError?.message}
      >
        {selectedDegreeLevelData?.data?.degreeLevel && (
          <DegreeLevelForm data={selectedDegreeLevelData.data.degreeLevel} onSuccess={editDegreeLevelModal.close} streamId={streamId} />
        )}
      </PrimaryFormModal>
    </SectionWrapper>
  );
};

export default DegreeLevelTable;
