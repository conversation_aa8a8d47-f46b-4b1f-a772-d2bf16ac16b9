'use client';

import { FormField, FormItem, FormControl, Form, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { tutionInfoSchema } from '@/lib/validations/tutor-dash/zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { Loader2 } from 'lucide-react';
import * as z from 'zod';
import { Textarea } from '@/components/ui/textarea';

import MultiSelectCheckbox from './MultiSelectCheckbox';
import MultiSelectWithSearch from './MultiSelectWithSearch';

const TutionInfoForm = ({ two = false }: any) => {
  const form = useForm<z.infer<typeof tutionInfoSchema>>({
    resolver: zodResolver(tutionInfoSchema),
    defaultValues: {
      mode: [],
      location: '',
      distance: '',
      language: [],
      description: '',
    },
  });

  let modeOptions = [];

  if (two) {
    modeOptions = [
      { label: 'Online', value: 'online' },
      { label: 'Offline', value: 'offline' },
    ];
  } else {
    modeOptions = [
      { label: 'Student House', value: 'student-house' },
      { label: 'Tutor House', value: 'tutor-house' },
      { label: 'Online', value: 'online' },
      { label: 'Institute', value: 'institute' },
    ];
  }

  async function onSubmit(values: z.infer<typeof tutionInfoSchema>) {
    console.log(values);
  }

  return (
    <div>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className='grid grid-cols-1 md:grid-cols-2 items-start gap-6'>
          {/* Mode */}
          <FormField
            control={form.control}
            name='mode'
            render={({ field }) => (
              <FormItem className='w-full'>
                <FormLabel className='primary-label'>
                  <span>Mode</span> <span className='text-primaryColor'>*</span>
                </FormLabel>
                <FormControl>
                  <MultiSelectCheckbox options={modeOptions} values={field.value} onChange={field.onChange} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Location */}
          <FormField
            control={form.control}
            name='location'
            render={({ field }) => (
              <FormItem className='w-full'>
                <FormLabel className='primary-label'>
                  <span>Location</span> <span className='text-primaryColor'>*</span>
                </FormLabel>
                <FormControl>
                  <Input className='primary-input' type='text' placeholder='Enter location' {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Distance */}
          <FormField
            control={form.control}
            name='distance'
            render={({ field }) => (
              <FormItem className='w-full'>
                <FormLabel className='primary-label'>
                  <span>Distance (km)</span> <span className='text-primaryColor'>*</span>
                </FormLabel>
                <FormControl>
                  <Input className='primary-input' type='number' placeholder='Enter distance' {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Language */}
          <FormField
            control={form.control}
            name='language'
            render={({ field }) => (
              <FormItem className='w-full'>
                <FormLabel className='primary-label'>
                  <span>Language</span> <span className='text-primaryColor'>*</span>
                </FormLabel>
                <FormControl>
                  <MultiSelectWithSearch
                    options={[
                      { label: 'English', value: 'english' },
                      { label: 'Hindi', value: 'hindi' },
                      { label: 'Marathi', value: 'marathi' },
                      { label: 'Bengali', value: 'bengali' },
                    ]}
                    selectedOptions={field.value}
                    onChange={field.onChange}
                  />
                </FormControl>

                <FormMessage />
              </FormItem>
            )}
          />

          {/* Description */}
          <FormField
            control={form.control}
            name='description'
            render={({ field }) => (
              <FormItem className='w-full md:col-span-2'>
                <FormLabel className='primary-label'>
                  <span>Description</span> <span className='text-primaryColor'>*</span>
                </FormLabel>
                <FormControl>
                  <Textarea className='primary-input' placeholder='Enter description' {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <button type='submit' disabled={form.formState.isSubmitting} className='btn-default-md md:col-span-2 w-fit'>
            {form.formState.isSubmitting ? (
              <>
                <Loader2 size={20} className='animate-spin mr-3' />
                <span>Saving...</span>
              </>
            ) : (
              <span>Submit</span>
            )}
          </button>
        </form>
      </Form>
    </div>
  );
};

export default TutionInfoForm;
