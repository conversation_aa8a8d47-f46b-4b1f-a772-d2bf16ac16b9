import Image from 'next/image';
import { KeyValueDisplay } from '@/components/dashboard/shared/misc';

const TutorTuitionLeadViewPage = () => {
  return (
    <section className='flex gap-8 justify-start items-start'>
      {/* Left Column */}
      <div className='w-[25%] flex flex-col items-start gap-8'>
        {/* Avatar and Details */}
        <div className='flex flex-col justify-start items-start gap-4 bg-white rounded-3xl p-6 relative'>
          <div className='absolute top-0 left-0 transform bg-gradient-1 uppercase tracking-wider text-white text-xs font-semibold px-5 py-1.5 rounded-tl-3xl rounded-br-3xl'>
            #14571
          </div>

          <div className='absolute top-0 right-0 transform bg-gradient-1 uppercase tracking-wider text-white text-xs font-semibold px-5 py-1.5 rounded-bl-3xl rounded-tr-3xl'>
            25-06-2024
          </div>
          <div className='flex justify-center mt-4'>
            <Image
              alt='avatar'
              src='/temp/avatar.jpg'
              height={240}
              width={240}
              className='max-w-60 h-auto object-contain object-center rounded-3xl'
            />
          </div>
          <button className='btn-default-sm w-full justify-center !rounded-3xl'>Change Status</button>
        </div>
      </div>

      {/* Right Column */}
      <div className='w-[75%] flex flex-col items-start gap-8'>
        {/* User Info */}
        <div className='bg-white rounded-3xl p-6 w-full'>
          <div className='flex items-start justify-between mb-4'>
            <h2 className='text-xl font-semibold'>About Gaurav</h2>
            <span className='px-6 py-1.5 rounded-full text-sm bg-teal-500 text-white'>Active</span>
          </div>
          <div className='grid grid-cols-2 gap-4'>
            <KeyValueDisplay label='Enq ID' value='14571' />
            <KeyValueDisplay label='Mobile' value='91 XXXXXXXX94' />
            <KeyValueDisplay label='Location' value='India' />
            <KeyValueDisplay label='Admission Type' value='School' />
          </div>
        </div>

        {/* General Information */}
        <div className='bg-white rounded-3xl p-6 w-full relative'>
          <h2 className='text-xl font-semibold mb-4'>Enquiry details</h2>
          <div className='grid grid-cols-2 gap-4'>
            <KeyValueDisplay label='School Registration' value='Private' />
            <KeyValueDisplay label='School Type' value='Girls' />
            <KeyValueDisplay label='School Category' value='Boarding School' />
            <KeyValueDisplay label='Class' value='Class VIII' />
            <KeyValueDisplay label='Board' value='CBSE' />
            <KeyValueDisplay label='Any Special Requirements' value='N/A' />
          </div>
        </div>

        <TeacherProfiles />
      </div>
    </section>
  );
};

import { Briefcase, MapPin, Star, ScrollText, ScanEye, IndianRupee } from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { ParentDashLink } from '@/components/dashboard/parent-dash/misc';

const teachers = [
  {
    id: 1,
    name: 'DPS Public School',
    subjects: 'Math, Science, English',
    rating: 4.5,
    reviews: 12,
    experience: '5 years',
    salary: '₹50,000/month',
    location: 'New Delhi',
    description: 'Experienced Math and Science teacher with a passion for teaching and a proven track record of student success.',
    tags: ['Math', 'Science', 'English'],
    date: '2023-06-25',
    avatar: '/temp/avatar.jpg',
  },
  {
    id: 2,
    name: 'Pooja Singh',
    subjects: 'Physics, Chemistry, Mathematics',
    rating: 4.7,
    reviews: 20,
    experience: '7 years',
    salary: '₹60,000/month',
    location: 'Mumbai',
    description: 'Dedicated Physics and Chemistry teacher with extensive experience in preparing students for competitive exams.',
    tags: ['Physics', 'Chemistry', 'Mathematics'],
    date: '2023-06-20',
    avatar: '/temp/avatar.jpg',
  },
  {
    id: 3,
    name: 'Amit Sharma',
    subjects: 'Biology, Computer Science',
    rating: 4.2,
    reviews: 18,
    experience: '6 years',
    salary: '₹55,000/month',
    location: 'Bangalore',
    description: 'Passionate Biology and Computer Science teacher with a focus on practical learning and student engagement.',
    tags: ['Biology', 'Computer Science'],
    date: '2023-06-18',
    avatar: '/temp/avatar.jpg',
  },
  {
    id: 4,
    name: 'Neha Patel',
    subjects: 'Mathematics, Biology',
    rating: 4.6,
    reviews: 15,
    experience: '8 years',
    salary: '₹70,000/month',
    location: 'Ahmedabad',
    description: 'Expert Mathematics and Biology teacher with a knack for making complex concepts easy to understand.',
    tags: ['Mathematics', 'Biology'],
    date: '2023-06-15',
    avatar: '/temp/avatar.jpg',
  },
  {
    id: 5,
    name: 'Mehul Verma',
    subjects: 'English, History',
    rating: 4.4,
    reviews: 10,
    experience: '4 years',
    salary: '₹45,000/month',
    location: 'Pune',
    description: 'Enthusiastic English and History teacher with a love for storytelling and student interaction.',
    tags: ['English', 'History'],
    date: '2023-06-10',
    avatar: '/temp/avatar.jpg',
  },
];

const TeacherCard = ({ teacher }: { teacher: any }) => (
  <div className='bg-white p-6 rounded-3xl grid grid-cols-1 md:grid-cols-5 gap-2'>
    <div className='md:col-span-4'>
      <div className='flex justify-between'>
        <h3 className='text-lg font-semibold'>{teacher.name}</h3>
        <Image src={teacher.avatar} alt={`${teacher.name} avatar`} className='size-12 md:hidden' height={40} width={40} />
      </div>
      <div className='flex items-center text-gray-600 font-medium text-sm mb-3'>
        <span>{teacher.subjects}</span>
        <span className='mx-2'>|</span>
        <span className='flex items-start md:items-center gap-2'>
          <Star fill='orange' stroke='none' size={16} />
          {teacher.rating} ({teacher.reviews} Reviews)
        </span>
      </div>
      <div className='flex gap-2 items-center text-sm flex-wrap'>
        <p className='flex gap-1 items-start md:items-center'>
          <Briefcase size={16} className='shrink-0' /> <span>{teacher.experience}</span>
        </p>
        <span className='text-gray-400'>|</span>
        <p className='flex gap-1 items-start md:items-center'>
          <MapPin size={16} className='shrink-0' /> <span>{teacher.location}</span>
        </p>
        <span className='text-gray-400'>|</span>
        <p className='flex gap-1 items-start md:items-center'>
          <IndianRupee size={16} className='shrink-0' /> <span>{teacher.salary}</span>
        </p>
      </div>
      <div className='text-gray-500 my-2'>
        <p className='flex gap-2 items-start md:items-center'>
          <ScrollText size={18} className='shrink-0' />
          <span className='line-clamp-2 md:line-clamp-1'>{teacher.description}</span>
        </p>
      </div>
      <div className='flex flex-wrap gap-2 mb-4'>
        {teacher.tags.map((tag: any, index: any) => (
          <span key={index} className='bg-gray-100 text-gray-700 px-4 py-1 rounded capitalize'>
            {tag}
          </span>
        ))}
      </div>
      <p className='font-medium text-xs hidden md:block'>{formatDistanceToNow(new Date(teacher.date))} ago</p>
    </div>
    <div className='flex justify-between md:flex-col md:justify-center items-center md:col-span-1 gap-4 flex-wrap'>
      <p className='font-medium text-xs md:hidden'>{formatDistanceToNow(new Date(teacher.date))} ago</p>
      <Image
        src={teacher.avatar}
        alt={`${teacher.name} avatar`}
        className='size-24 hidden md:block rounded-full ring-4 ring-offset-4 ring-primaryColor'
        height={100}
        width={100}
      />
      <ParentDashLink href={`/teachers/${teacher.id}`} className='btn-default__outline-sm gap-2 mt-auto flex items-center'>
        <ScanEye size={18} /> <span>Apply Now</span>
      </ParentDashLink>
    </div>
  </div>
);

const TeacherProfiles = () => (
  <section className='space-y-6'>
    {teachers.map((teacher) => (
      <TeacherCard key={teacher.id} teacher={teacher} />
    ))}
  </section>
);

export default TutorTuitionLeadViewPage;
