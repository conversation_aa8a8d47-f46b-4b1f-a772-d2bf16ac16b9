'use client';

import { Form } from '@/components/ui/form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import { toast } from 'react-toastify';
import { PrimaryInput, PrimarySwitchInput, SubmitButton } from '@/components/forms';
import { IAddUpdateFormProps } from '@/types/api';
import { useCreateHobbyType, useUpdateHobbyType } from '@/hooks/education/hobby.hooks';
import { IHobbyTypeDocument } from '@/server/services/education/hobby.service';
import { createHobbyTypeSchema } from '@/validation/schemas/education/hobby.schema';

interface IHobbyTypeFormProps extends IAddUpdateFormProps<IHobbyTypeDocument> {
  onSuccess: () => void;
}

const HobbyTypeForm: React.FC<IHobbyTypeFormProps> = ({ data, onSuccess }) => {
  const { mutateAsync: createHobbyType, isPending: isCreating } = useCreateHobbyType();
  const { mutateAsync: updateHobbyType, isPending: isUpdating } = useUpdateHobbyType();

  const hobbyTypeId = data?._id;
  const isSubmitting = isCreating || isUpdating;

  const form = useForm<z.infer<typeof createHobbyTypeSchema>>({
    resolver: zodResolver(createHobbyTypeSchema),
    defaultValues: {
      name: data?.name || '',
      isActive: data?.isActive !== undefined ? data.isActive : true,
    },
  });

  const onSubmit = async (values: z.infer<typeof createHobbyTypeSchema>) => {
    try {
      const result = hobbyTypeId 
        ? await updateHobbyType({ id: hobbyTypeId, data: values }) 
        : await createHobbyType(values);
        
      if (!result.success) throw new Error(result.message || 'Operation failed');

      toast.success(result.message);
      onSuccess();
    } catch (error: any) {
      toast.error(error.message || 'An error occurred');
    }
  };

  return (
    <section className='relative'>
      <div className='bg-white rounded-lg shadow-sm p-6 max-w-2xl'>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className='grid grid-cols-1 gap-6 items-start justify-start'>
            {/* Name field */}
            <PrimaryInput form={form} name='name' label='Hobby Type Name' placeholder='Enter hobby type name' required />

            {/* Is Active */}
            <PrimarySwitchInput
              form={form}
              name='isActive'
              label='Is this hobby type active?'
              description='Making it inactive will hide it from the website.'
            />

            <SubmitButton
              submittingLabel={hobbyTypeId ? 'Updating...' : 'Creating...'}
              label={hobbyTypeId ? 'Update Hobby Type' : 'Create Hobby Type'}
              isSubmitting={isSubmitting}
              disabled={isSubmitting}
              className='w-fit'
            />
          </form>
        </Form>
      </div>
    </section>
  );
};

export default HobbyTypeForm;
