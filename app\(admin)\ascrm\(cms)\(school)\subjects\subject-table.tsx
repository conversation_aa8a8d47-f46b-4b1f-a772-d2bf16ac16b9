'use client';

import { useState, useEffect, useCallback } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { AdminDashActionLinks, HeadingBar, LimitFilter, MasterFilter, MasterTable, SectionWrapper } from '@/components/dashboard/ascrm/misc';
import { ErrorState } from '@/components/dashboard/ascrm/misc/StateUI';
import { filterTypeMaps, FilterTypeKey } from '@/constants';
import { FilterValues, FilterOption } from '@/types/api';
import { useGetAllSubjects, useDeleteSubject, useGetSubjectById, useGetClassById } from '@/hooks/education/school.hooks';
import { PrimaryFormModal, useModal, StatusToggle } from '@/components/dashboard/shared/misc';
import SubjectForm from './subject-form';
import { subjectService } from '@/server/services/education/school.service';
import { QUERY_KEYS } from '@/lib/react-query/queryKeys';

const SubjectTable: React.FC = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const addSubjectModal = useModal();
  const editSubjectModal = useModal();

  const [selectedSubjectId, setSelectedSubjectId] = useState<string | null>(null);
  const [filters, setFilters] = useState<FilterValues>({});
  const [className, setClassName] = useState<string>('');
  const [boardId, setBoardId] = useState<string>('');

  const classId = searchParams.get('class');
  if (!classId) return <ErrorState message='You are not authorized to view this page.' />;

  const { data: classData, error: classError } = useGetClassById(classId || '');

  useEffect(() => {
    if (classData?.data?.class) {
      const classItem = classData.data.class;
      setClassName(classItem.name);
      setBoardId(classItem.board || '');
    }
  }, [classData]);

  const updateFiltersFromParams = useCallback(() => {
    const initialFilters: FilterValues = {};
    searchParams.forEach((value, key) => {
      if (key !== 'page') {
        initialFilters[key] = value;
      }
    });
    setFilters(initialFilters);
  }, [searchParams]);

  useEffect(() => {
    updateFiltersFromParams();
  }, [updateFiltersFromParams]);

  const page = Number(searchParams.get('page')) || 1;

  const queryParams = { page, class: classId, ...filters };

  const { data: subjectsResponse, isLoading, error } = useGetAllSubjects(queryParams);
  const deleteItem = useDeleteSubject();
  const { data: selectedSubjectData, isLoading: isLoadingSelectedSubject, error: selectedSubjectError } = useGetSubjectById(selectedSubjectId || '');

  const subjects = subjectsResponse?.data?.subjects || [];
  const pagination = subjectsResponse?.data?.pagination;

  const handleEditSubject = (id: string) => {
    setSelectedSubjectId(id);
    editSubjectModal.open();
  };

  if (error || classError) {
    return <ErrorState message={error?.message || classError?.message || 'An unexpected error occurred.'} />;
  }

  const headers = ['Name', 'Status', 'Actions'];

  const rows = subjects.map((subject) => {
    return {
      id: subject._id,
      values: [
        <p className='font-medium'>{subject.name}</p>,
        <StatusToggle
          id={subject._id}
          isActive={subject.isActive}
          updateFn={subjectService.updateSubject}
          queryKey={[
            QUERY_KEYS.EDUCATION.SUBJECTS,
            [QUERY_KEYS.EDUCATION.SUBJECTS, { page, class: classId, ...filters }],
            [QUERY_KEYS.EDUCATION.SUBJECTS_BY_CLASS, classId],
          ]}
          entityName='Subject'
        />,
        <AdminDashActionLinks
          delete
          editAction={() => handleEditSubject(subject._id)}
          basePath='/subjects'
          id={subject._id}
          itemName={subject.name}
          deleteItem={() => deleteItem.mutateAsync(subject._id)}
        />,
      ],
    };
  });

  const filterOptions: FilterOption[] = [
    {
      type: filterTypeMaps.input.key as FilterTypeKey,
      label: 'Subject Name',
      key: 'name',
    },
    {
      type: filterTypeMaps.input.key as FilterTypeKey,
      label: 'Board',
      key: 'board',
    },
    {
      type: filterTypeMaps.input.key as FilterTypeKey,
      label: 'Class',
      key: 'class',
    },
  ];

  const handleApplyFilters = (newFilters: FilterValues) => {
    const params = new URLSearchParams(searchParams);
    Object.entries(newFilters).forEach(([key, value]) => {
      if (value && typeof value === 'string') {
        params.set(key, value);
      } else {
        params.delete(key);
      }
    });
    params.set('page', '1');
    router.push(`?${params.toString()}`);
  };

  const handleResetFilters = () => {
    router.push(`?page=1`);
  };

  return (
    <SectionWrapper>
      <HeadingBar
        title={className ? `Subjects of ${className}` : 'School Subjects'}
        elements={[
          <LimitFilter
            key='limit'
            value={(filters.limit as string) || '10'}
            onChange={(value: string) => {
              handleApplyFilters({ limit: value });
            }}
          />,
          <MasterFilter
            key='filter'
            filterOptions={filterOptions}
            onApplyFilters={handleApplyFilters}
            onResetFilters={handleResetFilters}
            initialFilters={filters}
          />,
        ]}
        addAction={addSubjectModal.open}
      />
      <MasterTable headers={headers} rows={rows} isLoading={isLoading} pagination={pagination} />

      {/* Add Subject Modal */}
      <PrimaryFormModal
        isOpen={addSubjectModal.isOpen}
        onClose={addSubjectModal.close}
        title={className ? `Add New Subject for ${className}` : 'Add New School Subject'}
        subtitle='Create a new school subject for your platform'
        size='sm'
        showSparkles={true}
      >
        <SubjectForm onSuccess={addSubjectModal.close} classId={classId} boardId={boardId} />
      </PrimaryFormModal>

      {/* Edit Subject Modal */}
      <PrimaryFormModal
        isOpen={editSubjectModal.isOpen}
        onClose={() => {
          editSubjectModal.close();
          setSelectedSubjectId(null);
        }}
        title={className ? `Edit ${className} Class Subject` : 'Edit School Subject'}
        subtitle='Update existing school subject details'
        size='sm'
        isLoading={isLoadingSelectedSubject}
        loadingTitle='Loading Subject Data'
        loadingMessage='Please wait while we fetch the subject details...'
        isError={!!selectedSubjectError}
        errorMessage={selectedSubjectError?.message}
      >
        {selectedSubjectData?.data?.subject && <SubjectForm data={selectedSubjectData.data.subject} onSuccess={editSubjectModal.close} />}
      </PrimaryFormModal>
    </SectionWrapper>
  );
};

export default SubjectTable;
