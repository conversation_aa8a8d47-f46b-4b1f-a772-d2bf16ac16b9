'use client';

import { Form } from '@/components/ui/form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import { toast } from 'react-toastify';
import { PrimaryInput, PrimarySwitchInput, SubmitButton } from '@/components/forms';
import { IAddUpdateFormProps } from '@/types/api';
import { useCreateCourseType, useUpdateCourseType } from '@/hooks/education/course.hooks';
import { ICourseTypeDocument } from '@/server/services/education/course.service';
import { createCourseTypeSchema } from '@/validation/schemas/education/course.schema';

interface ICourseTypeFormProps extends IAddUpdateFormProps<ICourseTypeDocument> {
  onSuccess: () => void;
}

const CourseTypeForm: React.FC<ICourseTypeFormProps> = ({ data, onSuccess }) => {
  const { mutateAsync: createCourseType, isPending: isCreating } = useCreateCourseType();
  const { mutateAsync: updateCourseType, isPending: isUpdating } = useUpdateCourseType();

  const courseTypeId = data?._id;
  const isSubmitting = isCreating || isUpdating;

  const form = useForm<z.infer<typeof createCourseTypeSchema>>({
    resolver: zodResolver(createCourseTypeSchema),
    defaultValues: {
      name: data?.name || '',
      isActive: data?.isActive !== undefined ? data.isActive : true,
    },
  });

  const onSubmit = async (values: z.infer<typeof createCourseTypeSchema>) => {
    try {
      const result = courseTypeId 
        ? await updateCourseType({ id: courseTypeId, data: values }) 
        : await createCourseType(values);
        
      if (!result.success) throw new Error(result.message || 'Operation failed');

      toast.success(result.message);
      onSuccess();
    } catch (error: any) {
      toast.error(error.message || 'An error occurred');
    }
  };

  return (
    <section className='relative'>
      <div className='bg-white rounded-lg shadow-sm p-6 max-w-2xl'>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className='grid grid-cols-1 gap-6 items-start justify-start'>
            {/* Name field */}
            <PrimaryInput form={form} name='name' label='Course Type Name' placeholder='Enter course type name' required />

            {/* Is Active */}
            <PrimarySwitchInput
              form={form}
              name='isActive'
              label='Is this course type active?'
              description='Making it inactive will hide it from the website.'
            />

            <SubmitButton
              submittingLabel={courseTypeId ? 'Updating...' : 'Creating...'}
              label={courseTypeId ? 'Update Course Type' : 'Create Course Type'}
              isSubmitting={isSubmitting}
              disabled={isSubmitting}
              className='w-fit'
            />
          </form>
        </Form>
      </div>
    </section>
  );
};

export default CourseTypeForm;
