'use client';

import { Form } from '@/components/ui/form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import { toast } from 'react-toastify';
import { PrimaryInput, PrimarySwitchInput, SubmitButton } from '@/components/forms';
import { IAddUpdateFormProps } from '@/types/api';
import { useCreateCourse, useUpdateCourse } from '@/hooks/education/course.hooks';
import { ICourseDocument } from '@/server/services/education/course.service';
import { createCourseSchema } from '@/validation/schemas/education/course.schema';
import { extractId } from '@/validation/utils/form.utils';

interface ICourseFormProps extends IAddUpdateFormProps<ICourseDocument> {
  onSuccess: () => void;
  courseTypeId?: string | null;
}

const CourseForm: React.FC<ICourseFormProps> = ({ data, onSuccess, courseTypeId }) => {
  const { mutateAsync: createCourse, isPending: isCreating } = useCreateCourse();
  const { mutateAsync: updateCourse, isPending: isUpdating } = useUpdateCourse();

  const courseId = data?._id;
  const isSubmitting = isCreating || isUpdating;

  const form = useForm<z.infer<typeof createCourseSchema>>({
    resolver: zodResolver(createCourseSchema),
    defaultValues: {
      name: data?.name || '',
      courseType: extractId(data?.courseType) || courseTypeId || '',
      isActive: data?.isActive !== undefined ? data.isActive : true,
    },
  });

  const onSubmit = async (values: z.infer<typeof createCourseSchema>) => {
    try {
      const result = courseId ? await updateCourse({ id: courseId, data: values }) : await createCourse(values);

      if (!result.success) throw new Error(result.message || 'Operation failed');

      toast.success(result.message);
      onSuccess();
    } catch (error: any) {
      toast.error(error.message || 'An error occurred');
    }
  };

  return (
    <section className='relative'>
      <div className='bg-white rounded-lg shadow-sm p-6 max-w-2xl'>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className='grid grid-cols-1 gap-6 items-start justify-start'>
            {/* Name field */}
            <PrimaryInput form={form} name='name' label='Course Name' placeholder='Enter course name' required />

            {/* Is Active */}
            <PrimarySwitchInput
              form={form}
              name='isActive'
              label='Is this course active?'
              description='Making it inactive will hide it from the website.'
            />

            <SubmitButton
              submittingLabel={courseId ? 'Updating...' : 'Creating...'}
              label={courseId ? 'Update Course' : 'Create Course'}
              isSubmitting={isSubmitting}
              disabled={isSubmitting}
              className='w-fit'
            />
          </form>
        </Form>
      </div>
    </section>
  );
};

export default CourseForm;
