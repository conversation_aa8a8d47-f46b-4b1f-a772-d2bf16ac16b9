import { cn } from '@/lib/utils';

interface BackgroundProps {
  variant: 'primary' | 'secondary' | 'blue' | 'green' | 'purple';
  direction?: 'tl2br' | 'tr2bl' | 'bl2tr' | 'br2tl';
}

const DIRECTION_CLASSES: Record<string, string[]> = {
  tl2br: ['-right-10 -top-10', 'right-20 top-5', '-left-5 bottom-0'],
  tr2bl: ['-left-10 -top-10', 'left-20 top-5', '-right-5 bottom-0'],
  bl2tr: ['-right-10 -bottom-10', 'right-20 bottom-5', '-left-5 top-0'],
  br2tl: ['-left-10 -bottom-10', 'left-20 bottom-5', '-right-5 top-0'],
};

const VARIANT_CLASSES: Record<string, string[]> = {
  primary: ['bg-primaryColor-50', 'bg-primaryColor-100', 'bg-primaryColor-50'],
  secondary: ['bg-secondaryColor-100', 'bg-secondaryColor-200', 'bg-secondaryColor-100'],
  blue: ['bg-blue-50', 'bg-blue-100', 'bg-blue-200'],
  green: ['bg-green-50', 'bg-green-100', 'bg-green-200'],
  purple: ['bg-purple-50', 'bg-purple-100', 'bg-purple-200'],
};

const SIZES = ['w-40 h-40', 'w-16 h-16', 'w-20 h-20'];
const OPACITIES = ['opacity-70', 'opacity-80', 'opacity-60'];

const Backgrounds = ({ variant = 'primary', direction = 'tr2bl' }: BackgroundProps) => {
  const positions = DIRECTION_CLASSES[direction || 'tl2br'] || DIRECTION_CLASSES['tl2br'];
  const colors = VARIANT_CLASSES[variant] || VARIANT_CLASSES['primary'];

  const circles = positions.map((pos, idx) => (
    <div key={idx} className={cn('absolute', pos, SIZES[idx], colors[idx], 'rounded-full', OPACITIES[idx])}></div>
  ));

  return <div className='absolute inset-0 pointer-events-none overflow-hidden'>{circles}</div>;
};

export default Backgrounds;
