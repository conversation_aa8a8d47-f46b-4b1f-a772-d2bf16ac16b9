'use client';

import { <PERSON><PERSON><PERSON>, Edit, Trash2, DollarSign, GraduationCap, School, Languages, Gamepad2, FileText, Code } from 'lucide-react';
import { ITeachingSubjectDocumentPopulated } from '@/server/services/tutor/tuition-profile.service';
import { serviceCategoryMap } from '@/validation/schemas/education/index.maps';
import { rateTypeMap } from '@/validation/schemas/tutor/tuition.maps';

interface TeachingSubjectsListProps {
  subjects: ITeachingSubjectDocumentPopulated[];
  selectedCategory: string;
  onEdit: (subject: ITeachingSubjectDocumentPopulated) => void;
  onDelete: (subject: ITeachingSubjectDocumentPopulated) => void;
}

const TeachingSubjectsList = ({ subjects, selectedCategory, onEdit, onDelete }: TeachingSubjectsListProps) => {
  const getSubjectDisplay = (subject: ITeachingSubjectDocumentPopulated) => {
    switch (subject.serviceCategory) {
      case 'schools':
        return {
          category: subject.boardDetails?.name || 'Unknown Board',
          level: subject.classDetails?.name || 'Unknown Class',
          subjects: subject.allSubjects ? 'All Subjects' : subject.subjectDetails?.map((s) => s.name).join(', ') || 'No subjects',
          icon: School,
        };
      case 'colleges':
        return {
          category: subject.streamDetails?.name || 'Unknown Stream',
          level: `${subject.degreeLevelDetails?.name} - ${subject.degreeDetails?.name}`,
          subjects: subject.allSubjects ? 'All Subjects' : subject.collegeSubjectDetails?.map((s) => s.name).join(', ') || 'No subjects',
          icon: GraduationCap,
        };
      case 'languages':
        return {
          category: subject.languageTypeDetails?.name || 'Unknown Type',
          level: subject.languageDetails?.name || 'Unknown Language',
          subjects: 'Language Teaching',
          icon: Languages,
        };
      case 'hobbies':
        return {
          category: subject.hobbyTypeDetails?.name || 'Unknown Type',
          level: subject.hobbyDetails?.name || 'Unknown Hobby',
          subjects: 'Hobby Teaching',
          icon: Gamepad2,
        };
      case 'exams':
        return {
          category: subject.examCategoryDetails?.name || 'Unknown Category',
          level: subject.examDetails?.name || 'Unknown Exam',
          subjects: subject.allSubjects ? 'All Subjects' : subject.examSubjectDetails?.map((s) => s.name).join(', ') || 'No subjects',
          icon: FileText,
        };
      case 'it_courses':
        return {
          category: subject.courseTypeDetails?.name || 'Unknown Type',
          level: subject.courseDetails?.name || 'Unknown Course',
          subjects: 'IT Course Teaching',
          icon: Code,
        };
      default:
        return {
          category: 'Unknown',
          level: 'Unknown',
          subjects: 'Unknown',
          icon: BookOpen,
        };
    }
  };

  // Get color scheme based on category
  const getColorScheme = (category: string) => {
    switch (category) {
      case 'schools':
        return {
          bgGradient: 'from-blue-50 to-blue-100',
          bgSolid: 'bg-blue-50',
          border: 'border-blue-200',
          text: 'text-blue-600',
          badge: 'bg-blue-100 text-blue-700',
          icon: 'text-blue-500',
        };
      case 'colleges':
        return {
          bgGradient: 'from-green-50 to-green-100',
          bgSolid: 'bg-green-50',
          border: 'border-green-200',
          text: 'text-green-600',
          badge: 'bg-green-100 text-green-700',
          icon: 'text-green-500',
        };
      case 'languages':
        return {
          bgGradient: 'from-purple-50 to-purple-100',
          bgSolid: 'bg-purple-50',
          border: 'border-purple-200',
          text: 'text-purple-600',
          badge: 'bg-purple-100 text-purple-700',
          icon: 'text-purple-500',
        };
      case 'hobbies':
        return {
          bgGradient: 'from-orange-50 to-orange-100',
          bgSolid: 'bg-orange-50',
          border: 'border-orange-200',
          text: 'text-orange-600',
          badge: 'bg-orange-100 text-orange-700',
          icon: 'text-orange-500',
        };
      case 'exams':
        return {
          bgGradient: 'from-red-50 to-red-100',
          bgSolid: 'bg-red-50',
          border: 'border-red-200',
          text: 'text-red-600',
          badge: 'bg-red-100 text-red-700',
          icon: 'text-red-500',
        };
      case 'it_courses':
        return {
          bgGradient: 'from-indigo-50 to-indigo-100',
          bgSolid: 'bg-indigo-50',
          border: 'border-indigo-200',
          text: 'text-indigo-600',
          badge: 'bg-indigo-100 text-indigo-700',
          icon: 'text-indigo-500',
        };
      default:
        return {
          bgGradient: 'from-gray-50 to-gray-100',
          bgSolid: 'bg-gray-50',
          border: 'border-gray-200',
          text: 'text-gray-600',
          badge: 'bg-gray-100 text-gray-700',
          icon: 'text-gray-500',
        };
    }
  };

  if (subjects.length === 0) {
    const categoryInfo = serviceCategoryMap[selectedCategory as keyof typeof serviceCategoryMap];
    return (
      <div className='flex flex-col items-center justify-center py-12 text-center bg-white rounded-xl border border-gray-100 shadow-sm'>
        <BookOpen className='text-gray-400 mb-4' size={48} />
        <h4 className='text-xl font-bold text-gray-800 mb-2'>No {categoryInfo?.label || selectedCategory} subjects found</h4>
        <p className='text-gray-500 max-w-md'>You haven't added any subjects for this category yet. Click "Add Subject" to get started.</p>
      </div>
    );
  }

  const colorScheme = getColorScheme(selectedCategory);

  return (
    <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
      {subjects.map((subject) => {
        const display = getSubjectDisplay(subject);
        const rateType = rateTypeMap[subject.rateType as keyof typeof rateTypeMap];
        const IconComponent = display.icon;

        return (
          <div
            key={subject._id}
            className={`bg-white rounded-2xl border ${colorScheme.border} overflow-hidden shadow-sm hover:shadow-md transition-all duration-300 group`}
          >
            <div className={`h-1.5 bg-gradient-to-r ${colorScheme.bgGradient}`}></div>

            <div className='p-6'>
              <div className='flex items-start gap-3 mb-4'>
                <div className={`relative w-12 h-12 rounded-lg bg-gradient-to-br ${colorScheme.bgGradient} p-0.5 shadow-sm`}>
                  <div className={`absolute inset-0 rounded-lg bg-gradient-to-br ${colorScheme.bgGradient} opacity-20`}></div>
                  <div className='w-full h-full rounded-lg bg-white flex items-center justify-center'>
                    <IconComponent size={22} className={colorScheme.icon} />
                  </div>
                </div>

                <div className='flex-1'>
                  <div className='flex items-center gap-2 mb-1'>
                    <h3 className='font-bold text-gray-800'>{display.category}</h3>
                    <span className={`px-2 py-0.5 text-xs font-medium rounded-full ${colorScheme.badge}`}>
                      {serviceCategoryMap[subject.serviceCategory as keyof typeof serviceCategoryMap]?.label}
                    </span>
                  </div>
                  <p className='text-xs text-gray-600 mb-2'>{display.level}</p>

                  {/* Edit/Delete Actions */}
                  <div className='flex gap-2'>
                    <button
                      onClick={() => onEdit(subject)}
                      className='inline-flex items-center gap-1.5 px-3 py-1.5 text-xs font-medium text-blue-600 bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors duration-200'
                    >
                      <Edit size={14} />
                      Edit
                    </button>
                    <button
                      onClick={() => onDelete(subject)}
                      className='inline-flex items-center gap-1.5 px-3 py-1.5 text-xs font-medium text-red-600 bg-red-50 hover:bg-red-100 rounded-lg transition-colors duration-200'
                    >
                      <Trash2 size={14} />
                      Delete
                    </button>
                  </div>
                </div>
              </div>

              <div className='space-y-3'>
                <div className='flex items-center gap-2.5 group/item'>
                  <div
                    className={`w-8 h-8 rounded-lg ${colorScheme.bgSolid} flex items-center justify-center group-hover/item:bg-opacity-80 transition-colors`}
                  >
                    <BookOpen size={14} className={`${colorScheme.icon} group-hover/item:scale-110 transition-transform`} />
                  </div>
                  <div>
                    <p className='text-xs text-gray-500'>Subjects</p>
                    <p className='text-xs font-medium text-gray-800 line-clamp-2'>{display.subjects}</p>
                  </div>
                </div>

                <div className='flex items-center gap-2.5 group/item'>
                  <div
                    className={`w-8 h-8 rounded-lg ${colorScheme.bgSolid} flex items-center justify-center group-hover/item:bg-opacity-80 transition-colors`}
                  >
                    <DollarSign size={14} className={`${colorScheme.icon} group-hover/item:scale-110 transition-transform`} />
                  </div>
                  <div>
                    <p className='text-xs text-gray-500'>Rate</p>
                    <p className='text-xs font-medium text-gray-800'>
                      ₹{subject.amount}/{rateType?.label || subject.rateType}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default TeachingSubjectsList;
