'use client';

import { Form } from '@/components/ui/form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import { toast } from 'react-toastify';
import { PrimaryInput, PrimarySelect, PrimarySwitchInput, SubmitButton } from '@/components/forms';
import { IAddUpdateFormProps } from '@/types/api';
import { useCreateClass, useUpdateClass } from '@/hooks/education/school.hooks';
import { createClassSchema } from '@/validation/schemas/education/school.schema';
import { IClassDocument } from '@/server/services/education/school.service';
import { extractId } from '@/validation/utils/form.utils';
import { schoolClassRomanValues } from '@/validation/schemas/education/index.maps';

interface ClassFormProps extends IAddUpdateFormProps<IClassDocument> {
  onSuccess: () => void;
  boardId?: string | null;
}

const ClassForm: React.FC<ClassFormProps> = ({ data, onSuccess, boardId }) => {
  const { mutateAsync: createClass, isPending: isCreating } = useCreateClass();
  const { mutateAsync: updateClass, isPending: isUpdating } = useUpdateClass();

  const classId = data?._id;
  const isSubmitting = isCreating || isUpdating;

  const form = useForm<z.infer<typeof createClassSchema>>({
    resolver: zodResolver(createClassSchema),
    defaultValues: {
      name: data?.name || '',
      displayOrder: data?.displayOrder || 0,
      board: extractId(data?.board) || boardId || '',
      isActive: data?.isActive !== undefined ? data.isActive : true,
    },
  });

  const onSubmit = async (values: z.infer<typeof createClassSchema>) => {
    try {
      const result = classId ? await updateClass({ id: classId, data: values }) : await createClass(values);
      if (!result.success) throw new Error(result.message || 'Operation failed');

      toast.success(result.message);
      onSuccess();
    } catch (error: any) {
      toast.error(error.message || 'An error occurred');
    }
  };

  return (
    <section className='relative'>
      <div className='bg-white rounded-lg shadow-sm p-6 max-w-2xl'>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className='grid grid-cols-1 gap-6 items-start justify-start'>
            {/* Name field */}
            <PrimarySelect
              options={schoolClassRomanValues.map((value) => ({ value, label: value }))}
              form={form}
              name='name'
              label='Class Name'
              placeholder='Enter class name'
              required
            />

            {/* Display Order field */}
            <PrimaryInput form={form} name='displayOrder' label='Display Order' placeholder='Enter display order' required type='number' />

            {/* Is Active */}
            <PrimarySwitchInput
              form={form}
              name='isActive'
              label='Is this class active?'
              description='Making it inactive will hide it from the website.'
            />

            <SubmitButton
              submittingLabel={classId ? 'Updating...' : 'Creating...'}
              label={classId ? 'Update Class' : 'Create Class'}
              isSubmitting={isSubmitting}
              disabled={isSubmitting}
              className='w-fit'
            />
          </form>
        </Form>
      </div>
    </section>
  );
};

export default ClassForm;
