import { LucideIcon } from 'lucide-react';
import React from 'react';

interface FormHeadingProps {
  icon: LucideIcon;
  title: string;
  variant?: 'primary' | 'secondary' | 'blue' | 'green' | 'purple';
  className?: string;
}

const variantStyles = {
  blue: {
    bg: 'bg-blue-50',
    border: 'border-blue-200',
    icon: 'text-blue-500',
    text: 'text-blue-500',
  },
  green: {
    bg: 'bg-green-50',
    border: 'border-green-200',
    icon: 'text-green-600',
    text: 'text-green-600',
  },
  purple: {
    bg: 'bg-purple-50',
    border: 'border-purple-200',
    icon: 'text-purple-500',
    text: 'text-purple-600',
  },
  primary: {
    bg: 'bg-primaryColor-50',
    border: 'border-primaryColor-200',
    icon: 'text-primaryColor-600',
    text: 'text-primaryColor-600',
  },
  secondary: {
    bg: 'bg-secondaryColor-50',
    border: 'border-secondaryColor-200',
    icon: 'text-secondaryColor-400',
    text: 'text-secondaryColor-600',
  },
};

const FormHeading = ({ icon: Icon, title, variant = 'primary', className = '' }: FormHeadingProps) => {
  const styles = variantStyles[variant] || variantStyles.primary;
  return (
    <div className={`flex items-center ${styles.bg} border ${styles.border} rounded-lg p-3 mb-6 ${className}`}>
      <Icon className={`${styles.icon} mr-2`} size={18} />
      <span className={`font-medium ${styles.text} text-[15px] tracking-tight`}>{title}</span>
    </div>
  );
};

export default FormHeading;
