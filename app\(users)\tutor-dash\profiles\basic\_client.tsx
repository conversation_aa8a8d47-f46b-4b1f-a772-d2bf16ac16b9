'use client';

import { useState } from 'react';
import { GeneralInfo, AddressInfo, ProfilePicture, AboutInfo, PasswordSection } from './index';
import { useGetGeneralInfo, useGetUserAddresses } from '@/hooks/users/user.hooks';

const ParentBasicProfilePageHelper = () => {
  const [editGeneralInfo, setEditGeneralInfo] = useState(false);
  const [editCurrentAddress, setEditCurrentAddress] = useState(false);
  const [editPermanentAddress, setEditPermanentAddress] = useState(false);
  const [isSameAsCurrent, setIsSameAsCurrent] = useState(false);
  // 1. Fetch general info
  const { data: generalInfoResponse, isLoading: generalInfoLoading, error: generalInfoError } = useGetGeneralInfo();
  const generalInfo = generalInfoResponse?.data?.generalInfo;

  // 2. Fetch addresses
  const { data: addressesResponse, isLoading: addressesLoading, error: addressesError } = useGetUserAddresses();
  const addresses = addressesResponse?.data?.addresses || [];

  // 3. Find current and permanent addresses
  const currentAddress = addresses.find((addr) => addr.addressType === 'current');
  const permanentAddress = addresses.find((addr) => addr.addressType === 'permanent');

  const profilePicture = generalInfo?.profilePicture || '/temp/avatar.jpg';

  const profilePictureProps = {
    userType: generalInfo?.userType || 'Student',
    imageUrl: profilePicture,
    isLoading: generalInfoLoading,
  };

  const aboutData = { 'User ID/Referral Code': '123', email: generalInfo?.email, primaryMobile: generalInfo?.phone };

  const sampleAddress = { houseNo: '', locality: '', landmark: '', areaPinCode: '', city: '', district: '', state: '' };

  const currentAddressData = currentAddress || { ...sampleAddress, addressType: 'current' };
  const permanentAddressData = permanentAddress || { ...sampleAddress, addressType: 'permanent' };

  const anyEditInfoActive = editGeneralInfo || editCurrentAddress || editPermanentAddress;

  const toggleEditItem = (setItem: React.Dispatch<React.SetStateAction<boolean>>) => {
    setItem((prevValue) => !prevValue);
  };

  return (
    <section className='grid grid-cols-1 lg:grid-cols-4 gap-8 justify-start items-start'>
      {/* Left Column */}
      <div className='lg:col-span-1 flex flex-col items-start gap-8'>
        <ProfilePicture {...profilePictureProps} />
        <AboutInfo data={aboutData} showPromotionBadge={true} isLoading={generalInfoLoading} error={generalInfoError} />
        <PasswordSection />
      </div>

      {/* Right Column */}
      <div className='lg:col-span-3 flex flex-col items-start gap-8'>
        {/* General Information */}
        <GeneralInfo
          editGeneralInfo={editGeneralInfo}
          setEditGeneralInfo={setEditGeneralInfo}
          anyEditInfoActive={anyEditInfoActive}
          toggleEditItem={toggleEditItem}
          generalInfo={generalInfo}
          isLoading={generalInfoLoading}
          error={generalInfoError}
        />

        {/* Current Address */}
        <AddressInfo
          title='Current Address'
          editAddress={editCurrentAddress}
          setEditAddress={setEditCurrentAddress}
          anyEditInfoActive={anyEditInfoActive}
          toggleEditItem={toggleEditItem}
          existingAddress={currentAddressData}
          isLoading={addressesLoading}
          error={addressesError}
          addressType='current'
        />

        {/* Permanent Address */}
        <AddressInfo
          title='Permanent Address'
          editAddress={editPermanentAddress}
          setEditAddress={setEditPermanentAddress}
          anyEditInfoActive={anyEditInfoActive}
          toggleEditItem={toggleEditItem}
          existingAddress={permanentAddressData}
          showSameAsCurrentCheckbox={true}
          onSameAsCurrentChange={setIsSameAsCurrent}
          isSameAsCurrent={isSameAsCurrent}
          currentAddressData={currentAddressData}
          isLoading={addressesLoading}
          error={addressesError}
          addressType='permanent'
        />
      </div>
    </section>
  );
};

export default ParentBasicProfilePageHelper;
