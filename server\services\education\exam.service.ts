import apiClient from '@/server/apiClient';
import { IAPIResponse, IPagination } from '@/types/api';
import {
  CreateExamCategoryInput,
  CreateExamInput,
  CreateExamSubjectInput,
  UpdateExamCategoryInput,
  UpdateExamInput,
  UpdateExamSubjectInput,
} from '@/validation/schemas/education/exam.schema';

// Types for Exam Categories
export interface IExamCategoryDocument extends CreateExamCategoryInput {
  _id: string;
  createdAt: string;
  updatedAt: string;
}

export interface IExamCategoriesResponse {
  examCategories: IExamCategoryDocument[];
  pagination: IPagination;
}

export interface IExamCategoryResponse {
  examCategory: IExamCategoryDocument;
}

// Types for Exams
export interface IExamDocument extends CreateExamInput {
  _id: string;
  examCategoryDetails?: IExamCategoryDocument;
  createdAt: string;
  updatedAt: string;
}

export interface IExamsResponse {
  exams: IExamDocument[];
  pagination: IPagination;
}

export interface IExamResponse {
  exam: IExamDocument;
}

// Types for Exam Subjects
export interface IExamSubjectDocument extends CreateExamSubjectInput {
  _id: string;
  examDetails?: IExamDocument;
  createdAt: string;
  updatedAt: string;
}

export interface IExamSubjectsResponse {
  examSubjects: IExamSubjectDocument[];
  pagination: IPagination;
}

export interface IExamSubjectResponse {
  examSubject: IExamSubjectDocument;
}

// 1. Exam Category Service
const examCategoryService = {
  getAllExamCategories: async (params?: Record<string, any>) => {
    const queryParams = new URLSearchParams(params);
    return await apiClient.get<IExamCategoriesResponse>(`/education/exam-categories?${queryParams.toString()}`);
  },

  getExamCategoryById: async (id: string) => {
    return await apiClient.get<IExamCategoryResponse>(`/education/exam-categories/${id}`);
  },

  createExamCategory: async (data: CreateExamCategoryInput) => {
    return await apiClient.post<IExamCategoryResponse>('/education/exam-categories', data);
  },

  updateExamCategory: async (id: string, data: UpdateExamCategoryInput) => {
    return await apiClient.patch<IExamCategoryResponse>(`/education/exam-categories/${id}`, data);
  },

  deleteExamCategory: async (id: string) => {
    return await apiClient.delete<IAPIResponse>(`/education/exam-categories/${id}`);
  },
};

// 2. Exam Service
const examService = {
  getAllExams: async (params?: Record<string, any>) => {
    const queryParams = new URLSearchParams(params);
    return await apiClient.get<IExamsResponse>(`/education/exams?${queryParams.toString()}`);
  },

  getExamById: async (id: string) => {
    return await apiClient.get<IExamResponse>(`/education/exams/${id}`);
  },

  createExam: async (data: CreateExamInput) => {
    return await apiClient.post<IExamResponse>('/education/exams', data);
  },

  updateExam: async (id: string, data: UpdateExamInput) => {
    return await apiClient.patch<IExamResponse>(`/education/exams/${id}`, data);
  },

  deleteExam: async (id: string) => {
    return await apiClient.delete<IAPIResponse>(`/education/exams/${id}`);
  },
};

// 3. Exam Subject Service
const examSubjectService = {
  getAllExamSubjects: async (params?: Record<string, any>) => {
    const queryParams = new URLSearchParams(params);
    return await apiClient.get<IExamSubjectsResponse>(`/education/exam-subjects?${queryParams.toString()}`);
  },

  getExamSubjectById: async (id: string) => {
    return await apiClient.get<IExamSubjectResponse>(`/education/exam-subjects/${id}`);
  },

  createExamSubject: async (data: CreateExamSubjectInput) => {
    return await apiClient.post<IExamSubjectResponse>('/education/exam-subjects', data);
  },

  updateExamSubject: async (id: string, data: UpdateExamSubjectInput) => {
    return await apiClient.patch<IExamSubjectResponse>(`/education/exam-subjects/${id}`, data);
  },

  deleteExamSubject: async (id: string) => {
    return await apiClient.delete<IAPIResponse>(`/education/exam-subjects/${id}`);
  },
};

export { examCategoryService, examService, examSubjectService };