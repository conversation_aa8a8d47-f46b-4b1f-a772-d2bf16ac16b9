'use client';

import { Book<PERSON>, BookO<PERSON>, Trophy, XCircle, Plus } from 'lucide-react';
import { cn } from '@/lib/utils';
import { statusThemes } from './utils';

interface StatusTabProps {
  status: string;
  count: number;
  isActive: boolean;
  onClick: () => void;
}

const StatusTab = ({ status, count, isActive, onClick }: StatusTabProps) => {
  const theme = statusThemes[status as keyof typeof statusThemes];

  return (
    <button
      onClick={onClick}
      className={cn(
        'relative w-full flex items-center justify-between p-4 rounded-xl transition-all duration-300 border',
        isActive
          ? `bg-gradient-to-r ${theme.primary} text-white shadow-lg border-transparent transform scale-[1.02]`
          : 'bg-white hover:bg-gray-50 text-gray-700 border-gray-200 hover:shadow-md'
      )}
    >
      <div className='flex items-center gap-3.5'>
        <div
          className={cn(
            'w-11 h-11 rounded-lg flex items-center justify-center transition-colors',
            isActive ? 'bg-white/20 backdrop-blur-sm' : `${theme.bg}`
          )}
        >
          {status === 'active' ? (
            <BookOpen size={20} className={cn('transition-transform duration-300', isActive ? 'text-white transform scale-110' : theme.text)} />
          ) : status === 'converted' ? (
            <Trophy size={20} className={cn('transition-transform duration-300', isActive ? 'text-white transform scale-110' : theme.text)} />
          ) : (
            <XCircle size={20} className={cn('transition-transform duration-300', isActive ? 'text-white transform scale-110' : theme.text)} />
          )}
        </div>
        <div className='text-left'>
          <span className={cn('font-semibold text-[15px] transition-colors block', isActive ? 'text-white' : 'text-gray-700')}>{theme.label}</span>
          <span className={cn('text-xs transition-colors block mt-0.5', isActive ? 'text-white/80' : 'text-gray-500')}>
            {count} {count === 1 ? 'Enquiry' : 'Enquiries'}
          </span>
        </div>
      </div>

      {isActive && (
        <div className='flex items-center'>
          <div className='w-2 h-8 rounded-full bg-white/30 mr-2'></div>
          <div className='px-3 py-1.5 rounded-lg text-xs font-medium bg-white/20 text-white backdrop-blur-sm'>Selected</div>
        </div>
      )}
    </button>
  );
};

interface LeadStatusTabsProps {
  selectedTab: 'active' | 'converted' | 'closed';
  setSelectedTab: (tab: 'active' | 'converted' | 'closed') => void;
  tabCounts: Record<string, number>;
}

const LeadStatusTabs = ({ selectedTab, setSelectedTab, tabCounts }: LeadStatusTabsProps) => {
  return (
    <div className='sticky top-4'>
      <div className='flex items-center gap-4 mb-8'>
        <div className='relative w-14 h-14 rounded-2xl bg-gradient-to-br from-red-500 to-red-600 flex items-center justify-center shadow-md'>
          <div className='absolute inset-0 rounded-2xl bg-gradient-to-br from-red-400 to-red-600 opacity-50 blur-[3px]'></div>
          <div className='absolute -bottom-1.5 -right-1.5 w-7 h-7 rounded-full bg-white flex items-center justify-center shadow-md'>
            <div className='w-5 h-5 rounded-full bg-gradient-to-br from-red-400 to-red-600'></div>
          </div>
          <div className='relative z-10'>
            <BookA size={24} color='white' />
          </div>
        </div>

        <div>
          <h3 className='text-2xl font-bold text-gray-800 mb-1'>Tuition Enquiries</h3>
          <p className='text-sm text-gray-500'>Manage your tuition requests</p>
        </div>
      </div>

      <div className='space-y-4 mb-8'>
        <StatusTab status='active' count={tabCounts.active} isActive={selectedTab === 'active'} onClick={() => setSelectedTab('active')} />
        <StatusTab
          status='converted'
          count={tabCounts.converted}
          isActive={selectedTab === 'converted'}
          onClick={() => setSelectedTab('converted')}
        />
        <StatusTab status='closed' count={tabCounts.closed} isActive={selectedTab === 'closed'} onClick={() => setSelectedTab('closed')} />
      </div>
    </div>
  );
};

export default LeadStatusTabs;
