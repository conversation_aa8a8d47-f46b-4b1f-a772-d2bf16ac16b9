import { useMutation, useQuery } from '@tanstack/react-query';
import { QUERY_KEYS } from '@/lib/react-query/queryKeys';
import { queryClient } from '@/lib/react-query/queryClient';
import { CreateStaffInput, UpdateStaffInput, UpdateStaffPasswordInput, StaffLoginInput } from '@/validation/schemas/staff.schema';
import { staffService, staffAuthService } from '@/server/services/staff.service';

// 1. Staff Authentication Hooks

export function useStaffLogin() {
  return useMutation({
    mutationFn: (data: StaffLoginInput) => staffAuthService.login(data),
  });
}

export function useStaffLogout() {
  return useMutation({
    mutationFn: staffAuthService.logout,
    onSuccess: () => {
      queryClient.clear();
    },
  });
}

export function useRefreshStaffToken() {
  return useMutation({
    mutationFn: staffAuthService.refreshToken,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.STAFF.PROFILE] });
    },
  });
}

export function useUpdateStaffPassword() {
  return useMutation({
    mutationFn: (data: UpdateStaffPasswordInput) => staffAuthService.updatePassword(data),
  });
}

// 2. Staff Management Hooks

export function useCurrentStaff(options = {}) {
  return useQuery({
    queryKey: [QUERY_KEYS.STAFF.PROFILE],
    queryFn: staffService.getCurrentStaff,
    ...options,
  });
}

export function useCreateStaff() {
  return useMutation({
    mutationFn: (data: CreateStaffInput) => staffService.createStaff(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.STAFF.LIST] });
    },
  });
}

export function useGetAllStaff(options = {}) {
  return useQuery({
    queryKey: [QUERY_KEYS.STAFF.LIST],
    queryFn: staffService.getAllStaff,
    ...options,
  });
}

export function useGetStaffById(id: string, options = {}) {
  return useQuery({
    queryKey: [QUERY_KEYS.STAFF.DETAIL, id],
    queryFn: () => staffService.getStaffById(id),
    enabled: !!id,
    ...options,
  });
}

export function useUpdateStaff() {
  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateStaffInput }) => staffService.updateStaff(id, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.STAFF.DETAIL, variables.id] });
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.STAFF.LIST] });
    },
  });
}

export function useDeleteStaff() {
  return useMutation({
    mutationFn: (id: string) => staffService.deleteStaff(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.STAFF.LIST] });
    },
  });
}
