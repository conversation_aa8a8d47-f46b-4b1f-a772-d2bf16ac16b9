import { DashboardTopCard, RecentLeads, DashboardMetrics, PromotionalSlides, WalletOverview, AppDownloadCard } from './_helper';

const TutorDashboardPage = () => {
  return (
    <section className='grid grid-cols-1 gap-4 xl:grid-cols-3 lg:grid-cols-2 lg:gap-8 min-h-screen'>
      <main className='lg:col-span-2 xl:col-span-2 space-y-4'>
        <DashboardTopCard />
        <div className='xl:hidden'>
          <PromotionalSlides />
          <div className='my-8 md:w-1/2 mx-auto'>
            <WalletOverview />
          </div>
        </div>
        <RecentLeads />
        <DashboardMetrics />
      </main>
      <aside className='flex items-center justify-start flex-col gap-4 lg:col-span-2 xl:col-span-1'>
        <div className='max-xl:hidden w-full'>
          <PromotionalSlides />
        </div>
        <div className='flex items-center flex-col md:flex-row xl:flex-col justify-start gap-4'>
          <div className='max-md:my-4 xl:my-4 w-full max-xl:hidden'>
            <WalletOverview />
          </div>
          <AppDownloadCard />
        </div>
      </aside>
    </section>
  );
};
export default TutorDashboardPage;
