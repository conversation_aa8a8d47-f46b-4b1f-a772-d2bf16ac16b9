import { GraduationCap, BookOpenText, MapIcon } from 'lucide-react';
import TutorDashLink from './InstiuteDashLink';

const MobileLeadItemCard: React.FC<any> = ({ lead }) => (
  <div className='bg-gradient-to-br from-primaryColor via-primaryColor to-primaryColor-200 text-white rounded-xl p-4 space-y-2'>
    <div className='flex items-start gap-4'>
      <div className='size-10 bg-gray-200 rounded' />
      <div>
        <p className='text-base font-semibold'>{lead.name}</p>
        <p className='text-sm'>
          <span>#212{lead.id}</span> <span>({lead.distance})</span>
        </p>
      </div>
    </div>
    <div>
      <p className='flex items-center justify-start gap-1.5'>
        <GraduationCap size={16} className='shrink-0' />
        <span>
          {lead.board}/{lead.enquiryFor}
        </span>
      </p>
      <p className='flex items-center justify-start gap-1.5'>
        <BookOpenText size={16} className='shrink-0' />
        <span>{lead.subjects.join(', ')}</span>
      </p>
      <p className='flex items-center justify-start gap-1.5'>
        <MapIcon size={16} className='shrink-0' />
        <span>{lead.location}</span>
      </p>
      <div className='flex justify-between items-center'>
        <p>3 days ago</p>
        <TutorDashLink className='btn-default-sm' href={`/leads/${lead.id}/view`}>
          View Details
        </TutorDashLink>
      </div>
    </div>
  </div>
);

export default MobileLeadItemCard;
