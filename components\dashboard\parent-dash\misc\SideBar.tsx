'use client';

import Link from 'next/link';
import Image from 'next/image';

import logo from '@/public/temp/logo.png';
import GoPro from './GoPro';
import { useSidebarStore } from '@/store/sidebarStore';
import { cn } from '@/lib/utils';
import { navigationLinks } from '@/constants/parent-dash';
import ParentDashLink from './TutorDashLink';

const Sidebar = () => {
  const { isSidebarOpen } = useSidebarStore();
  return (
    <aside
      className={cn(
        'sticky group top-0 left-0 max-lg:hidden bg-white w-full pr-4 flex flex-col justify-start items-start gap-8 h-screen pt-5 pb-8 max-w-72 overflow-hidden hover:overflow-auto custom-scrollbar rounded-3xl',
        !isSidebarOpen ? 'hidden' : ''
      )}
    >
      <div className='px-3'>
        <Link href='/' className='flex items-center justify-start'>
          <Image src={logo} alt='Logo' className='object-contain h-10 max-md:w-32' />
        </Link>
      </div>
      <div className='overflow-hidden hover:overflow-auto custom-scrollbar w-72 pb-12'>
        {navigationLinks.map((item, idx) => (
          <div key={idx}>
            <p className='text-gray-400 text-xs font-medium pl-6 mt-4 mb-2 uppercase'>{item.label}</p>
            {item.links && (
              <div className='px-4'>
                {item.links.map((sublink, subIdx) => (
                  <ParentDashLink
                    key={subIdx}
                    href={sublink.href}
                    className='shrink-0 w-56 flex items-center gap-4 pr-4 hover:pl-6 p-3 rounded-full text-sm transition-all duration-300 text-gray-600 hover:bg-my-gradient-1 hover:text-white relative before:hover:absolute before:hover:h-full before:hover:w-4 before:hover:bg-my-gradient-1 before:hover:-left-7 before:hover:rounded-full'
                  >
                    <sublink.icon size={24} strokeWidth={1.5} />
                    <span className='capitalize'>{sublink.title}</span>
                  </ParentDashLink>
                ))}
              </div>
            )}
          </div>
        ))}
      </div>
    </aside>
  );
};

export default Sidebar;
