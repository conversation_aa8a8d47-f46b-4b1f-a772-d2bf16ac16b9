'use client';

import AddressForm from './address-form';
import { cn } from '@/lib/utils';
import { Alert, KeyValueGrid, VisitorsLoader, ToggleEdit } from '@/components/dashboard/shared/misc';
import { addressSchema } from '@/validation/schemas/user.schema';
import { useMemo } from 'react';
import { normalizeDataBySchema } from '@/lib/string.utils';

type AddressInfoProps = {
  title: string;
  editAddress: boolean;
  setEditAddress: React.Dispatch<React.SetStateAction<boolean>>;
  anyEditInfoActive: boolean;
  toggleEditItem: (setItem: React.Dispatch<React.SetStateAction<boolean>>) => void;
  existingAddress: Record<string, any>;
  showSameAsCurrentCheckbox?: boolean;
  onSameAsCurrentChange?: (checked: boolean) => void;
  isSameAsCurrent?: boolean;
  currentAddressData?: Record<string, any>;
  isLoading?: boolean;
  error?: Error | null;
  addressType?: 'current' | 'permanent';
};

const AddressInfo = ({
  title,
  editAddress,
  setEditAddress,
  anyEditInfoActive,
  toggleEditItem,
  existingAddress,
  showSameAsCurrentCheckbox = false,
  onSameAsCurrentChange,
  isSameAsCurrent = false,
  currentAddressData,
  isLoading = false,
  error = null,
  addressType = 'current',
}: AddressInfoProps) => {
  const parsedAddress = useMemo(() => {
    const sourceData = showSameAsCurrentCheckbox && isSameAsCurrent && currentAddressData ? currentAddressData : existingAddress;
    return normalizeDataBySchema(sourceData, addressSchema, ['addressType']);
  }, [existingAddress, showSameAsCurrentCheckbox, isSameAsCurrent, currentAddressData]);

  return (
    <div className={cn('bg-white rounded-3xl p-6 w-full relative', anyEditInfoActive && !editAddress ? 'opacity-50' : '')}>
      <ToggleEdit title={title} editItem={editAddress} toggleEditItem={toggleEditItem} setEditItem={setEditAddress} disabled={isLoading || !!error} />

      {isLoading ? (
        <VisitorsLoader title={title} message='Loading address information...' loaderType='progress' />
      ) : error ? (
        <Alert type='error' title='Error' message={error.message || 'Failed to load address information'} />
      ) : editAddress ? (
        <>
          {showSameAsCurrentCheckbox && (
            <Alert
              className='mb-4'
              type='info'
              title='Same as Current Address'
              message='Check this if your permanent address is the same as your current address. This will automatically copy and save all the details from your current address.'
            />
          )}

          <AddressForm
            address={parsedAddress}
            showSameAsCurrentCheckbox={showSameAsCurrentCheckbox}
            onSameAsCurrentChange={onSameAsCurrentChange}
            isSameAsCurrent={isSameAsCurrent}
            currentAddressData={currentAddressData}
            addressId={existingAddress?._id}
            addressType={addressType}
            onSuccess={() => setEditAddress(false)}
          />
        </>
      ) : (
        <>
          {showSameAsCurrentCheckbox && isSameAsCurrent && (
            <Alert className='mb-4' type='info' title='Using Current Address' message='Your permanent address is the same as your current address.' />
          )}

          <KeyValueGrid data={parsedAddress} rows={2} />
        </>
      )}
    </div>
  );
};

export default AddressInfo;
