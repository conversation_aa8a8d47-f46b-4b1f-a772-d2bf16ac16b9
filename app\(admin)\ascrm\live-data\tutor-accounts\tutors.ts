export const users = [
  {
    name: '<PERSON><PERSON>',
    gender: 'male',
    userId: 'U1001',
    profilePicture: '/images/profiles/amit-sharma.jpg',
    referralLink: 'https://example.com/referral/U1001',
    accountCreatedDate: '2023-05-10T08:30:00Z',
    source: 'Google',
    verificationStatus: {
      isVerified: true,
      agentName: '<PERSON>',
      verificationDate: '2023-06-15T10:45:00Z',
    },
    accountStatus: 'Active',
    leadAllotmentStatus: true,
  },
  {
    name: '<PERSON><PERSON> <PERSON>',
    gender: 'female',
    userId: 'U1002',
    profilePicture: '/images/profiles/priya-singh.jpg',
    referralLink: 'https://example.com/referral/U1002',
    accountCreatedDate: '2023-06-01T14:20:00Z',
    source: 'Facebook',
    verificationStatus: {
      isVerified: false,
      agentName: '',
      verificationDate: '',
    },
    accountStatus: 'Inactive',
    leadAllotmentStatus: false,
  },
  {
    name: '<PERSON><PERSON>',
    gender: 'male',
    userId: 'U1003',
    profilePicture: '/images/profiles/rahul-verma.jpg',
    referralLink: 'https://example.com/referral/U1003',
    accountCreatedDate: '2023-05-15T09:10:00Z',
    source: 'LinkedIn',
    verificationStatus: {
      isVerified: true,
      agentName: 'Anita Das',
      verificationDate: '2023-05-20T11:30:00Z',
    },
    accountStatus: 'Active',
    leadAllotmentStatus: true,
  },
  {
    name: 'Sneha Kapoor',
    gender: 'female',
    userId: 'U1004',
    profilePicture: '/images/profiles/sneha-kapoor.jpg',
    referralLink: 'https://example.com/referral/U1004',
    accountCreatedDate: '2023-07-01T13:00:00Z',
    source: 'Organic',
    verificationStatus: {
      isVerified: false,
      agentName: '',
      verificationDate: '',
    },
    accountStatus: 'Active',
    leadAllotmentStatus: false,
  },
  {
    name: 'Rajesh Kumar',
    gender: 'male',
    userId: 'U1005',
    profilePicture: '/images/profiles/rajesh-kumar.jpg',
    referralLink: 'https://example.com/referral/U1005',
    accountCreatedDate: '2023-04-20T15:30:00Z',
    source: 'Referral',
    verificationStatus: {
      isVerified: true,
      agentName: 'Sunita Rao',
      verificationDate: '2023-04-25T16:45:00Z',
    },
    accountStatus: 'Inactive',
    leadAllotmentStatus: false,
  },
  {
    name: 'Neha Desai',
    gender: 'female',
    userId: 'U1006',
    profilePicture: '/images/profiles/neha-desai.jpg',
    referralLink: 'https://example.com/referral/U1006',
    accountCreatedDate: '2023-03-18T12:10:00Z',
    source: 'Google',
    verificationStatus: {
      isVerified: true,
      agentName: 'Vikram Patel',
      verificationDate: '2023-03-25T10:20:00Z',
    },
    accountStatus: 'Active',
    leadAllotmentStatus: true,
  },
  {
    name: 'Vikash Reddy',
    gender: 'male',
    userId: 'U1007',
    profilePicture: '/images/profiles/vikash-reddy.jpg',
    referralLink: 'https://example.com/referral/U1007',
    accountCreatedDate: '2023-06-10T11:50:00Z',
    source: 'Twitter',
    verificationStatus: {
      isVerified: false,
      agentName: '',
      verificationDate: '',
    },
    accountStatus: 'Inactive',
    leadAllotmentStatus: false,
  },
  {
    name: 'Pooja Bhatt',
    gender: 'female',
    userId: 'U1008',
    profilePicture: '/images/profiles/pooja-bhatt.jpg',
    referralLink: 'https://example.com/referral/U1008',
    accountCreatedDate: '2023-05-25T17:40:00Z',
    source: 'Facebook',
    verificationStatus: {
      isVerified: true,
      agentName: 'Manish Malhotra',
      verificationDate: '2023-05-30T14:00:00Z',
    },
    accountStatus: 'Active',
    leadAllotmentStatus: true,
  },
  {
    name: 'Rohit Sharma',
    gender: 'male',
    userId: 'U1009',
    profilePicture: '/images/profiles/rohit-sharma.jpg',
    referralLink: 'https://example.com/referral/U1009',
    accountCreatedDate: '2023-04-15T10:30:00Z',
    source: 'LinkedIn',
    verificationStatus: {
      isVerified: false,
      agentName: '',
      verificationDate: '',
    },
    accountStatus: 'Inactive',
    leadAllotmentStatus: false,
  },
  {
    name: 'Anjali Rao',
    gender: 'female',
    userId: 'U1010',
    profilePicture: '/images/profiles/anjali-rao.jpg',
    referralLink: 'https://example.com/referral/U1010',
    accountCreatedDate: '2023-07-05T14:50:00Z',
    source: 'Organic',
    verificationStatus: {
      isVerified: true,
      agentName: 'Deepak Gupta',
      verificationDate: '2023-07-10T12:00:00Z',
    },
    accountStatus: 'Active',
    leadAllotmentStatus: true,
  },
  {
    name: 'Suresh Iyer',
    gender: 'male',
    userId: 'U1011',
    profilePicture: '/images/profiles/suresh-iyer.jpg',
    referralLink: 'https://example.com/referral/U1011',
    accountCreatedDate: '2023-05-12T09:20:00Z',
    source: 'Google',
    verificationStatus: {
      isVerified: false,
      agentName: '',
      verificationDate: '',
    },
    accountStatus: 'Inactive',
    leadAllotmentStatus: false,
  },
  {
    name: 'Kiran Mehta',
    gender: 'female',
    userId: 'U1012',
    profilePicture: '/images/profiles/kiran-mehta.jpg',
    referralLink: 'https://example.com/referral/U1012',
    accountCreatedDate: '2023-04-30T13:10:00Z',
    source: 'Referral',
    verificationStatus: {
      isVerified: true,
      agentName: 'Arjun Singh',
      verificationDate: '2023-05-05T11:45:00Z',
    },
    accountStatus: 'Active',
    leadAllotmentStatus: true,
  },
];
