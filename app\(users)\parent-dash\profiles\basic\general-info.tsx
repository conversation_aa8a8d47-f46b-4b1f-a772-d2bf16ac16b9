'use client';

import { cn } from '@/lib/utils';
import { <PERSON>ert, KeyValueGrid, VisitorsLoader, ToggleEdit } from '@/components/dashboard/shared/misc';
import { IGetGeneralInfo } from '@/server/services/user.service';
import ParentBasicProfileForm from './general-info-form';
import { generalInfoSchema } from '@/validation/schemas/user.schema';
import { useMemo } from 'react';
import { normalizeDataBySchema } from '@/lib/string.utils';

interface GeneralInfoProps {
  editGeneralInfo: boolean;
  setEditGeneralInfo: React.Dispatch<React.SetStateAction<boolean>>;
  anyEditInfoActive: boolean;
  toggleEditItem: (setItem: React.Dispatch<React.SetStateAction<boolean>>) => void;
  generalInfo?: IGetGeneralInfo;
  isLoading: boolean;
  error: Error | null;
}

const GeneralInfo = ({ editGeneralInfo, setEditGeneralInfo, anyEditInfoActive, toggleEditItem, generalInfo, isLoading, error }: GeneralInfoProps) => {
  const parsedGeneralInfo = useMemo(() => {
    return normalizeDataBySchema(generalInfo, generalInfoSchema);
  }, [generalInfo]);

  return (
    <div className={cn('bg-white rounded-3xl p-6 w-full relative', anyEditInfoActive && !editGeneralInfo ? 'opacity-50' : '')}>
      <ToggleEdit
        title='General Information'
        editItem={editGeneralInfo}
        toggleEditItem={toggleEditItem}
        setEditItem={setEditGeneralInfo}
        disabled={isLoading || !!error}
      />

      {isLoading ? (
        <VisitorsLoader title='General Information' message='Loading general information...' />
      ) : error ? (
        <Alert type='error' title='Error' message={error.message || 'Failed to load general information'} />
      ) : editGeneralInfo ? (
        <ParentBasicProfileForm parent={generalInfo} handleClose={() => setEditGeneralInfo(false)} />
      ) : (
        <KeyValueGrid data={parsedGeneralInfo} rows={2} />
      )}
    </div>
  );
};

export default GeneralInfo;
