'use client';

import { cn } from '@/lib/utils';
import { useSidebarStore } from '@/store/sidebarStore';
import { AlignJustify, Bell, HandCoins, User } from 'lucide-react';

const NavBar = () => {
  const { isSidebarOpen, toggleSidebar } = useSidebarStore();

  return (
    <nav className='bg-white rounded-lg m-2'>
      <div className='px-4 py-3 md:px-6 md:py-4 flex items-center justify-between'>
        <SidebarToggle isSidebarOpen={isSidebarOpen} onToggle={toggleSidebar} />
        <NavbarActions />
      </div>
    </nav>
  );
};

const SidebarToggle = ({ isSidebarOpen, onToggle }: { isSidebarOpen: boolean; onToggle: () => void }) => (
  <div className='flex gap-4 md:gap-6 max-lg:hidden items-center text-sm tracking-wider'>
    <button
      onClick={onToggle}
      className={cn(
        'mr-3 md:mr-5 hover:bg-primaryColor-50 p-2.5 md:p-3 rounded-full transition-colors',
        !isSidebarOpen ? 'bg-primaryColor text-white hover:text-black' : 'text-gray-700'
      )}
    >
      <AlignJustify className='size-5 md:size-6' />
    </button>
  </div>
);

const NavbarActions = () => (
  <div className='flex items-center w-full gap-3 md:gap-4'>
    <CoinsDisplay amount={630} />
    <NotificationBell />
    <UserProfile name='Yevhen H.' />
  </div>
);

const CoinsDisplay = ({ amount }: { amount: number }) => (
  <div className='flex items-center bg-yellow-100 py-1.5 md:py-2 pl-2.5 md:pl-3 pr-3 md:pr-5 rounded-lg gap-1.5 md:gap-2'>
    <span className='text-white bg-yellow-600 size-7 md:size-8 rounded-lg flex items-center justify-center'>
      <HandCoins size={18} strokeWidth={1.5} />
    </span>
    <span className='font-semibold text-xs md:text-base'>{amount} Coins</span>
  </div>
);

const NotificationBell = () => (
  <button className='relative p-1.5 md:p-2 rounded-full hover:bg-gray-200 transition-colors'>
    <Bell size={25} className='text-gray-600' />
    <span className='absolute top-0.5 right-2 inline-block size-2 bg-red-600 rounded-full' />
  </button>
);

const UserProfile = ({ name }: { name: string }) => (
  <div className='hidden md:flex items-center btn-default-md ml-auto'>
    <User size={20} className='mr-1 md:mr-2' />
    <span className='text-xs md:text-base max-md:hidden'>{name}</span>
  </div>
);

export default NavBar;
