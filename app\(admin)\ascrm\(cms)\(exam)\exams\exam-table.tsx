'use client';

import { useState, useEffect, useCallback } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { AdminDashActionLinks, HeadingBar, LimitFilter, MasterFilter, MasterTable, SectionWrapper } from '@/components/dashboard/ascrm/misc';
import { ErrorState } from '@/components/dashboard/ascrm/misc/StateUI';
import { filterTypeMaps, FilterTypeKey } from '@/constants';
import { FilterValues, FilterOption } from '@/types/api';
import { useGetAllExams, useDeleteExam, useGetExamById, useGetExamCategoryById } from '@/hooks/education/exam.hooks';
import { AdminViewButton, PrimaryFormModal, useModal, StatusToggle } from '@/components/dashboard/shared/misc';
import ExamForm from './exam-form';
import { examService } from '@/server/services/education/exam.service';
import { QUERY_KEYS } from '@/lib/react-query/queryKeys';

const ExamTable: React.FC = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const addExamModal = useModal();
  const editExamModal = useModal();

  const [selectedExamId, setSelectedExamId] = useState<string | null>(null);
  const [filters, setFilters] = useState<FilterValues>({});
  const [examCategoryName, setExamCategoryName] = useState<string>('');

  const examCategoryId = searchParams.get('examCategory');

  if (!examCategoryId) return <ErrorState message='You are not authorized to view this page.' />;

  const { data: examCategoryData, error: examCategoryError } = useGetExamCategoryById(examCategoryId || '');

  useEffect(() => {
    if (examCategoryData?.data?.examCategory) {
      setExamCategoryName(examCategoryData.data.examCategory.name);
    }
  }, [examCategoryData]);

  const updateFiltersFromParams = useCallback(() => {
    const initialFilters: FilterValues = {};
    searchParams.forEach((value, key) => {
      if (key !== 'page' && key !== 'examCategory') {
        initialFilters[key] = value;
      }
    });
    setFilters(initialFilters);
  }, [searchParams]);

  useEffect(() => {
    updateFiltersFromParams();
  }, [updateFiltersFromParams]);

  const page = Number(searchParams.get('page')) || 1;

  const queryParams = { page, examCategory: examCategoryId, ...filters };

  const { data: examsResponse, isLoading, error } = useGetAllExams(queryParams);
  const deleteItem = useDeleteExam();
  const { data: selectedExamData, isLoading: isLoadingSelectedExam, error: selectedExamError } = useGetExamById(selectedExamId || '');

  const exams = examsResponse?.data?.exams || [];
  const pagination = examsResponse?.data?.pagination;

  const handleEditExam = (id: string) => {
    setSelectedExamId(id);
    editExamModal.open();
  };

  if (error || examCategoryError) {
    return <ErrorState message={error?.message || examCategoryError?.message || 'An unexpected error occurred.'} />;
  }

  const headers = ['Name', 'Status', 'Actions'];

  const rows = exams.map((exam) => {
    return {
      id: exam._id,
      values: [
        <p className='font-medium'>{exam.name}</p>,
        <StatusToggle
          id={exam._id}
          isActive={exam.isActive}
          updateFn={examService.updateExam}
          queryKey={[QUERY_KEYS.EDUCATION.EXAMS, [QUERY_KEYS.EDUCATION.EXAMS, { page, examCategory: examCategoryId, ...filters }]]}
          entityName='Exam'
        />,
        <AdminDashActionLinks
          delete
          editAction={() => handleEditExam(exam._id)}
          basePath='/exams'
          id={exam._id}
          itemName={exam.name}
          deleteItem={() => deleteItem.mutateAsync(exam._id)}
          manualAction={[<AdminViewButton variant='icon' href={`/exam-subjects?exam=${exam._id}`} label={`View ${exam.name} Subjects`} />]}
        />,
      ],
    };
  });

  const filterOptions: FilterOption[] = [
    {
      type: filterTypeMaps.input.key as FilterTypeKey,
      label: 'Exam Name',
      key: 'name',
    },
  ];

  const handleApplyFilters = (newFilters: FilterValues) => {
    const params = new URLSearchParams(searchParams);
    Object.entries(newFilters).forEach(([key, value]) => {
      if (value && typeof value === 'string') {
        params.set(key, value);
      } else {
        params.delete(key);
      }
    });
    params.set('page', '1');
    router.push(`?${params.toString()}`);
  };

  const handleResetFilters = () => {
    const params = new URLSearchParams();
    params.set('examCategory', examCategoryId);
    params.set('page', '1');
    router.push(`?${params.toString()}`);
  };

  return (
    <SectionWrapper>
      <HeadingBar
        title={examCategoryName ? `Exams of ${examCategoryName}` : 'Exams'}
        elements={[
          <LimitFilter
            key='limit'
            value={(filters.limit as string) || '10'}
            onChange={(value: string) => {
              handleApplyFilters({ limit: value });
            }}
          />,
          <MasterFilter
            key='filter'
            filterOptions={filterOptions}
            onApplyFilters={handleApplyFilters}
            onResetFilters={handleResetFilters}
            initialFilters={filters}
          />,
        ]}
        addAction={addExamModal.open}
      />
      <MasterTable headers={headers} rows={rows} isLoading={isLoading} pagination={pagination} />

      {/* Add Exam Modal */}
      <PrimaryFormModal
        isOpen={addExamModal.isOpen}
        onClose={addExamModal.close}
        title={examCategoryName ? `Add New ${examCategoryName} Exam` : 'Add New Exam'}
        subtitle='Create a new exam for your platform'
        size='sm'
        showSparkles={true}
      >
        <ExamForm onSuccess={addExamModal.close} examCategoryId={examCategoryId} />
      </PrimaryFormModal>

      {/* Edit Exam Modal */}
      <PrimaryFormModal
        isOpen={editExamModal.isOpen}
        onClose={() => {
          editExamModal.close();
          setSelectedExamId(null);
        }}
        title={examCategoryName ? `Edit ${examCategoryName} Exam` : 'Edit Exam'}
        subtitle='Update existing exam details'
        size='sm'
        isLoading={isLoadingSelectedExam}
        loadingTitle='Loading Exam Data'
        loadingMessage='Please wait while we fetch the exam details...'
        isError={!!selectedExamError}
        errorMessage={selectedExamError?.message}
      >
        {selectedExamData?.data?.exam && (
          <ExamForm data={selectedExamData.data.exam} onSuccess={editExamModal.close} examCategoryId={examCategoryId} />
        )}
      </PrimaryFormModal>
    </SectionWrapper>
  );
};

export default ExamTable;
