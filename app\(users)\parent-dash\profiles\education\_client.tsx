'use client';

import { useState } from 'react';
import { useGetAllChildProfiles } from '@/hooks/profile/profile.hooks';
import StudentProfileDetail from './student-profile-detail';
import EducationList from './education-list';
import StudentProfiles from './student-profiles';
import { VisitorsLoader } from '@/components/dashboard/shared/misc';

const EducationProfilePageHelper = () => {
  const [activeStudentId, setActiveStudentId] = useState<string>('');
  const { data: childProfilesData, isLoading: isLoadingProfiles } = useGetAllChildProfiles();
  const students = childProfilesData?.data?.childProfiles || [];

  if (students.length > 0 && !activeStudentId) {
    setActiveStudentId(students[0]._id);
  }

  const activeStudentData = students.find((student) => student._id === activeStudentId);

  if (isLoadingProfiles) {
    return <VisitorsLoader size='full' title='Student Profiles' message='Loading student profiles...' />;
  }

  return (
    <section className='bg-gradient-to-b from-white to-gray-50 p-8 rounded-3xl relative min-h-[650px] shadow-sm border border-gray-100'>
      <StudentProfiles students={students} activeStudentId={activeStudentId} setActiveStudentId={setActiveStudentId} />

      {activeStudentData && (
        <div className='mt-8 space-y-8'>
          <StudentProfileDetail student={activeStudentData} />
          <EducationList childProfileId={activeStudentData._id} />
        </div>
      )}
    </section>
  );
};

export default EducationProfilePageHelper;
