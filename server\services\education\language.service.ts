import apiClient from '@/server/apiClient';
import { IAPIResponse, IPagination } from '@/types/api';
import {
  CreateLanguageTypeInput,
  CreateLanguageInput,
  UpdateLanguageTypeInput,
  UpdateLanguageInput,
} from '@/validation/schemas/education/language.schema';

// Types for Language Types
export interface ILanguageTypeDocument extends CreateLanguageTypeInput {
  _id: string;
  createdAt: string;
  updatedAt: string;
}

export interface ILanguageTypesResponse {
  languageTypes: ILanguageTypeDocument[];
  pagination: IPagination;
}

export interface ILanguageTypeResponse {
  languageType: ILanguageTypeDocument;
}

// Types for Languages
export interface ILanguageDocument extends CreateLanguageInput {
  _id: string;
  languageTypeDetails?: ILanguageTypeDocument;
  createdAt: string;
  updatedAt: string;
}

export interface ILanguagesResponse {
  languages: ILanguageDocument[];
  pagination: IPagination;
}

export interface ILanguageResponse {
  language: ILanguageDocument;
}

// 1. Language Type Service
const languageTypeService = {
  getAllLanguageTypes: async (params?: Record<string, any>) => {
    const queryParams = new URLSearchParams(params);
    return await apiClient.get<ILanguageTypesResponse>(`/education/language-types?${queryParams.toString()}`);
  },

  getLanguageTypeById: async (id: string) => {
    return await apiClient.get<ILanguageTypeResponse>(`/education/language-types/${id}`);
  },

  createLanguageType: async (data: CreateLanguageTypeInput) => {
    return await apiClient.post<ILanguageTypeResponse>('/education/language-types', data);
  },

  updateLanguageType: async (id: string, data: UpdateLanguageTypeInput) => {
    return await apiClient.patch<ILanguageTypeResponse>(`/education/language-types/${id}`, data);
  },

  deleteLanguageType: async (id: string) => {
    return await apiClient.delete<IAPIResponse>(`/education/language-types/${id}`);
  },
};

// 2. Language Service
const languageService = {
  getAllLanguages: async (params?: Record<string, any>) => {
    const queryParams = new URLSearchParams(params);
    return await apiClient.get<ILanguagesResponse>(`/education/languages?${queryParams.toString()}`);
  },

  getLanguageById: async (id: string) => {
    return await apiClient.get<ILanguageResponse>(`/education/languages/${id}`);
  },

  createLanguage: async (data: CreateLanguageInput) => {
    return await apiClient.post<ILanguageResponse>('/education/languages', data);
  },

  updateLanguage: async (id: string, data: UpdateLanguageInput) => {
    return await apiClient.patch<ILanguageResponse>(`/education/languages/${id}`, data);
  },

  deleteLanguage: async (id: string) => {
    return await apiClient.delete<IAPIResponse>(`/education/languages/${id}`);
  },
};

export { languageTypeService, languageService };
