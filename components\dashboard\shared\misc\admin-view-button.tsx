'use client';

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { AdminDashLink } from '@/components/dashboard/ascrm/misc';
import { ExternalLink, ArrowRight } from 'lucide-react';

interface PremiumViewButtonProps {
  href: string;
  label?: string;
  variant?: 'icon' | 'text';
}

function PremiumViewButton({ href, label, variant = 'text' }: PremiumViewButtonProps) {
  const [isHovered, setIsHovered] = useState(false);

  return (
    <div className='relative inline-block' onMouseEnter={() => setIsHovered(true)} onMouseLeave={() => setIsHovered(false)}>
      <AdminDashLink href={href} className='block'>
        <motion.div
          className='relative flex items-center px-4 py-1.5 overflow-hidden'
          initial={{ borderRadius: 8 }}
          animate={{
            borderRadius: isHovered ? 12 : 8,
            boxShadow: isHovered
              ? '0 10px 25px -5px rgba(220, 38, 38, 0.5), 0 8px 10px -6px rgba(220, 38, 38, 0.3)'
              : '0 4px 6px -1px rgba(220, 38, 38, 0.3), 0 2px 4px -2px rgba(220, 38, 38, 0.2)',
          }}
          transition={{ duration: 0.2 }}
        >
          <motion.div
            className='absolute inset-0 bg-gradient-to-r from-red-600 to-rose-500'
            initial={{ opacity: 0.9 }}
            animate={{ opacity: isHovered ? 1 : 0.9 }}
          />

          <motion.div
            className='absolute inset-0 bg-gradient-to-br from-red-500 via-rose-600 to-red-700'
            initial={{ opacity: 0, scale: 1.1 }}
            animate={{
              opacity: isHovered ? 0.9 : 0,
              scale: isHovered ? 1 : 1.1,
            }}
            transition={{ duration: 0.3 }}
          />

          <AnimatePresence>
            {isHovered && (
              <>
                {[...Array(8)].map((_, i) => (
                  <motion.div
                    key={i}
                    className='absolute w-1 h-1 rounded-full bg-white/40'
                    initial={{
                      x: '50%',
                      y: '50%',
                      opacity: 0,
                      scale: 0,
                    }}
                    animate={{
                      x: `${50 + (Math.random() * 100 - 50)}%`,
                      y: `${50 + (Math.random() * 100 - 50)}%`,
                      opacity: [0, 0.8, 0],
                      scale: [0, 1.5, 0],
                    }}
                    exit={{ opacity: 0, scale: 0 }}
                    transition={{
                      duration: 1 + Math.random(),
                      repeat: Number.POSITIVE_INFINITY,
                      repeatType: 'loop',
                      delay: Math.random() * 0.5,
                    }}
                  />
                ))}
              </>
            )}
          </AnimatePresence>

          <motion.div
            className='absolute inset-0 rounded-xl pointer-events-none'
            initial={{ opacity: 0 }}
            animate={{
              opacity: isHovered ? 1 : 0,
              boxShadow: isHovered ? 'inset 0 0 0 1.5px rgba(255,255,255,0.4)' : 'inset 0 0 0 1px rgba(255,255,255,0.2)',
            }}
            transition={{ duration: 0.2 }}
          />

          <motion.div
            className='absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent'
            initial={{ x: '-100%' }}
            animate={{ x: isHovered ? '100%' : '-100%' }}
            transition={{ duration: 0.8, ease: 'easeInOut' }}
          />

          <div className='relative flex items-center justify-center w-full'>
            <motion.div
              className='relative text-white'
              animate={{
                rotate: isHovered ? [0, 15, 0, -15, 0] : 0,
                scale: isHovered ? [1, 1.2, 1] : 1,
              }}
              transition={{
                duration: isHovered ? 0.6 : 0.2,
                repeat: isHovered ? Number.POSITIVE_INFINITY : 0,
                repeatDelay: 1.5,
              }}
            >
              <ExternalLink size={16} strokeWidth={2.5} />

              <AnimatePresence>
                {isHovered && (
                  <motion.div
                    className='absolute inset-0 rounded-full bg-white/30'
                    initial={{ scale: 1, opacity: 0.8 }}
                    animate={{ scale: 2, opacity: 0 }}
                    exit={{ opacity: 0 }}
                    transition={{
                      duration: 1,
                      repeat: Number.POSITIVE_INFINITY,
                      repeatType: 'loop',
                    }}
                  />
                )}
              </AnimatePresence>
            </motion.div>

            {variant === 'text' && label && (
              <>
                <motion.span
                  className='ml-2 font-medium text-white'
                  animate={{
                    x: isHovered ? 2 : 0,
                    textShadow: isHovered ? '0 0 8px rgba(255,255,255,0.5)' : 'none',
                  }}
                >
                  View {label}
                </motion.span>

                <motion.div
                  className='ml-2 text-white/90'
                  initial={{ opacity: 0, x: -5 }}
                  animate={{
                    opacity: isHovered ? 1 : 0,
                    x: isHovered ? 0 : -5,
                    scale: isHovered ? [1, 1.2, 1] : 1,
                  }}
                  transition={{
                    duration: 0.2,
                    scale: {
                      duration: 0.4,
                      repeat: isHovered ? Number.POSITIVE_INFINITY : 0,
                      repeatDelay: 0.8,
                    },
                  }}
                >
                  <ArrowRight size={14} strokeWidth={2.5} />
                </motion.div>
              </>
            )}
          </div>
        </motion.div>
      </AdminDashLink>
    </div>
  );
}

export default PremiumViewButton;
