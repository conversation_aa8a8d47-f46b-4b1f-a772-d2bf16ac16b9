'use client';

import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON> } from 'lucide-react';
import { useUpdateEnquiryStatus } from '@/hooks/enquiry.hooks';
import { toast } from 'react-toastify';
import { CancelButton, SubmitButton } from '@/components/forms';
import { IEnquiryDocument } from '@/server/services/enquiry.service';
import { PrimaryModal } from '@/components/dashboard/shared/misc';

interface LeadCloseModalProps {
  isOpen: boolean;
  onClose: () => void;
  enquiry: IEnquiryDocument | null;
}

const LeadCloseModal = ({ isOpen, onClose, enquiry }: LeadCloseModalProps) => {
  const updateEnquiryStatus = useUpdateEnquiryStatus();

  const handleDelete = async () => {
    if (!enquiry) return;

    try {
      const response = await updateEnquiryStatus.mutateAsync({ id: enquiry._id, isActive: false });
      if (!response.success) throw new Error(response.message || 'Failed to close enquiry');
      toast.success('Enquiry has been closed successfully');
      onClose();
    } catch (error) {
      console.error('Error closing enquiry:', error);
      toast.error('Failed to close enquiry. Please try again.');
    }
  };

  if (!enquiry || !isOpen) return null;

  return (
    <PrimaryModal hideCloseButton isOpen={isOpen} onClose={onClose} variant='primary' maxWidth='max-w-sm'>
      {/* Header */}
      <div className='bg-gradient-to-r from-red-500 to-red-600 p-4 relative'>
        <div className='flex items-center gap-3'>
          <div className='w-10 h-10 rounded-full bg-white/20 flex items-center justify-center'>
            <AlertTriangle className='text-white' size={20} />
          </div>
          <div>
            <h3 className='text-lg font-bold text-white'>Close Enquiry</h3>
            <p className='text-white/80 text-sm'>This action cannot be undone</p>
          </div>
        </div>
        <button
          onClick={onClose}
          className='absolute top-4 right-4 w-8 h-8 rounded-full bg-white/20 flex items-center justify-center text-white/80 hover:text-white hover:bg-white/30 transition-colors'
        >
          <X size={20} />
        </button>
      </div>

      {/* Content */}
      <div className='p-5'>
        <p className='text-gray-600 mb-4'>
          Are you sure you want to close the enquiry for{' '}
          <span className='font-semibold text-gray-800'>{enquiry.childProfileDetails?.fullName || 'Unknown Student'}</span>?
        </p>

        {/* Actions */}
        <div className='flex justify-end gap-3 mt-4'>
          <CancelButton onClose={onClose} size='sm' />
          <SubmitButton
            isSubmitting={updateEnquiryStatus.isPending}
            label='Close Enquiry'
            submittingLabel='Closing...'
            variant='primary'
            onClick={handleDelete}
            size='sm'
          />
        </div>
      </div>
    </PrimaryModal>
  );
};

export default LeadCloseModal;
