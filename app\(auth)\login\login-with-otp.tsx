'use client';

import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useState, useEffect } from 'react';
import { Loader2 } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { Form } from '@/components/ui/form';
import { PrimaryOTPInput, PrimaryPhoneInput, SubmitButton } from '@/components/forms';
import { toast } from 'react-toastify';
import { useRouter } from 'next/navigation';
import { phoneSchema } from '@/validation/schemas/common.schema';
import authService from '@/server/services/auth.service';
import { IUserTypeMap } from '@/validation/schemas/maps';
import { userDashboardsByRoleMap } from '@/constants';

const otpSchema = z.object({ otp: z.string().length(4, 'OTP must be exactly 4 digits') });

const requestOtpSchema = z.object({ phone: phoneSchema });

const LoginWithMobileOtpForm = ({ selectedUserType }: { selectedUserType: IUserTypeMap }) => {
  const [isOtpSent, setIsOtpSent] = useState(false);
  const [resendTimer, setResendTimer] = useState(300);
  const [isLoading, setIsLoading] = useState(false);
  const [phoneNumber, setPhoneNumber] = useState('');
  const router = useRouter();

  const phoneForm = useForm<z.infer<typeof requestOtpSchema>>({
    resolver: zodResolver(requestOtpSchema),
    defaultValues: {
      phone: '',
    },
  });

  const otpForm = useForm<z.infer<typeof otpSchema>>({
    resolver: zodResolver(otpSchema),
    defaultValues: { otp: '' },
  });

  useEffect(() => {
    if (resendTimer > 0 && isOtpSent) {
      const timerId = setInterval(() => {
        setResendTimer((prev) => prev - 1);
      }, 1000);

      return () => clearInterval(timerId);
    }
  }, [resendTimer, isOtpSent]);

  async function handlePhoneSubmit({ phone }: z.infer<typeof requestOtpSchema>) {
    setIsLoading(true);
    try {
      const response = await authService.requestOTP({ phone, userType: selectedUserType! });
      if (response.success) {
        setPhoneNumber(phone);
        setIsOtpSent(true);
        setResendTimer(300);
      } else {
        toast.error(response.message);
      }
    } catch (error: any) {
      toast.error(error.message || 'An error occurred while sending OTP');
    } finally {
      setIsLoading(false);
    }
  }

  async function handleOtpSubmit({ otp }: z.infer<typeof otpSchema>) {
    if (!phoneNumber) {
      toast.error('Phone number is missing. Please try again.');
      setIsOtpSent(false);
      return;
    }

    setIsLoading(true);
    try {
      const response = await authService.verifyOTP({ phone: phoneNumber, otp, userType: selectedUserType! });
      if (response.success) {
        toast.success(response.message);
        router.push(`/${userDashboardsByRoleMap[selectedUserType].dashboard}`);
      } else {
        throw new Error(response.message);
      }
    } catch (error: any) {
      toast.error(error.message || 'An error occurred during verification');
    } finally {
      setIsLoading(false);
    }
  }

  async function handleResendOtp() {
    if (!phoneNumber) {
      toast.error('Phone number is missing. Please try again.');
      setIsOtpSent(false);
      return;
    }

    setIsLoading(true);
    try {
      const response = await authService.requestOTP({ phone: phoneNumber, userType: selectedUserType! });
      if (response.success) {
        setResendTimer(300);
      } else {
        throw new Error(response.message);
      }
    } catch (error: any) {
      toast.error(error.message || 'An error occurred while resending OTP');
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <div>
      <AnimatePresence mode='wait'>
        {!isOtpSent ? (
          <motion.div
            key='phone'
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 20 }}
            transition={{ duration: 0.3 }}
          >
            <Form {...phoneForm}>
              <form onSubmit={phoneForm.handleSubmit(handlePhoneSubmit)} className='w-full flex flex-col items-start justify-start gap-y-8'>
                <PrimaryPhoneInput
                  form={phoneForm}
                  name='phone'
                  label='Enter your phone number'
                  placeholder='Enter your phone number (e.g., 9198765432)'
                  required
                />
                <button className='w-full btn-default-md' type='submit' disabled={isLoading || phoneForm.formState.isSubmitting}>
                  {isLoading || phoneForm.formState.isSubmitting ? (
                    <>
                      <Loader2 size={20} className='animate-spin mr-3' />
                      <span>Sending OTP...</span>
                    </>
                  ) : (
                    <span>Get OTP</span>
                  )}
                </button>
              </form>
            </Form>
          </motion.div>
        ) : (
          <motion.div
            key='otp'
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.3 }}
          >
            <Form {...otpForm}>
              <form
                onSubmit={otpForm.handleSubmit(handleOtpSubmit)}
                className='w-full flex flex-col items-start justify-start gap-y-4 pb-4 border-b border-gray-100/50'
              >
                <PrimaryOTPInput
                  form={otpForm}
                  name='otp'
                  label={`Enter the 4-digit OTP sent to your phone ${phoneNumber ? phoneNumber.slice(-4).padStart(10, 'x') : ''}`}
                  required
                  length={4}
                />
                <div className='w-full flex justify-between items-center text-sm'>
                  <div>
                    {resendTimer > 0 ? (
                      <span>{`Resend OTP in ${Math.floor(resendTimer / 60)}:${String(resendTimer % 60).padStart(2, '0')}`}</span>
                    ) : (
                      <button type='button' className='text-primaryColor' onClick={handleResendOtp} disabled={resendTimer > 0 || isLoading}>
                        Resend OTP
                      </button>
                    )}
                  </div>
                  <div className='flex justify-between items-center'>
                    <SubmitButton
                      disabled={isLoading || otpForm.formState.isSubmitting}
                      isSubmitting={isLoading || otpForm.formState.isSubmitting}
                      label='Verify OTP'
                      submittingLabel='Verifying...'
                    />
                  </div>
                </div>
              </form>
            </Form>
            <button className='mt-4 text-sm text-primaryColor font-medium hover:underline' onClick={() => setIsOtpSent(false)}>
              Change Phone Number
            </button>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default LoginWithMobileOtpForm;
