import { LoginInput, RegisterInput, RequestOtpInput, VerifyOtpInput } from '@/validation/schemas/auth.schema';
import apiClient from '@/server/apiClient';
import { IAPIResponse, ISessionUser } from '@/types/api';

export interface AuthResponseData {
  user: ISessionUser;
}

export type AuthResponse = IAPIResponse<AuthResponseData>;

const authService = {
  register: async (data: RegisterInput): Promise<AuthResponse> => await apiClient.post<AuthResponseData>('/auth/register', data),

  login: async (data: LoginInput): Promise<AuthResponse> => await apiClient.post<AuthResponseData>('/auth/login', data),

  requestOTP: async (data: RequestOtpInput): Promise<IAPIResponse> => await apiClient.post<IAPIResponse>('/auth/request-otp', data),

  verifyOTP: async (data: VerifyOtpInput): Promise<AuthResponse> => await apiClient.post<AuthResponseData>('/auth/verify-otp', data),

  logout: async (): Promise<IAPIResponse> => await apiClient.post<IAPIResponse>('/auth/logout', {}),

  refreshSession: async (): Promise<AuthResponse> => await apiClient.post<AuthResponseData>('/auth/refresh', {}),
};

export default authService;
