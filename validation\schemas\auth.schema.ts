import { z } from 'zod';
import { dateOfBirthSchema, emailSchema, fullNameSchema, genderSchema, PASSWORD_REGEX, PASSWORD_REQUIREMENTS, phoneSchema } from './common.schema';
import { userTypeMap, studentOrTutorGroupMap, institutionGroupMap, IUserTypeMap } from './maps';

// Main Schemas
export const registerSchema = z
  .object({
    fullName: fullNameSchema,
    email: emailSchema,
    phone: phoneSchema,
    password: z
      .string({ required_error: 'Password is required' })
      .min(8, 'Password must be at least 8 characters')
      .regex(PASSWORD_REGEX, PASSWORD_REQUIREMENTS),
    confirmPassword: z.string({ required_error: 'Password confirmation is required' }),
    location: z.string({ required_error: 'Location is required' }).min(1, 'Location is required'),
    referralSource: z.string({ required_error: 'Referral source is required' }).min(1, 'Please select how you heard about us'),
    otherReferralSource: z.string().optional(),
    userType: z.enum(Object.keys(userTypeMap) as [IUserTypeMap]).refine((value) => !!value, { message: 'User type is required' }),
    gender: genderSchema.optional(),
    dateOfBirth: dateOfBirthSchema.optional(),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Passwords don't match",
    path: ['confirmPassword'],
  })
  .superRefine((data, ctx) => {
    if (Object.keys(studentOrTutorGroupMap).includes(data.userType)) {
      if (!data.gender) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Gender is required for students and tutors',
          path: ['gender'],
        });
      }

      if (!data.dateOfBirth) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Date of Birth is required for students and tutors',
          path: ['dateOfBirth'],
        });
      }
    }

    if (Object.keys(institutionGroupMap).includes(data.userType)) {
      if (!data.dateOfBirth) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Date of Incorporation is required for schools, colleges, and institutes',
          path: ['dateOfBirth'],
        });
      }
    }

    if (data.referralSource === 'Other' && !data.otherReferralSource) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Please specify how you heard about us',
        path: ['otherReferralSource'],
      });
    }
  });

export const loginSchema = z
  .object({
    email: z.string().optional(),
    phone: phoneSchema.optional(),
    password: z.string({ required_error: 'Password is required' }).min(8, 'Password must be at least 8 characters'),
    userType: z.enum(Object.keys(userTypeMap) as [IUserTypeMap]).refine((value) => !!value, { message: 'User type is required' }),
  })
  .refine(
    (data) => {
      return !!data.email || !!data.phone;
    },
    {
      message: 'Please enter either an email address or a phone number',
      path: ['email'],
    }
  )
  .refine(
    (data) => {
      if (data.email) {
        try {
          z.string().email().parse(data.email);
          return true;
        } catch {
          return false;
        }
      }
      return true;
    },
    {
      message: 'Please enter a valid email address',
      path: ['email'],
    }
  );

export const forgotPasswordSchema = z.object({
  email: emailSchema,
  userType: z.enum(Object.keys(userTypeMap) as [IUserTypeMap]).refine((value) => !!value, { message: 'User type is required' }),
});

export const resetPasswordSchema = z
  .object({
    newPassword: z
      .string({ required_error: 'New password is required' })
      .min(8, 'Password must be at least 8 characters')
      .regex(PASSWORD_REGEX, PASSWORD_REQUIREMENTS),
    confirmPassword: z.string({ required_error: 'Password confirmation is required' }).min(8, 'Password must be at least 8 characters'),
    token: z.string({ required_error: 'Reset token is required' }).min(1, 'Reset token is required'),
    email: emailSchema,
    userType: z.enum(Object.keys(userTypeMap) as [IUserTypeMap]).refine((value) => !!value, { message: 'User type is required' }),
  })
  .refine((data) => data.newPassword === data.confirmPassword, {
    message: 'Passwords do not match',
    path: ['confirmPassword'],
  });

export const requestOtpSchema = z.object({
  phone: phoneSchema,
  userType: z.enum(Object.keys(userTypeMap) as [IUserTypeMap]).refine((value) => !!value, { message: 'User type is required' }),
});

export const verifyOtpSchema = z.object({
  phone: phoneSchema,
  otp: z.string({ required_error: 'OTP is required' }).length(4, 'OTP must be 4 digits'),
  userType: z.enum(Object.keys(userTypeMap) as [IUserTypeMap]).refine((value) => !!value, { message: 'User type is required' }),
});

export type RegisterInput = z.infer<typeof registerSchema>;
export type LoginInput = z.infer<typeof loginSchema>;
export type ForgotPasswordInput = z.infer<typeof forgotPasswordSchema>;
export type ResetPasswordInput = z.infer<typeof resetPasswordSchema>;
export type RequestOtpInput = z.infer<typeof requestOtpSchema>;
export type VerifyOtpInput = z.infer<typeof verifyOtpSchema>;
