'use client';

import { X } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';

type ModalVariant = 'primary' | 'secondary' | 'green' | 'purple';

const modalVariants = {
  primary: {
    iconBg: 'bg-gradient-to-br from-primaryColor-500 to-primaryColor-600',
    iconBlur: 'bg-gradient-to-br from-primaryColor-400 to-primaryColor-600',
  },
  secondary: {
    iconBg: 'bg-gradient-to-br from-secondaryColor-500 to-secondaryColor-600',
    iconBlur: 'bg-gradient-to-br from-secondaryColor-400 to-secondaryColor-600',
  },
  green: {
    iconBg: 'bg-gradient-to-br from-green-500 to-green-600',
    iconBlur: 'bg-gradient-to-br from-green-400 to-green-600',
  },
  purple: {
    iconBg: 'bg-gradient-to-br from-purple-500 to-purple-600',
    iconBlur: 'bg-gradient-to-br from-purple-400 to-purple-600',
  },
};

interface PrimaryModalProps {
  isOpen: boolean;
  onClose: () => void;
  variant?: ModalVariant;
  maxWidth?: string;
  children: React.ReactNode;
  hideCloseButton?: boolean;
}

const PrimaryModal = ({ isOpen, onClose, variant = 'primary', maxWidth = 'max-w-3xl', hideCloseButton = false, children }: PrimaryModalProps) => {
  const colors = modalVariants[variant];
  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          className='fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm'
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
        >
          <motion.div
            className={cn('relative w-full max-h-[90vh] overflow-hidden bg-white rounded-2xl shadow-2xl', maxWidth)}
            initial={{ scale: 0.95, y: 20, opacity: 0 }}
            animate={{ scale: 1, y: 0, opacity: 1 }}
            exit={{ scale: 0.95, y: 20, opacity: 0 }}
            transition={{ type: 'spring', damping: 25, stiffness: 300 }}
          >
            {!hideCloseButton && (
              <button
                onClick={onClose}
                className='w-10 h-10 z-20 absolute top-4 right-4 rounded-full flex items-center justify-center text-gray-500 bg-gray-100 hover:bg-gray-200 transition-colors'
              >
                <X size={20} />
              </button>
            )}

            {/* Content */}
            <div className='overflow-y-auto max-h-[90vh]'>{children}</div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default PrimaryModal;
