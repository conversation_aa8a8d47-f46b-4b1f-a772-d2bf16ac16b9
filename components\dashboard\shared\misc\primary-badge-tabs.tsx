import React from 'react';
import { cn } from '@/lib/utils';

export type BadgeTabItem = {
  id: string;
  label: string;
  count?: number;
  variant?: 'primary' | 'secondary' | 'blue' | 'green' | 'purple' | 'orange' | 'gray';
};

const variantMap = {
  primary: 'bg-primaryColor-500',
  secondary: 'bg-secondaryColor-500',
  blue: 'bg-blue-500',
  green: 'bg-green-500',
  purple: 'bg-purple-500',
  orange: 'bg-orange-500',
  gray: 'bg-gray-500',
};

interface PrimaryBadgeTabsProps {
  tabs: BadgeTabItem[];
  activeTab: string;
  onChange: (tabId: string) => void;
  defaultVariant?: keyof typeof variantMap;
  className?: string;
}

const PrimaryBadgeTabs: React.FC<PrimaryBadgeTabsProps> = ({ tabs, activeTab, onChange, defaultVariant = 'primary', className }) => {
  return (
    <div className={cn('flex flex-wrap gap-2 overflow-x-auto pb-1 bg-white rounded-lg p-4 border border-gray-100 shadow-sm', className)}>
      {tabs.map((tab) => {
        const isActive = activeTab === tab.id;
        const variant = tab.variant || defaultVariant;
        const badgeColor = variantMap[variant];

        return (
          <button
            key={tab.id}
            onClick={() => onChange(tab.id)}
            className={cn(
              'px-4 py-2 rounded-lg text-sm font-medium transition-all',
              isActive ? `${badgeColor} text-white shadow-md` : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            )}
          >
            {tab.label} {typeof tab.count === 'number' && `(${tab.count})`}
          </button>
        );
      })}
    </div>
  );
};

export default PrimaryBadgeTabs;
