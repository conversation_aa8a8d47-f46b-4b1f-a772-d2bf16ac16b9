import { InstiuteDashActionLinks, MobileLeadItemCard, SimpleTable } from '@/components/dashboard/institute-dash/misc';
import { BookA } from 'lucide-react';

const leads = [
  {
    id: 1,
    name: '<PERSON><PERSON>',
    enquiryFor: 'Class 10',
    location: 'New Delhi',
    distance: '5 km',
    address: '123 Main St, New Delhi, DL 110001',
    state: 'Delhi',
    country: 'India',
    pincode: '110001',
    subjects: ['Math', 'Science', 'English'],
    board: 'CBSE',
    status: 'converted',
  },
  {
    id: 5,
    name: '<PERSON><PERSON> Ver<PERSON>',
    enquiryFor: 'Class 10',
    location: 'New Delhi',
    distance: '5 km',
    status: 'active',
    address: '123 Main St, New Delhi, DL 110001',
    state: 'Delhi',
    country: 'India',
    pincode: '110001',
    subjects: ['Math', 'Science', 'English'],
    board: 'CBSE',
  },
  {
    id: 2,
    name: '<PERSON><PERSON> Singh',
    enquiryFor: 'B.Sc',
    location: 'Mumbai',
    distance: '15 km',
    status: 'active',
    address: '456 Elm St, Mumbai, MH 400001',
    state: 'Maharashtra',
    country: 'India',
    pincode: '400001',
    subjects: ['Physics', 'Chemistry', 'Mathematics', 'Biology', 'Computer Science'],
    board: 'CBSE',
  },
  {
    id: 3,
    name: 'Amit Sharma',
    enquiryFor: 'Class 12',
    location: 'Bangalore',
    distance: '10 km',
    status: 'closed',
    address: '789 Oak St, Bangalore, KA 560001',
    state: 'Karnataka',
    country: 'India',
    pincode: '560001',
    subjects: ['Physics', 'Chemistry', 'Mathematics', 'Biology', 'Computer Science'],
    board: 'CBSE',
  },
  {
    id: 4,
    name: 'Neha Patel',
    enquiryFor: 'M.Sc',
    location: 'Ahmedabad',
    distance: '20 km',
    status: 'closed',
    address: '321 Pine St, Ahmedabad, GJ 380001',
    state: 'Gujarat',
    country: 'India',
    pincode: '380001',
    subjects: ['Physics', 'Chemistry', 'Mathematics', 'Biology', 'Computer Science'],
    board: 'CBSE',
  },
];

const ParentAdmissionLeadsHelper = () => {
  const headers = ['Name', 'Enquiry For', 'Location', 'Subjects', 'Mode', 'Gender', 'Date', 'Action'];

  const rows = leads.map((lead, index) => [
    <div key={`${lead.id}-${index}`}>
      <p>{lead.name}</p>
      <p>{lead.id}000</p>
    </div>,

    <div key={`${lead.enquiryFor}-${index}`}>
      <p>{lead.enquiryFor}</p>
      <p>{['B.Sc', 'M.Sc'].includes(lead.enquiryFor) ? 'Computer' : 'CBSE'}</p>
    </div>,

    lead.location,

    lead.subjects.join(', '),

    lead.status === 'active' ? 'Online' : 'Offline',

    'Not Specified',

    '06 July 2024',

    <InstiuteDashActionLinks key={`${lead.id}-${index}`} basePath='tutor-info' id={lead.id.toString()} view />,
  ]);

  return (
    <div className='bg-white p-6 rounded-3xl relative min-h-[650px] flex flex-col lg:flex-row items-start justify-start gap-4'>
      <div className='w-full'>
        <div className='flex justify-between items-center'>
          <h2 className='text-lg font-semibold mb-4 capitalize'>Recent Leads For You</h2>
          <button className='btn-default-sm'>Add New Enquiry</button>
        </div>
        <div className='flex flex-col gap-4 md:hidden'>
          {leads.map((lead, index) => (
            <MobileLeadItemCard key={index} lead={lead} />
          ))}
        </div>
        <div className='max-md:hidden'>
          <SimpleTable headers={headers} rows={rows} />
        </div>
      </div>
    </div>
  );
};

export default ParentAdmissionLeadsHelper;
