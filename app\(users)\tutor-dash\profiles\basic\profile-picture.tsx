'use client';

import { useState, useRef, ChangeEvent } from 'react';
import Image from 'next/image';
import { z } from 'zod';
import { Form } from '@/components/ui/form';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Camera, Loader2 } from 'lucide-react';
import { ProfilePictureFormValues, profilePictureSchema } from '@/validation/schemas/user.schema';
import { useUpdateProfilePicture } from '@/hooks/users/user.hooks';
import { getImageUrl } from '@/lib/string.utils';
import { VisitorsLoader } from '@/components/dashboard/shared/misc';
import { toast } from 'react-toastify';

interface ProfilePictureProps {
  userType: string;
  imageUrl: string;
  isLoading: boolean;
}

const ProfilePicture = ({ userType, imageUrl, isLoading }: ProfilePictureProps) => {
  const [previewImage, setPreviewImage] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { mutateAsync: updateProfilePicture, isPending: isUpdatingProfilePicture } = useUpdateProfilePicture();

  const form = useForm<ProfilePictureFormValues>({
    resolver: zodResolver(profilePictureSchema),
    defaultValues: { profilePicture: undefined },
  });

  const handleFileChange = (e: ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    try {
      profilePictureSchema.parse({ profilePicture: file });

      const objectUrl = URL.createObjectURL(file);
      setPreviewImage(objectUrl);

      form.setValue('profilePicture', file);
    } catch (error) {
      if (error instanceof z.ZodError) {
        error.errors.forEach((err) => {
          form.setError('profilePicture', { type: 'manual', message: err.message });
        });
      }
      console.error('File validation error:', error);
    }
  };

  const onSubmit = async (data: ProfilePictureFormValues) => {
    try {
      const formData = new FormData();
      formData.append('profilePicture', data.profilePicture);
      const response = await updateProfilePicture(formData);

      if (response.success) {
        toast.success('Profile picture updated successfully!');
        setPreviewImage(null);
        form.reset();
      } else {
        throw new Error(response.message);
      }
    } catch (error) {
      console.error('Error uploading file:', error);
      toast.error('Failed to upload profile picture');
    }
  };

  const handleButtonClick = () => {
    if (previewImage) {
      form.handleSubmit(onSubmit)();
    } else {
      fileInputRef.current?.click();
    }
  };

  const handleCancelUpload = () => {
    setPreviewImage(null);
    form.reset();
  };

  return (
    <div className='space-y-4 bg-white rounded-3xl p-6 relative w-full'>
      <div className='absolute top-0 left-0 transform bg-gradient-1 uppercase tracking-wider text-white text-xs font-semibold px-5 py-1.5 rounded-tl-3xl rounded-br-3xl'>
        {userType}
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)}>
          <input type='file' ref={fileInputRef} className='hidden' accept='image/jpeg,image/png,image/jpg,image/webp' onChange={handleFileChange} />

          <div className='relative group w-full'>
            <div className='w-full h-60 flex justify-center items-center overflow-hidden rounded-3xl'>
              {isLoading ? (
                <VisitorsLoader size='auto' loaderType='skeleton' className='w-full h-full' />
              ) : (
                <Image
                  alt='avatar'
                  src={previewImage || getImageUrl(imageUrl)}
                  height={240}
                  width={240}
                  className='w-full h-full object-contain object-center rounded-3xl'
                  sizes='(max-width: 768px) 100vw, 240px'
                />
              )}
            </div>

            {!isLoading && (
              <div
                className='absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-3xl cursor-pointer'
                onClick={() => fileInputRef.current?.click()}
              >
                <Camera size={48} className='text-white' />
              </div>
            )}
          </div>

          {form.formState.errors.profilePicture && <p className='text-red-500 text-sm mt-2'>{form.formState.errors.profilePicture.message}</p>}

          <div className='flex gap-2 mt-4'>
            <button
              type={previewImage ? 'submit' : 'button'}
              className='btn-default-sm flex-1 justify-center !rounded-3xl'
              onClick={previewImage ? undefined : handleButtonClick}
              disabled={isUpdatingProfilePicture || isLoading}
            >
              {isUpdatingProfilePicture ? (
                <>
                  <Loader2 size={16} className='animate-spin mr-2' />
                  Uploading...
                </>
              ) : previewImage ? (
                'Upload'
              ) : (
                'Modify Picture'
              )}
            </button>

            {previewImage && !isUpdatingProfilePicture && (
              <button type='button' className='btn-default__outline-sm flex-1 justify-center !rounded-3xl' onClick={handleCancelUpload}>
                Cancel
              </button>
            )}
          </div>
        </form>
      </Form>
    </div>
  );
};

export default ProfilePicture;
