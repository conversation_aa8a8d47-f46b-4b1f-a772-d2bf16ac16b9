'use client';

import { Form } from '@/components/ui/form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import { toast } from 'react-toastify';
import { PrimaryInput, PrimarySwitchInput, SubmitButton } from '@/components/forms';
import { IAddUpdateFormProps } from '@/types/api';
import { useCreateLanguageType, useUpdateLanguageType } from '@/hooks/education/language.hooks';
import { ILanguageTypeDocument } from '@/server/services/education/language.service';
import { createLanguageTypeSchema } from '@/validation/schemas/education/language.schema';

interface ILanguageTypeFormProps extends IAddUpdateFormProps<ILanguageTypeDocument> {
  onSuccess: () => void;
}

const LanguageTypeForm: React.FC<ILanguageTypeFormProps> = ({ data, onSuccess }) => {
  const { mutateAsync: createLanguageType, isPending: isCreating } = useCreateLanguageType();
  const { mutateAsync: updateLanguageType, isPending: isUpdating } = useUpdateLanguageType();

  const languageTypeId = data?._id;
  const isSubmitting = isCreating || isUpdating;

  const form = useForm<z.infer<typeof createLanguageTypeSchema>>({
    resolver: zodResolver(createLanguageTypeSchema),
    defaultValues: {
      name: data?.name || '',
      isActive: data?.isActive !== undefined ? data.isActive : true,
    },
  });

  const onSubmit = async (values: z.infer<typeof createLanguageTypeSchema>) => {
    try {
      const result = languageTypeId 
        ? await updateLanguageType({ id: languageTypeId, data: values }) 
        : await createLanguageType(values);
        
      if (!result.success) throw new Error(result.message || 'Operation failed');

      toast.success(result.message);
      onSuccess();
    } catch (error: any) {
      toast.error(error.message || 'An error occurred');
    }
  };

  return (
    <section className='relative'>
      <div className='bg-white rounded-lg shadow-sm p-6 max-w-2xl'>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className='grid grid-cols-1 gap-6 items-start justify-start'>
            {/* Name field */}
            <PrimaryInput form={form} name='name' label='Language Type Name' placeholder='Enter language type name' required />

            {/* Is Active */}
            <PrimarySwitchInput
              form={form}
              name='isActive'
              label='Is this language type active?'
              description='Making it inactive will hide it from the website.'
            />

            <SubmitButton
              submittingLabel={languageTypeId ? 'Updating...' : 'Creating...'}
              label={languageTypeId ? 'Update Language Type' : 'Create Language Type'}
              isSubmitting={isSubmitting}
              disabled={isSubmitting}
              className='w-fit'
            />
          </form>
        </Form>
      </div>
    </section>
  );
};

export default LanguageTypeForm;
