import { cn } from '@/lib/utils';
import { Loader2, Check } from 'lucide-react';

const variantGradients = {
  primary: 'bg-gradient-to-r from-primaryColor-500 to-primaryColor-600 hover:from-primaryColor-600 hover:to-primaryColor-700',
  secondary: 'bg-gradient-to-r from-secondaryColor-500 to-secondaryColor-600 hover:from-secondaryColor-600 hover:to-secondaryColor-700',
  green: 'bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700',
  purple: 'bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700',
};

const sizeStyles = {
  sm: 'px-3.5 py-1.5 text-sm',
  md: 'px-5 py-2.5',
  lg: 'px-6 py-3 text-lg',
};

interface SubmitButtonProps {
  isSubmitting: boolean;
  label: string;
  submittingLabel: string;
  disabled?: boolean;
  className?: string;
  variant?: 'primary' | 'secondary' | 'green' | 'purple';
  size?: 'sm' | 'md' | 'lg';
  onClick?: () => void;
}

const SubmitButton: React.FC<SubmitButtonProps> = ({
  isSubmitting,
  label,
  submittingLabel,
  disabled = false,
  className = '',
  variant,
  size = 'md',
  onClick,
}) => {
  return (
    <button
      type='submit'
      {...(onClick && { onClick })}
      disabled={isSubmitting || disabled}
      className={
        variant
          ? cn(
              'rounded-xl text-white font-medium transition-all shadow-md flex items-center gap-2',
              variantGradients[variant],
              sizeStyles[size],
              className
            )
          : cn('btn-default-md', className)
      }
    >
      {variant ? (
        isSubmitting ? (
          <>
            <div className='w-6 h-6 rounded-full bg-white/20 flex items-center justify-center'>
              <Loader2 className='animate-spin h-4 w-4 text-white' />
            </div>
            <span>{submittingLabel}</span>
          </>
        ) : (
          <>
            <div className='w-6 h-6 rounded-full bg-white/20 flex items-center justify-center'>
              <Check size={14} className='text-white' />
            </div>
            <span>{label}</span>
          </>
        )
      ) : isSubmitting ? (
        <div className='flex gap-2 items-center justify-center'>
          <Loader2 size={20} className='animate-spin mr-3' />
          <span>{submittingLabel}</span>
        </div>
      ) : (
        <span>{label}</span>
      )}
    </button>
  );
};

export default SubmitButton;
