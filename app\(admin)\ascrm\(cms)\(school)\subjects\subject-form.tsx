'use client';

import { Form } from '@/components/ui/form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import { toast } from 'react-toastify';
import { PrimaryInput, PrimarySwitchInput, SubmitButton } from '@/components/forms';
import { IAddUpdateFormProps } from '@/types/api';
import { useCreateSubject, useUpdateSubject } from '@/hooks/education/school.hooks';
import { ISubjectDocument } from '@/server/services/education/school.service';
import { extractId } from '@/validation/utils/form.utils';
import { createSubjectSchema } from '@/validation/schemas/education/school.schema';

interface SubjectFormProps extends IAddUpdateFormProps<ISubjectDocument> {
  onSuccess: () => void;
  boardId?: string | null;
  classId?: string | null;
}

const SubjectForm: React.FC<SubjectFormProps> = ({ data, onSuccess, boardId, classId }) => {
  const { mutateAsync: createSubject, isPending: isCreating } = useCreateSubject();
  const { mutateAsync: updateSubject, isPending: isUpdating } = useUpdateSubject();
  const subjectId = data?._id;
  const isSubmitting = isCreating || isUpdating;

  const form = useForm<z.infer<typeof createSubjectSchema>>({
    resolver: zodResolver(createSubjectSchema),
    defaultValues: {
      name: data?.name || '',
      board: extractId(data?.board) || boardId || '',
      class: extractId(data?.class) || classId || '',
      isActive: data?.isActive !== undefined ? data.isActive : true,
    },
  });

  const onSubmit = async (values: z.infer<typeof createSubjectSchema>) => {
    try {
      const result = subjectId ? await updateSubject({ id: subjectId, data: values }) : await createSubject(values);
      if (!result.success) throw new Error(result.message || 'Operation failed');

      toast.success(result.message);
      onSuccess();
    } catch (error: any) {
      toast.error(error.message || 'An error occurred');
    }
  };

  return (
    <section className='relative'>
      <div className='bg-white rounded-lg shadow-sm p-6 max-w-2xl'>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className='grid grid-cols-1 gap-6 items-start justify-start'>
            {/* Name field */}
            <PrimaryInput form={form} name='name' label='Subject Name' placeholder='Enter subject name' required />

            {/* Is Active */}
            <PrimarySwitchInput
              form={form}
              name='isActive'
              label='Is this subject active?'
              description='Making it inactive will hide it from the website.'
            />

            <SubmitButton
              submittingLabel={subjectId ? 'Updating...' : 'Creating...'}
              label={subjectId ? 'Update Subject' : 'Create Subject'}
              isSubmitting={isSubmitting}
              disabled={isSubmitting}
              className='w-fit'
            />
          </form>
        </Form>
      </div>
    </section>
  );
};

export default SubjectForm;
