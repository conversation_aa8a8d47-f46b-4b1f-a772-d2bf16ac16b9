'use client';

import { useState, useEffect, useCallback } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { AdminDashActionLinks, HeadingBar, LimitFilter, MasterFilter, MasterTable, SectionWrapper } from '@/components/dashboard/ascrm/misc';
import { ErrorState } from '@/components/dashboard/ascrm/misc/StateUI';
import { filterTypeMaps, FilterTypeKey } from '@/constants';
import { FilterValues, FilterOption } from '@/types/api';
import { useGetAllHobbyTypes, useDeleteHobbyType, useGetHobbyTypeById } from '@/hooks/education/hobby.hooks';
import { AdminViewButton, PrimaryFormModal, useModal, StatusToggle } from '@/components/dashboard/shared/misc';
import HobbyTypeForm from './hobby-type-form';
import { hobbyTypeService } from '@/server/services/education/hobby.service';
import { QUERY_KEYS } from '@/lib/react-query/queryKeys';

const HobbyTypeTable: React.FC = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const addHobbyTypeModal = useModal();
  const editHobbyTypeModal = useModal();

  const [selectedHobbyTypeId, setSelectedHobbyTypeId] = useState<string | null>(null);
  const [filters, setFilters] = useState<FilterValues>({});

  const updateFiltersFromParams = useCallback(() => {
    const initialFilters: FilterValues = {};
    searchParams.forEach((value, key) => {
      if (key !== 'page') {
        initialFilters[key] = value;
      }
    });
    setFilters(initialFilters);
  }, [searchParams]);

  useEffect(() => {
    updateFiltersFromParams();
  }, [updateFiltersFromParams]);

  const page = Number(searchParams.get('page')) || 1;

  const queryParams = { page, ...filters };

  const { data: hobbyTypesResponse, isLoading, error } = useGetAllHobbyTypes(queryParams);
  const deleteItem = useDeleteHobbyType();
  const {
    data: selectedHobbyTypeData,
    isLoading: isLoadingSelectedHobbyType,
    error: selectedHobbyTypeError,
  } = useGetHobbyTypeById(selectedHobbyTypeId || '');

  const hobbyTypes = hobbyTypesResponse?.data?.hobbyTypes || [];
  const pagination = hobbyTypesResponse?.data?.pagination;

  const handleEditHobbyType = (id: string) => {
    setSelectedHobbyTypeId(id);
    editHobbyTypeModal.open();
  };

  if (error) {
    return <ErrorState message={error?.message || 'An unexpected error occurred.'} />;
  }

  const headers = ['Name', 'Status', 'Actions'];

  const rows = hobbyTypes.map((hobbyType) => {
    return {
      id: hobbyType._id,
      values: [
        <p className='font-medium'>{hobbyType.name}</p>,
        <StatusToggle
          id={hobbyType._id}
          isActive={hobbyType.isActive}
          updateFn={hobbyTypeService.updateHobbyType}
          queryKey={[QUERY_KEYS.EDUCATION.HOBBY_TYPES, [QUERY_KEYS.EDUCATION.HOBBY_TYPES, filters]]}
          entityName='Hobby Type'
        />,
        <AdminDashActionLinks
          delete
          editAction={() => handleEditHobbyType(hobbyType._id)}
          basePath='/hobby-types'
          id={hobbyType._id}
          itemName={hobbyType.name}
          deleteItem={() => deleteItem.mutateAsync(hobbyType._id)}
          manualAction={[<AdminViewButton variant='icon' href={`/hobbies?hobbyType=${hobbyType._id}`} label={`View ${hobbyType.name} Hobbies`} />]}
        />,
      ],
    };
  });

  const filterOptions: FilterOption[] = [
    {
      type: filterTypeMaps.input.key as FilterTypeKey,
      label: 'Hobby Type Name',
      key: 'name',
    },
  ];

  const handleApplyFilters = (newFilters: FilterValues) => {
    const params = new URLSearchParams(searchParams);
    Object.entries(newFilters).forEach(([key, value]) => {
      if (value && typeof value === 'string') {
        params.set(key, value);
      } else {
        params.delete(key);
      }
    });
    params.set('page', '1');
    router.push(`?${params.toString()}`);
  };

  const handleResetFilters = () => {
    router.push(`?page=1`);
  };

  return (
    <SectionWrapper>
      <HeadingBar
        title='Hobby Types'
        elements={[
          <LimitFilter
            key='limit'
            value={(filters.limit as string) || '10'}
            onChange={(value: string) => {
              handleApplyFilters({ limit: value });
            }}
          />,
          <MasterFilter
            key='filter'
            filterOptions={filterOptions}
            onApplyFilters={handleApplyFilters}
            onResetFilters={handleResetFilters}
            initialFilters={filters}
          />,
        ]}
        addAction={addHobbyTypeModal.open}
      />
      <MasterTable headers={headers} rows={rows} isLoading={isLoading} pagination={pagination} />

      {/* Add Hobby Type Modal */}
      <PrimaryFormModal
        isOpen={addHobbyTypeModal.isOpen}
        onClose={addHobbyTypeModal.close}
        title='Add New Hobby Type'
        subtitle='Create a new hobby type for your platform'
        size='sm'
        showSparkles={true}
      >
        <HobbyTypeForm onSuccess={addHobbyTypeModal.close} />
      </PrimaryFormModal>

      {/* Edit Hobby Type Modal */}
      <PrimaryFormModal
        isOpen={editHobbyTypeModal.isOpen}
        onClose={() => {
          editHobbyTypeModal.close();
          setSelectedHobbyTypeId(null);
        }}
        title='Edit Hobby Type'
        subtitle='Update existing hobby type details'
        size='sm'
        isLoading={isLoadingSelectedHobbyType}
        loadingTitle='Loading Hobby Type Data'
        loadingMessage='Please wait while we fetch the hobby type details...'
        isError={!!selectedHobbyTypeError}
        errorMessage={selectedHobbyTypeError?.message}
      >
        {selectedHobbyTypeData?.data?.hobbyType && <HobbyTypeForm data={selectedHobbyTypeData.data.hobbyType} onSuccess={editHobbyTypeModal.close} />}
      </PrimaryFormModal>
    </SectionWrapper>
  );
};

export default HobbyTypeTable;
