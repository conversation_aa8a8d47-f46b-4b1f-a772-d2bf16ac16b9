import { Lock, ShieldCheck, User } from 'lucide-react';
import { Checkbox } from '@/components/ui/checkbox';
import Link from 'next/link';

const TutorKycWelcomePage = () => {
  return (
    <section className='bg-white p-6 rounded-3xl relative min-h-[650px]'>
      <div className='flex flex-col md:flex-row items-center gap-8'>
        <div className='w-full md:w-1/2 flex flex-col gap-4'>
          <h2 className='text-2xl font-semibold'>Start Your KYC Process Easily</h2>
          <img src='/icons/kyc-welcome.webp' alt='KYC Welcome Page' className='max-w-full h-auto md:hidden' />
          <p className='text-gray-600'>
            Complete your KYC to begin your journey as a tutor. Please ensure that your documents are prepared for a seamless verification process.
          </p>
          <p className='text-gray-600'>Our platform guarantees secure and encrypted data handling to protect your information.</p>
          <div className='grid grid-cols-1 md:grid-cols-3 gap-4'>
            <div className='flex flex-col items-center gap-2 py-4 px-3 rounded-xl border bg-white'>
              <ShieldCheck size={40} className='text-primaryColor' />
              <p className='text-center text-gray-600'>We ensure the highest level of data security</p>
            </div>
            <div className='flex flex-col items-center gap-2 py-4 px-3 rounded-xl border bg-white'>
              <Lock size={40} className='text-primaryColor' />
              <p className='text-center text-gray-600'>Your information is encrypted with industry standards</p>
            </div>
            <div className='flex flex-col items-center gap-2 py-4 px-3 rounded-xl border bg-white'>
              <User size={40} className='text-primaryColor' />
              <p className='text-center text-gray-600'>Manage your personal data with ease and transparency</p>
            </div>
          </div>
          <p className='text-gray-600'>
            We require an Aadhar card as a mandatory document. Additionally, please provide your PAN card. If you do not have access to a PAN card,
            you may upload another valid document.
          </p>

          <div className='flex items-center gap-x-2'>
            <Checkbox id='terms' />
            <label htmlFor='terms' className='text-sm font-medium text-gray-600'>
              I accept terms and conditions
            </label>
          </div>

          <button className='btn-default-md'>Start Digital KYC (OTP)</button>
          <Link href='/tutor-dash/profiles/kyc/manual' className='btn-default__outline-md'>
            Start With Manual (Doc Upload)
          </Link>
        </div>
        <div className='w-full md:w-1/2 flex justify-center max-md:hidden'>
          <img src='/icons/kyc-welcome.webp' alt='KYC Welcome Page' className='max-w-full h-auto' />
        </div>
      </div>
    </section>
  );
};

export default TutorKycWelcomePage;
