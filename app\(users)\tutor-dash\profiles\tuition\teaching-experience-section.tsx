'use client';

import { useState } from 'react';
import { Briefcase, Plus, Search } from 'lucide-react';
import { Backgrounds, VisitorsDeleteModal, PrimaryBadgeTabs } from '@/components/dashboard/shared/misc';
import TeachingExperienceForm from './teaching-experience-form';
import TeachingExperienceCard from './teaching-experience-card';
import { ITeachingExperienceDocument } from '@/server/services/tutor/tuition-profile.service';
import { useDeleteTeachingExperience } from '@/hooks/tutor/tuition-profile.hooks';
import { tuitionTypeMap } from '@/validation/schemas/tutor/tuition.maps';
import { BadgeTabItem } from '@/components/dashboard/shared/misc/primary-badge-tabs';

interface TeachingExperienceSectionProps {
  experiences: ITeachingExperienceDocument[];
}

const TeachingExperienceSection = ({ experiences }: TeachingExperienceSectionProps) => {
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingExperience, setEditingExperience] = useState<ITeachingExperienceDocument | undefined>(undefined);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [experienceToDelete, setExperienceToDelete] = useState<ITeachingExperienceDocument | null>(null);

  const deleteExperience = useDeleteTeachingExperience();

  const handleAddNew = () => {
    setEditingExperience(undefined);
    setIsFormOpen(true);
  };

  const handleEdit = (experience: ITeachingExperienceDocument) => {
    setEditingExperience(experience);
    setIsFormOpen(true);
  };

  const handleDeleteClick = (experience: ITeachingExperienceDocument) => {
    setExperienceToDelete(experience);
    setDeleteModalOpen(true);
  };

  const closeForm = () => {
    setEditingExperience(undefined);
    setIsFormOpen(false);
  };

  // Count experiences by type
  const [activeTab, setActiveTab] = useState('all');

  const privateCount = experiences.filter((exp) => exp.tuitionType === tuitionTypeMap.private.key).length;
  const schoolCount = experiences.filter((exp) => exp.tuitionType === tuitionTypeMap.school.key).length;
  const collegeCount = experiences.filter((exp) => exp.tuitionType === tuitionTypeMap.college.key).length;
  const instituteCount = experiences.filter((exp) => exp.tuitionType === tuitionTypeMap.institute.key).length;
  const otherCount = experiences.filter((exp) => exp.tuitionType === tuitionTypeMap.other.key).length;
  const allCount = experiences.length;

  const filteredExperiences = experiences.filter((exp) => (activeTab === 'all' ? true : exp.tuitionType === activeTab));

  const tabs: BadgeTabItem[] = [
    { id: 'all', label: 'All Experiences', count: allCount, variant: 'primary' },
    { id: tuitionTypeMap.private.key, label: 'Private', count: privateCount, variant: 'green' },
    { id: tuitionTypeMap.school.key, label: 'Schools', count: schoolCount, variant: 'blue' },
    { id: tuitionTypeMap.college.key, label: 'Colleges', count: collegeCount, variant: 'purple' },
    { id: tuitionTypeMap.institute.key, label: 'Institutes', count: instituteCount, variant: 'orange' },
    { id: tuitionTypeMap.other.key, label: 'Other', count: otherCount, variant: 'gray' },
  ];

  return (
    <>
      <div className='bg-white rounded-2xl p-6 border border-gray-100 shadow-sm relative overflow-hidden'>
        <Backgrounds variant='primary' />
        <div className='relative z-10 flex flex-col md:flex-row justify-between items-start md:items-center'>
          <div>
            <div className='flex items-center gap-2'>
              <h2 className='text-2xl font-bold text-gray-800'>Teaching Experience</h2>
              <span className='px-3 py-1 bg-green-50 text-green-600 rounded-full text-xs font-medium'>
                {experiences.length} {experiences.length === 1 ? 'experience' : 'experiences'}
              </span>
            </div>
            <p className='text-sm text-gray-500 mt-1'>Manage your teaching experience and work history</p>
          </div>

          <div className='flex gap-3 mt-4 md:mt-0'>
            <button
              onClick={handleAddNew}
              className='flex items-center gap-2 px-5 py-2.5 bg-gradient-to-r from-red-500 to-red-600 text-white rounded-lg font-medium hover:from-red-600 hover:to-red-700 transition-all duration-300 shadow-md hover:shadow-lg hover:scale-[1.02]'
            >
              <Plus size={18} />
              <span>Add Experience</span>
            </button>
          </div>
        </div>
      </div>

      {/* Experience Type Tabs */}
      <PrimaryBadgeTabs tabs={tabs} activeTab={activeTab} onChange={setActiveTab} className='my-6' />

      {experiences.length > 0 ? (
        <>
          {filteredExperiences.length > 0 ? (
            <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
              {filteredExperiences.map((experience) => (
                <TeachingExperienceCard
                  key={experience._id}
                  experience={experience}
                  onEdit={handleEdit}
                  onDelete={() => handleDeleteClick(experience)}
                />
              ))}
            </div>
          ) : (
            <div className='flex flex-col items-center justify-center py-10 text-center bg-white rounded-xl border border-gray-100 relative overflow-hidden'>
              <div className='absolute top-0 inset-x-0 h-1.5 bg-gradient-to-r from-gray-300 to-gray-400'></div>
              <div className='absolute -right-12 -top-12 w-32 h-32 bg-gray-50 rounded-full opacity-40'></div>

              <div className='w-20 h-20 rounded-full bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center mb-5 shadow-sm relative'>
                <Search className='text-gray-500' size={32} />
              </div>

              <h4 className='text-2xl font-semibold text-gray-700 mb-2'>
                No{' '}
                {activeTab === 'all'
                  ? ''
                  : activeTab === tuitionTypeMap.private.key
                  ? 'Private'
                  : activeTab === tuitionTypeMap.school.key
                  ? 'School'
                  : activeTab === tuitionTypeMap.college.key
                  ? 'College'
                  : activeTab === tuitionTypeMap.institute.key
                  ? 'Institute'
                  : 'Other'}{' '}
                experiences
              </h4>
              <p className='text-gray-500 max-w-md mb-6'>Try selecting a different experience type or add a new experience.</p>
              <button
                onClick={() => setActiveTab('all')}
                className='flex items-center gap-2 px-5 py-2.5 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-lg font-medium hover:from-blue-600 hover:to-blue-700 transition-all duration-300 shadow-md hover:shadow-lg hover:scale-[1.02]'
              >
                <Search size={18} />
                <span>View all experiences</span>
              </button>
            </div>
          )}
        </>
      ) : (
        <div className='flex flex-col items-center justify-center py-12 text-center bg-white rounded-xl border border-gray-100 shadow-sm relative overflow-hidden'>
          <div className='absolute top-0 inset-x-0 h-2 bg-gradient-to-r from-green-400 to-green-600'></div>
          <div className='absolute -right-12 -top-12 w-48 h-48 bg-green-50 rounded-full opacity-50'></div>
          <div className='absolute -left-12 -bottom-12 w-48 h-48 bg-blue-50 rounded-full opacity-50'></div>

          <div className='w-24 h-24 rounded-full bg-gradient-to-br from-green-50 to-green-100 flex items-center justify-center mb-6 shadow-inner'>
            <Briefcase className='text-green-500' size={40} />
          </div>

          <h4 className='text-2xl font-bold text-gray-800 mb-2'>No teaching experience found</h4>
          <p className='text-gray-500 max-w-md mb-8'>
            Add your teaching experience to showcase your expertise and help students understand your background.
          </p>

          <button
            onClick={handleAddNew}
            className='flex items-center gap-2 px-7 py-3.5 bg-gradient-to-r from-red-500 to-red-600 text-white rounded-lg font-semibold hover:from-red-600 hover:to-red-700 transition-all duration-300 shadow-lg hover:shadow-xl hover:scale-[1.03] transform-gpu'
          >
            <Plus size={20} className='text-white' />
            <span>Add Teaching Experience</span>
          </button>
        </div>
      )}

      {/* Teaching Experience Form Modal */}
      <TeachingExperienceForm isOpen={isFormOpen} onClose={closeForm} experience={editingExperience} />

      {/* Delete Confirmation Modal */}
      <VisitorsDeleteModal
        isOpen={deleteModalOpen}
        onClose={() => {
          setDeleteModalOpen(false);
          setExperienceToDelete(null);
        }}
        onDelete={() => deleteExperience.mutateAsync(experienceToDelete?._id || '')}
        itemName={
          experienceToDelete
            ? `${tuitionTypeMap[experienceToDelete.tuitionType]?.label || experienceToDelete.tuitionType} experience at ${
                experienceToDelete.placeName
              }`
            : 'this experience'
        }
      />
    </>
  );
};

export default TeachingExperienceSection;
