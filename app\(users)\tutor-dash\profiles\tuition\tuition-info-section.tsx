'use client';

import { useState } from 'react';
import { User, Plus, Edit, Clock, MapPin, Languages, Users, BookOpen, Home } from 'lucide-react';
import { Backgrounds } from '@/components/dashboard/shared/misc';
import TuitionInfoForm from './tuition-info-form';
import { ITuitionInfoDocument } from '@/server/services/tutor/tuition-profile.service';
import { deliveryModeMap } from '@/validation/schemas/enquiry.maps';
import { languagesMap } from '@/constants';

interface TuitionInfoSectionProps {
  tuitionInfo?: ITuitionInfoDocument;
}

const TuitionInfoSection = ({ tuitionInfo }: TuitionInfoSectionProps) => {
  const [isFormOpen, setIsFormOpen] = useState(false);

  const handleAddEdit = () => {
    setIsFormOpen(true);
  };

  const deliveryModesDisplay = tuitionInfo?.deliveryModes?.map((mode) => deliveryModeMap[mode]?.label || mode).join(', ') || 'Not specified';

  const spokenLanguagesDisplay =
    tuitionInfo?.spokenLanguages?.map((lang) => languagesMap[lang as keyof typeof languagesMap]?.label || lang).join(', ') || 'Not specified';

  const formatExperience = (months: number | undefined) => {
    if (!months) return 'Not specified';

    const years = Math.floor(months / 12);
    const remainingMonths = months % 12;
    if (years > 0) {
      return remainingMonths > 0
        ? `${years} year${years > 1 ? 's' : ''} ${remainingMonths} month${remainingMonths > 1 ? 's' : ''}`
        : `${years} year${years > 1 ? 's' : ''}`;
    }
    return `${months} month${months > 1 ? 's' : ''}`;
  };

  return (
    <>
      <div className='bg-white rounded-2xl p-6 border border-gray-100 shadow-sm mb-8 relative overflow-hidden'>
        <Backgrounds variant='primary' />
        <div className='relative z-10 flex flex-col md:flex-row justify-between items-start md:items-center'>
          <div>
            <div className='flex items-center gap-2'>
              <h2 className='text-2xl font-bold text-gray-800'>Tuition Information</h2>
              {tuitionInfo && <span className='px-3 py-1 bg-blue-50 text-blue-600 rounded-full text-xs font-medium'>Profile Complete</span>}
            </div>
            <p className='text-sm text-gray-500 mt-1'>
              {tuitionInfo ? 'Manage your teaching information and preferences' : 'Add your basic tuition information to get started'}
            </p>
          </div>

          <button
            onClick={handleAddEdit}
            className='mt-4 md:mt-0 flex items-center gap-2 px-5 py-2.5 bg-gradient-to-r from-red-500 to-red-600 text-white rounded-lg font-medium hover:from-red-600 hover:to-red-700 transition-all duration-300 shadow-md hover:shadow-lg hover:scale-[1.02]'
          >
            {tuitionInfo ? <Edit size={18} /> : <Plus size={18} />}
            <span>{tuitionInfo ? 'Edit Information' : 'Add Information'}</span>
          </button>
        </div>
      </div>
      {tuitionInfo ? (
        <div className='bg-white rounded-2xl border border-gray-200 overflow-hidden shadow-sm'>
          <div className='h-1.5 bg-gradient-to-r from-blue-500 to-blue-600'></div>

          <div className='p-6'>
            <div className='flex justify-between items-start mb-6'>
              <div className='flex items-start gap-3'>
                <div className='relative w-12 h-12 rounded-lg bg-gradient-to-br from-blue-50 to-blue-100 p-0.5 shadow-sm'>
                  <div className='absolute inset-0 rounded-lg bg-gradient-to-br from-blue-500 to-blue-600 opacity-10'></div>
                  <div className='w-full h-full rounded-lg bg-white flex items-center justify-center'>
                    <User size={22} className='text-blue-600' />
                  </div>
                </div>

                <div>
                  <div className='flex items-center gap-2 mb-1'>
                    <h3 className='font-bold text-gray-800'>Teaching Profile</h3>
                    <span className='px-2 py-0.5 text-xs font-medium rounded-full bg-blue-50 text-blue-600'>
                      {tuitionInfo.isFullTimeTeacher ? 'Full-time' : 'Part-time'}
                    </span>
                  </div>
                  <p className='text-sm text-gray-600'>{formatExperience(tuitionInfo.totalTeachingExperience)} of teaching experience</p>
                </div>
              </div>
            </div>
            <div className='grid grid-cols-1 md:grid-cols-2 gap-4 mb-6'>
              <div className='flex items-center gap-2.5 group/item'>
                <div className='w-8 h-8 rounded-lg bg-blue-50 flex items-center justify-center group-hover/item:bg-blue-500 transition-colors'>
                  <Clock size={16} className='text-blue-600 group-hover/item:text-white transition-colors' />
                </div>
                <div>
                  <p className='text-xs text-gray-500'>Teaching Experience</p>
                  <p className='text-sm font-medium text-gray-800'>{formatExperience(tuitionInfo.totalTeachingExperience)}</p>
                </div>
              </div>

              <div className='flex items-center gap-2.5 group/item'>
                <div className='w-8 h-8 rounded-lg bg-blue-50 flex items-center justify-center group-hover/item:bg-blue-500 transition-colors'>
                  <Users size={16} className='text-blue-600 group-hover/item:text-white transition-colors' />
                </div>
                <div>
                  <p className='text-xs text-gray-500'>Special Students</p>
                  <p className='text-sm font-medium text-gray-800'>
                    {tuitionInfo.teachesSpecialStudents ? 'Yes, I teach special students' : 'Regular students only'}
                  </p>
                </div>
              </div>

              <div className='flex items-center gap-2.5 group/item'>
                <div className='w-8 h-8 rounded-lg bg-blue-50 flex items-center justify-center group-hover/item:bg-blue-500 transition-colors'>
                  <MapPin size={16} className='text-blue-600 group-hover/item:text-white transition-colors' />
                </div>
                <div>
                  <p className='text-xs text-gray-500'>Travel Distance</p>
                  <p className='text-sm font-medium text-gray-800'>Up to {tuitionInfo.maxTravelDistance} km</p>
                </div>
              </div>

              <div className='flex items-center gap-2.5 group/item'>
                <div className='w-8 h-8 rounded-lg bg-blue-50 flex items-center justify-center group-hover/item:bg-blue-500 transition-colors'>
                  <Home size={16} className='text-blue-600 group-hover/item:text-white transition-colors' />
                </div>
                <div>
                  <p className='text-xs text-gray-500'>Teaching Modes</p>
                  <p className='text-sm font-medium text-gray-800'>{deliveryModesDisplay}</p>
                </div>
              </div>

              <div className='flex items-center gap-2.5 group/item'>
                <div className='w-8 h-8 rounded-lg bg-blue-50 flex items-center justify-center group-hover/item:bg-blue-500 transition-colors'>
                  <Languages size={16} className='text-blue-600 group-hover/item:text-white transition-colors' />
                </div>
                <div>
                  <p className='text-xs text-gray-500'>Languages</p>
                  <p className='text-sm font-medium text-gray-800'>{spokenLanguagesDisplay}</p>
                </div>
              </div>

              <div className='flex items-center gap-2.5 group/item'>
                <div className='w-8 h-8 rounded-lg bg-blue-50 flex items-center justify-center group-hover/item:bg-blue-500 transition-colors'>
                  <MapPin size={16} className='text-blue-600 group-hover/item:text-white transition-colors' />
                </div>
                <div>
                  <p className='text-xs text-gray-500'>Location</p>
                  <p className='text-sm font-medium text-gray-800'>{tuitionInfo.location}</p>
                </div>
              </div>
            </div>{' '}
            {tuitionInfo.description && (
              <div className='bg-gradient-to-br from-blue-50/60 to-white rounded-xl p-5 border border-blue-100 shadow-sm relative overflow-hidden'>
                <div className='absolute top-0 right-0 w-24 h-24 bg-blue-100/30 rounded-full -mr-10 -mt-10 opacity-50'></div>

                <div className='relative flex space-x-3'>
                  <div className='flex-shrink-0'>
                    <div className='w-10 h-10 rounded-full bg-gradient-to-br from-blue-500 to-blue-600 p-0.5 shadow-md'>
                      <div className='w-full h-full rounded-full bg-white flex items-center justify-center'>
                        <BookOpen className='w-5 h-5 text-blue-600' />
                      </div>
                    </div>
                  </div>

                  <div className='flex-1'>
                    <h4 className='font-semibold text-gray-900 text-base mb-3 flex items-center'>
                      About Me
                      <span className='ml-2 px-2.5 py-0.5 text-xs font-medium rounded-full bg-blue-100 text-blue-700'>Biography</span>
                    </h4>

                    <div className='relative'>
                      <div className='absolute left-0 top-0 bottom-0 w-1 bg-gradient-to-b from-blue-500/50 to-blue-400/20 rounded-full'></div>
                      <p className='pl-3 text-gray-700 leading-relaxed text-sm'>{tuitionInfo.description}</p>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      ) : (
        <div className='flex flex-col items-center justify-center py-12 text-center bg-white rounded-xl border border-gray-100 shadow-sm relative overflow-hidden'>
          <div className='absolute top-0 inset-x-0 h-2 bg-gradient-to-r from-blue-400 to-blue-600'></div>
          <div className='absolute -right-12 -top-12 w-48 h-48 bg-blue-50 rounded-full opacity-50'></div>
          <div className='absolute -left-12 -bottom-12 w-48 h-48 bg-red-50 rounded-full opacity-50'></div>

          <div className='w-24 h-24 rounded-full bg-gradient-to-br from-blue-50 to-blue-100 flex items-center justify-center mb-6 shadow-inner'>
            <User className='text-blue-500' size={40} />
          </div>

          <h4 className='text-2xl font-bold text-gray-800 mb-2'>No tuition information found</h4>
          <p className='text-gray-500 max-w-md mb-8'>
            Add your basic tuition information to help students find you and understand your teaching approach.
          </p>

          <button
            onClick={handleAddEdit}
            className='flex items-center gap-2 px-7 py-3.5 bg-gradient-to-r from-red-500 to-red-600 text-white rounded-lg font-semibold hover:from-red-600 hover:to-red-700 transition-all duration-300 shadow-lg hover:shadow-xl hover:scale-[1.03] transform-gpu'
          >
            <Plus size={20} className='text-white' />
            <span>Add Tuition Information</span>
          </button>
        </div>
      )}
      {/* Tuition Info Form Modal */}
      <TuitionInfoForm isOpen={isFormOpen} onClose={() => setIsFormOpen(false)} tuitionInfo={tuitionInfo || undefined} />
    </>
  );
};

export default TuitionInfoSection;
