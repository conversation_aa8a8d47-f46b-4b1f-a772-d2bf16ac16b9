import apiClient from '@/server/apiClient';
import { IPagination } from '@/types/api';
import { CreateChildProfileInput } from '@/validation/schemas/parent/child-profile.schema';
import { IBoardDocument, IClassDocument } from '@/server/services/education/school.service';
import { IStreamDocument, IDegreeLevelDocument, IDegreeDocument, IBranchDocument } from '@/server/services/education/college.service';
import { IScoreTypeMap } from '@/validation/schemas/parent/education.maps';
import { CreateEducationDetailInput, UpdateEducationDetailInput } from '@/validation/schemas/parent/education-detail.schema';
import { IBusinessLocationDocument } from './user.service';

// Types for Child Profiles
export interface IChildProfileDocument extends CreateChildProfileInput {
  _id: string;
  userId: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface IChildProfilesResponse {
  childProfiles: IChildProfileDocument[];
  pagination?: IPagination;
}

export interface IChildProfileResponse {
  childProfile: IChildProfileDocument;
}

// Types for Education Details
export interface IEducationDetailDocument {
  _id: string;
  childProfileId: string;
  educationType: 'school' | 'degree' | 'other';

  // School specific fields
  boardId?: string;
  boardDetails?: IBoardDocument;
  classId?: string;
  classDetails?: IClassDocument;
  schoolName?: string;

  // Degree specific fields
  streamId?: string;
  streamDetails?: IStreamDocument;
  degreeLevelId?: string;
  degreeLevelDetails?: IDegreeLevelDocument;
  degreeId?: string;
  degreeDetails?: IDegreeDocument;
  branchId?: string;
  branchDetails?: IBranchDocument;
  collegeName?: string;

  // Other achievement specific fields
  certificateName?: string;
  certificateFor?: string;
  certificateBy?: string;

  // Common fields
  businessLocationId?: string;
  location: string;
  startDate: string;
  endDate?: string;
  scoreType: IScoreTypeMap;
  obtainedValue: string;
  maximumValue: number;
  certificateNumber?: string;
  attachmentUrl?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;

  // Populated fields
  childProfileDetails?: IChildProfileDocument;
  businessLocationDetails?: IBusinessLocationDocument;
}

export interface IEducationDetailsResponse {
  educationDetails: IEducationDetailDocument[];
  pagination?: IPagination;
}

export interface IEducationDetailResponse {
  educationDetail: IEducationDetailDocument;
}

// Child Profile Service
const childProfileService = {
  getAllChildProfiles: async (params?: Record<string, any>) => {
    const queryParams = new URLSearchParams(params);
    return await apiClient.get<IChildProfilesResponse>(`/profile/child-profiles?${queryParams.toString()}`);
  },

  getChildProfileById: async (id: string) => {
    return await apiClient.get<IChildProfileResponse>(`/profile/child-profiles/${id}`);
  },

  createChildProfile: async (data: FormData) => {
    return await apiClient.post<IChildProfileResponse>('/profile/child-profiles', data);
  },

  updateChildProfile: async (id: string, data: FormData) => {
    return await apiClient.patch<IChildProfileResponse>(`/profile/child-profiles/${id}`, data);
  },

  deleteChildProfile: async (id: string) => {
    return await apiClient.delete(`/profile/child-profiles/${id}`);
  },
};

// Education Detail Service
const educationDetailService = {
  getAllEducationDetails: async (params?: Record<string, any>) => {
    const queryParams = new URLSearchParams(params);
    return await apiClient.get<IEducationDetailsResponse>(`/profile/education-details?${queryParams.toString()}`);
  },

  getEducationDetailById: async (id: string) => {
    return await apiClient.get<IEducationDetailResponse>(`/profile/education-details/${id}`);
  },

  createEducationDetail: async (data: CreateEducationDetailInput | FormData) => {
    return await apiClient.post<IEducationDetailResponse>('/profile/education-details', data);
  },

  updateEducationDetail: async (id: string, data: UpdateEducationDetailInput | FormData) => {
    return await apiClient.patch<IEducationDetailResponse>(`/profile/education-details/${id}`, data);
  },

  deleteEducationDetail: async (id: string) => {
    return await apiClient.delete(`/profile/education-details/${id}`);
  },
};

// Combined profile service
const profileService = {
  childProfiles: childProfileService,
  educationDetails: educationDetailService,
};

export default profileService;
