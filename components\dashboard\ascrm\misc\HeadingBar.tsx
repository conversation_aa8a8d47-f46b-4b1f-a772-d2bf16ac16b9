'use client';

import { CirclePlus, List } from 'lucide-react';
import AdminDashLink from './AdminDashLink';
import { Button } from '@/components/ui/button';
import React from 'react';

interface IHeadingBar {
  title: string;
  addPath?: string;
  listPath?: string;
  elements?: React.ReactNode[];
  addAction?: () => void;
}

const HeadingBar: React.FC<IHeadingBar> = ({ title, addPath, listPath, elements, addAction }) => {
  return (
    <div className='bg-white flex flex-col sm:flex-row justify-between items-start sm:items-center p-6 gap-4'>
      <h2 className='text-lg font-medium capitalize'>{title}</h2>
      <div className='flex flex-wrap items-center gap-3'>
        {elements && elements.map((element, index) => <React.Fragment key={index}>{element}</React.Fragment>)}
        {listPath && (
          <Button asChild variant='secondary' size='sm' className='rounded-full'>
            <AdminDashLink href={listPath} className='flex items-center space-x-2'>
              <List size={16} />
              <span className='hidden sm:inline'>View List</span>
            </AdminDashLink>
          </Button>
        )}
        {addAction ? (
          <Button
            variant='default'
            size='sm'
            className='rounded-full bg-gradient-to-l from-primaryColor-500 to-gray-950 hover:to-primaryColor-600 hover:from-gray-900 text-white shadow-md transition-all duration-300 transform hover:scale-105'
            onClick={addAction}
          >
            <span className='flex items-center space-x-2'>
              <CirclePlus size={16} className='animate-pulse' />
              <span className='hidden sm:inline font-semibold'>Add New</span>
            </span>
          </Button>
        ) : (
          addPath && (
            <Button asChild variant='default' size='sm' className='rounded-full'>
              <AdminDashLink href={addPath} className='flex items-center space-x-2'>
                <CirclePlus size={16} />
                <span className='hidden sm:inline'>Add New</span>
              </AdminDashLink>
            </Button>
          )
        )}
      </div>
    </div>
  );
};

export default HeadingBar;
