'use client';

import { Input } from '@/components/ui/input';
import { cn } from '@/lib/utils';
import { ChevronDown, X } from 'lucide-react';
import { useState, useRef, useEffect } from 'react';

interface IMultiSelectCheckbox {
  options: Array<{ label: string; value: string }>;
  selectedOptions: string[];
  onChange: (value: string[]) => void;
}

const MultiSelectWithSearch: React.FC<IMultiSelectCheckbox> = ({ options, selectedOptions, onChange }) => {
  const isValidArray = Array.isArray(selectedOptions) && selectedOptions.length > 0;

  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const dropdownRef = useRef<HTMLDivElement>(null);

  selectedOptions = isValidArray ? selectedOptions : [];

  const handleCheckboxChange = (value: string) => {
    console.log(selectedOptions);
    const newSelectedOptions =
      isValidArray && selectedOptions?.includes(value) ? selectedOptions.filter((option) => option !== value) : [...selectedOptions, value];
    onChange(newSelectedOptions);
  };

  const handleDropdownToggle = () => {
    setDropdownOpen(!dropdownOpen);
  };

  const handleClickOutside = (event: MouseEvent) => {
    if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
      setDropdownOpen(false);
    }
  };

  useEffect(() => {
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const filteredOptions = options.filter((option) => option.label.toLowerCase().includes(searchTerm.toLowerCase()));

  return (
    <div ref={dropdownRef} className={cn('relative w-full inline-flex', dropdownOpen ? 'flex-col-reverse gap-4' : 'flex-col gap-2')}>
      <div className='relative'>
        <div className='flex justify-between items-center relative'>
          <Input
            onClick={handleDropdownToggle}
            type='text'
            placeholder={'Select...'}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className='primary-input'
          />
          <ChevronDown size={16} className='text-gray-400 right-3.5 absolute' />
        </div>
        {dropdownOpen && (
          <div className='absolute top-14 mt-1 w-full p-4 border-t-[3px] flex gap-2 flex-wrap border-primaryColor-50 bg-white rounded-xl shadow-lg z-10'>
            {filteredOptions.map((option) => (
              <label key={option.value} className='flex items-center cursor-pointer text-sm p-2 bg-primaryColor-50 rounded'>
                <input
                  type='checkbox'
                  checked={isValidArray && selectedOptions.includes(option.value)}
                  onChange={() => handleCheckboxChange(option.value)}
                  className='mr-2 accent-primaryColor size-4'
                />
                {option.label}
              </label>
            ))}
          </div>
        )}
      </div>

      <div className='flex flex-wrap gap-2'>
        {isValidArray &&
          selectedOptions?.map((value) => {
            const selectedOption = options.find((option) => option.value === value);
            return (
              <div key={value} className='flex items-center bg-primaryColor-50 py-1 px-3 gap-2 rounded-full'>
                {selectedOption?.label}
                <div className='bg-primaryColor text-white p-1 rounded-full'>
                  <X size={12} className='cursor-pointer' onClick={() => handleCheckboxChange(value)} />
                </div>
              </div>
            );
          })}
      </div>
    </div>
  );
};

export default MultiSelectWithSearch;
