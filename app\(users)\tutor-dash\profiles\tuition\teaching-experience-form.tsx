'use client';

import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { toast } from 'react-toastify';
import { Form } from '@/components/ui/form';
import { useState, useEffect, useMemo } from 'react';
import { Briefcase, Building } from 'lucide-react';
import { PrimaryInput, PrimarySelect, PrimaryAutoCompleteInput, SubmitButton, CancelButton, PrimaryLocationInput } from '@/components/forms';
import { useUserSearchBusinessLocation } from '@/hooks/users/user.hooks';
import { PrimaryModalWithHeader } from '@/components/dashboard/shared/misc';
import { createTeachingExperienceSchema, CreateTeachingExperienceInput } from '@/validation/schemas/tutor/profiles/teaching-experience.schema';
import { useCreateTeachingExperience, useUpdateTeachingExperience } from '@/hooks/tutor/tuition-profile.hooks';
import { tuitionTypeMap, tuitionTypeOptions } from '@/validation/schemas/tutor/tuition.maps';
import { ITeachingExperienceDocument } from '@/server/services/tutor/tuition-profile.service';
import { FaInfoCircle } from 'react-icons/fa';
import { IAddressOutput } from '@/components/forms/primary-location-input';

interface TeachingExperienceFormProps {
  isOpen: boolean;
  onClose: () => void;
  experience?: ITeachingExperienceDocument;
}

const TeachingExperienceForm = ({ isOpen, onClose, experience }: TeachingExperienceFormProps) => {
  if (!isOpen) return null;

  const [hideNameLocation, setHideNameLocation] = useState(false);
  const createExperience = useCreateTeachingExperience();
  const updateExperience = useUpdateTeachingExperience();

  const isSubmitting = createExperience.isPending || updateExperience.isPending;
  const isEditing = !!experience;

  const form = useForm<CreateTeachingExperienceInput>({
    resolver: zodResolver(createTeachingExperienceSchema),
    mode: 'onChange',
    defaultValues: {
      tuitionType: (experience?.tuitionType ? experience.tuitionType : 'private') as any,
      experienceMonths: experience?.experienceMonths || 0,
      placeName: experience?.placeName || 'Self',
      location: experience?.location || 'Self',
      businessLocationId: experience?.businessLocationId || undefined,
      coordinates: experience?.coordinates || undefined,
    },
  });

  const watchedTuitionType = form.watch('tuitionType');

  const placeLabels =
    watchedTuitionType === 'private'
      ? 'Self'
      : watchedTuitionType === 'school'
      ? 'School Name'
      : watchedTuitionType === 'college'
      ? 'College Name'
      : watchedTuitionType === 'institute'
      ? 'Institute Name'
      : ('Institution Name' as const);

  useEffect(() => {
    if (watchedTuitionType === 'private') {
      form.setValue('placeName', 'Self');
      form.setValue('location', 'Self');
      form.setValue('businessLocationId', undefined);
      form.setValue('coordinates', undefined);
      setHideNameLocation(true);
    } else {
      if (!isEditing) {
        form.setValue('placeName', '');
        form.setValue('location', '');
        form.setValue('businessLocationId', undefined);
        form.setValue('coordinates', undefined);
      }
      setHideNameLocation(false);
    }
  }, [watchedTuitionType, form, isEditing, tuitionTypeMap]);

  const [businessSearchTerm, setBusinessSearchTerm] = useState('');

  const { data: businessLocationsData, isError: isBusinessLocationsError } = useUserSearchBusinessLocation(businessSearchTerm);

  const businessLocationSuggestions = useMemo(() => {
    return (businessLocationsData?.data?.businessLocations || []).map((business) => ({
      value: business._id,
      label: `${business.name}${business.location ? ` - ${business.location}` : ''}`,
    }));
  }, [businessLocationsData, isBusinessLocationsError]);

  const handleBusinessSelection = (selectedValue: string) => {
    const selectedBusiness = businessLocationsData?.data?.businessLocations?.find((business) => business._id === selectedValue);

    if (selectedBusiness) {
      form.setValue('businessLocationId', selectedBusiness._id);
      form.setValue('placeName', selectedBusiness.name);

      if (selectedBusiness.location) {
        form.setValue('location', selectedBusiness.location);
      } else {
        form.setValue('location', '');
      }

      if (selectedBusiness.coordinates) {
        form.setValue('coordinates', selectedBusiness.coordinates);
      } else {
        form.setValue('coordinates', undefined);
      }
    }
  };

  const handleParsedAddress = (parsed: IAddressOutput) => {
    if (parsed.lat && parsed.lng) {
      form.setValue('coordinates', { lat: parsed.lat, lng: parsed.lng });
    }
  };

  const onSubmit = async (values: CreateTeachingExperienceInput) => {
    try {
      if (isEditing && experience) {
        const result = await updateExperience.mutateAsync({ id: experience._id, data: values });
        if (!result.success) throw new Error(result.message || 'Operation failed');
        toast.success('Teaching experience updated successfully!');
      } else {
        const result = await createExperience.mutateAsync(values);
        if (!result.success) throw new Error(result.message || 'Operation failed');
        toast.success('Teaching experience added successfully!');
      }

      form.reset();
      onClose();
    } catch (error: any) {
      toast.error(error.message || 'Failed to save teaching experience');
      console.error('Teaching experience save error:', error);
    }
  };

  return (
    <PrimaryModalWithHeader
      isOpen={isOpen}
      onClose={onClose}
      title={`${isEditing ? 'Edit' : 'Add'} Teaching Experience`}
      subtitle={`${isEditing ? 'Update' : 'Add'} your teaching experience details`}
      icon={<Briefcase className='text-white' size={22} />}
      variant='primary'
      maxWidth='max-w-4xl'
    >
      <Form {...form} key={experience ? `edit-${experience._id}` : 'add-new'}>
        <form onSubmit={form.handleSubmit(onSubmit)} className='p-8'>
          <div className='grid grid-cols-1 md:grid-cols-3 gap-6'>
            {/* Tuition Type */}
            <PrimarySelect
              form={form}
              name='tuitionType'
              label='Tuition Type'
              options={tuitionTypeOptions}
              placeholder='Select tuition type'
              required
              variant='primary'
            />

            {/* Experience in Months */}
            <div className=''>
              <PrimaryInput
                form={form}
                name='experienceMonths'
                label='Experience (Months)'
                placeholder='Enter experience in months'
                type='number'
                required
                variant='primary'
              />
            </div>

            <div className='hidden md:block'></div>

            {/* Place Name - (Hidden for private) */}
            {!hideNameLocation && (
              <div className='md:col-span-2'>
                <PrimaryAutoCompleteInput
                  form={form}
                  name='placeName'
                  label={`${placeLabels}/Place Name`}
                  icon={<Building className='h-5 w-5 text-gray-400' />}
                  placeholder='Search for institution or enter manually'
                  options={businessLocationSuggestions}
                  required
                  variant='primary'
                  onSelect={(selectedValue) => handleBusinessSelection(selectedValue)}
                  onInputChange={(value) => setBusinessSearchTerm(value)}
                />
              </div>
            )}

            {/* Location - (Hidden for private) */}
            {!hideNameLocation && (
              <div className='md:col-span-1'>
                <PrimaryLocationInput
                  form={form}
                  name='location'
                  label='Location'
                  placeholder='Enter location'
                  required
                  setParsedAddress={handleParsedAddress}
                />
              </div>
            )}

            {/* Show info for private tuition */}
            {hideNameLocation && (
              <div className='md:col-span-3 bg-blue-50 rounded-xl p-6 shadow-sm flex items-start gap-3'>
                <div className='bg-blue-100 p-2 rounded-full'>
                  <FaInfoCircle size={20} className='text-blue-600' />
                </div>
                <div>
                  <h4 className='font-medium text-blue-700 mb-1'>Private Tuition Information</h4>
                  <p className='text-blue-600 text-sm'>
                    Place name and location are automatically set to "Self" since you provide tuition independently.
                  </p>
                </div>
              </div>
            )}
          </div>

          <div className='flex gap-4 justify-end pt-6 mt-8 border-t border-gray-100'>
            <CancelButton onClose={onClose} size='md' />
            <SubmitButton
              isSubmitting={isSubmitting}
              label={isEditing ? 'Update Experience' : 'Add Experience'}
              submittingLabel={isEditing ? 'Updating...' : 'Adding...'}
              variant='primary'
              size='md'
            />
          </div>
        </form>
      </Form>
    </PrimaryModalWithHeader>
  );
};

export default TeachingExperienceForm;
