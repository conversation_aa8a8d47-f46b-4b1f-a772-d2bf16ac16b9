'use client';

import Image from 'next/image';
import {
  Briefcase,
  Clock,
  Coins,
  Edit2,
  Eye,
  FileCog,
  Home,
  IndianRupee,
  MessageSquareText,
  PencilOff,
  ScanEye,
  School,
  ScrollText,
  Star,
  Wrench,
  X,
} from 'lucide-react';
import TutorBasicProfileForm from '@/components/dashboard/tutor-dash/forms/TutorBasicProfileForm';
import { useState } from 'react';
import AddressForm from '@/components/dashboard/tutor-dash/forms/AddressForm';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { cn } from '@/lib/utils';
import { KeyValueDisplay } from '@/components/dashboard/shared/misc';

import { Mail, MapPin, PhoneCall, ChevronDown, ArrowLeft, User, ArrowRight } from 'lucide-react';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import TutionInfoForm from '@/components/dashboard/institute-dash/forms/TuitionInfoForm';
import { AdminDashActionLinks } from '@/components/dashboard/ascrm/misc';
import { AlertDialog, AlertDialogCancel, AlertDialogContent, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { SimpleTable } from '@/components/dashboard/college-dash/misc';
import TeachingExperienceForm from '@/components/dashboard/institute-dash/forms/TeachingExperienceForm';
import WhatDoYouTeachForm from '@/components/dashboard/institute-dash/forms/WhatDoYouTeachForm';
import StudySectionTable from '@/components/dashboard/tutor-dash/misc/StudySectionTable';

const TutorDetailPage = () => {
  return (
    <div className='grid grid-cols-1 gap-4'>
      <TopHeader />
      <section className='flex flex-col md:flex-row gap-6'>
        {/* Left Column */}
        <div className='md:w-1/3 p-6 bg-white rounded-3xl relative'>
          <div className='absolute top-0 left-0 transform bg-gradient-1 uppercase tracking-wider text-white text-xs font-semibold px-5 py-1.5 rounded-tl-3xl rounded-br-3xl'>
            Active
          </div>
          <div className='flex flex-col items-center text-center'>
            <img
              src='/icons/avatar-male.webp'
              alt='Tutor'
              className='size-28 border-2 border-primaryColor-200 rounded-full object-cover ring-2 ring-offset-4 ring-primaryColor'
            />
            <h2 className='mt-4 text-xl font-semibold'>Leonard Campbell</h2>
            <p className='text-gray-500'>#4232</p>

            <div className='flex gap-2 items-center my-4 w-full justify-center'>
              <div className='bg-gray-50 text-gray-600 p-3 rounded-full'>
                <PhoneCall size={18} strokeWidth={1.5} />
              </div>
              <div className='bg-gray-50 text-gray-600 p-3 rounded-full'>
                <Mail size={18} strokeWidth={1.5} />
              </div>
              <div className='bg-gray-50 text-gray-600 p-3 rounded-full'>
                <MapPin size={18} strokeWidth={1.5} />
              </div>
            </div>

            <div className='flex items-center justify-center flex-wrap gap-4 border-b border-gray-50 pb-4 w-full'>
              <button className='btn-default-sm'>Qualified Leads (11)</button>
              <button className='btn-default__outline-sm gap-1'>
                <span>Allotment Details</span>
                <ChevronDown size={18} />
              </button>
            </div>
          </div>
          <div className='my-4 w-full'>
            <h3 className='text-gray-700 text-lg font-semibold'>Recent Review</h3>
            <div className='mt-4 bg-gray-100 p-4 rounded-lg shadow-sm'>
              <h4 className='text-gray-800 font-semibold'>Akash Kumar Shakya</h4>
              <p className='text-gray-500 text-sm'>Full Time - Yogyakarta, Indonesia</p>
            </div>
          </div>

          <div className='my-4'>
            <StatusIndicator />
          </div>
          <div className='w-full'>
            <h3 className='text-gray-700 text-lg font-semibold mb-4'>KYC and Profiles</h3>
            <div className='grid grid-cols-2 gap-4'>
              <CircularProgressBar percentage={70} label='KYC' colorClass='text-yellow-500' />
              <CircularProgressBar percentage={60} label='Verification' colorClass='text-primaryColor-500' />
              <CircularProgressBar percentage={80} label='Profile' colorClass='text-green-500' />
              <CircularProgressBar percentage={90} label='Account Status' colorClass='text-red-500' />
            </div>
          </div>
        </div>

        {/* Right Column */}
        <div className='md:w-2/3'>
          <Tabs defaultValue='Profiles' className='w-full'>
            <TabsList className='bg-white mb-4 w-full justify-start gap-4 h-12 rounded pl-4'>
              <TabsTrigger
                className='border-b-2 px-0 py-1 border-b-transparent data-[state=active]:border-primaryColor rounded-none !shadow-none'
                value='Profiles'
              >
                Profiles
              </TabsTrigger>

              <TabsTrigger
                className='border-b-2 px-0 py-1 border-b-transparent data-[state=active]:border-primaryColor rounded-none !shadow-none'
                value='WhatDoesHeTeach'
              >
                What Does He Teach?
              </TabsTrigger>

              <TabsTrigger
                className='border-b-2 px-0 py-1 border-b-transparent data-[state=active]:border-primaryColor rounded-none !shadow-none'
                value='Wallet'
              >
                Wallet
              </TabsTrigger>
              <TabsTrigger
                className='border-b-2 px-0 py-1 border-b-transparent data-[state=active]:border-primaryColor rounded-none !shadow-none'
                value='Services'
              >
                Services
              </TabsTrigger>
              <TabsTrigger
                className='border-b-2 px-0 py-1 border-b-transparent data-[state=active]:border-primaryColor rounded-none !shadow-none'
                value='History'
              >
                History
              </TabsTrigger>
            </TabsList>

            <div className='bg-white rounded-3xl p-6 pt-2'>
              {/* Profiles Tab Content */}
              <TabsContent value='Profiles'>
                <Tabs defaultValue='BasicProfile'>
                  <TabsList>
                    <TabsTrigger value='BasicProfile'>Basic Profile</TabsTrigger>
                    <TabsTrigger value='TuitionProfile'>Tuition Profile</TabsTrigger>
                    <TabsTrigger value='KYC'>KYC</TabsTrigger>
                    <TabsTrigger value='Qualification'>Qualification</TabsTrigger>
                    <TabsTrigger value='JobProfile'>Job Profile</TabsTrigger>
                  </TabsList>
                  <TabsContent value='BasicProfile'>
                    <PersonalTab />
                  </TabsContent>
                  <TabsContent value='TuitionProfile'>
                    <TuitionProfile />
                  </TabsContent>
                  <TabsContent value='KYC'>
                    <TutorKycPage />
                  </TabsContent>
                  <TabsContent value='Qualification'>
                    <EducationCategoryTabs />
                  </TabsContent>
                  <TabsContent value='JobProfile'>Job Profile Details</TabsContent>
                </Tabs>
              </TabsContent>

              {/* Wallet Tab Content */}
              <TabsContent value='Wallet'>
                <Tabs defaultValue='Promotion'>
                  <TabsList>
                    <TabsTrigger value='Promotion'>Promotion</TabsTrigger>
                    <TabsTrigger value='Commission'>Commission</TabsTrigger>
                    <TabsTrigger value='Membership'>Membership</TabsTrigger>
                  </TabsList>
                  <TabsContent value='Promotion'>
                    <Wallet />
                  </TabsContent>
                  <TabsContent value='Commission'>Commission Details</TabsContent>
                  <TabsContent value='Membership'>Membership Details</TabsContent>
                </Tabs>
              </TabsContent>

              {/* What Does He Teach Tab Content */}
              <TabsContent value='WhatDoesHeTeach'>
                <Tabs defaultValue='School' className='w-full'>
                  <TabsList>
                    <TabsTrigger value='School'>School </TabsTrigger>
                    <TabsTrigger value='College'>College </TabsTrigger>
                    <TabsTrigger value='Language'>Language </TabsTrigger>
                    <TabsTrigger value='Hobby'>Hobby </TabsTrigger>
                    <TabsTrigger value='ITCourse'>IT Course </TabsTrigger>
                    <TabsTrigger value='CompetitiveExam'>Competitive Exam </TabsTrigger>
                    <TabsTrigger value='EntranceExam'>Entrance Exam </TabsTrigger>
                  </TabsList>

                  <TabsContent value='School'>
                    <div>
                      <div className='flex justify-end items-start'>
                        <AlertDialog>
                          <div className='flex justify-between items-center w-full my-2'>
                            <h2 className='text-base font-semibold'>School Section Information</h2>
                            <AlertDialogTrigger className='btn-default-sm'>Add Record</AlertDialogTrigger>
                          </div>
                          <AlertDialogContent className='lg:w-[700px] lg:max-w-none'>
                            <WhatDoYouTeachForm />
                            <AlertDialogCancel className='border-0 absolute rounded-none rounded-tr-md right-0 top-0 bg-primaryColor-50 hover:bg-primaryColor-100 flex-shrink-0 p-2 tracking-wider cursor-pointer group overflow-hidden'>
                              <p className='text-primaryColor'>
                                <X />
                              </p>
                            </AlertDialogCancel>
                          </AlertDialogContent>
                        </AlertDialog>
                      </div>
                      <StudySectionTable
                        headers={['SN', 'Board', 'Class', 'Subjects', 'Budget']}
                        rows={[
                          [1, 'CBSE', '10th', 'All Subjects', '5000/hourly'],
                          [2, 'ICSE', '11th', 'Mathematics, Science, English', '7000/hourly'],
                          [3, 'State Board', '12th', 'Mathematics, Science, English', '9000/hourly'],
                          [4, 'International Board', '12th', 'Mathematics, Science, English', '11000/hourly'],
                          [5, 'ICSE', '11th', 'Mathematics, Science, English', '7000/hourly'],
                        ]}
                      />
                    </div>
                  </TabsContent>
                  <TabsContent value='College'>College Details</TabsContent>
                  <TabsContent value='Language'>Language Details</TabsContent>
                  <TabsContent value='Hobby'>Hobby Details</TabsContent>
                  <TabsContent value='ITCourse'>IT Course Details</TabsContent>
                  <TabsContent value='CompetitiveExam'>Competitive Exam Details</TabsContent>
                  <TabsContent value='EntranceExam'>Entrance Exam Details</TabsContent>
                </Tabs>
              </TabsContent>

              {/* Services Tab Content */}
              <TabsContent value='Services'>
                <Tabs defaultValue='Batches'>
                  <TabsList>
                    <TabsTrigger value='Batches'>Batches</TabsTrigger>
                    <TabsTrigger value='Jobs'>Jobs</TabsTrigger>
                    <TabsTrigger value='TuitionLeads'>Tuition Leads</TabsTrigger>
                    <TabsTrigger value='Complaints'>Complaints</TabsTrigger>
                  </TabsList>
                  <TabsContent value='Batches'>
                    <Batches />
                  </TabsContent>
                  <TabsContent value='Jobs'>
                    <Jobs />
                  </TabsContent>
                  <TabsContent value='TuitionLeads'>
                    <TuitionLeads />
                  </TabsContent>
                  <TabsContent value='Complaints'>
                    <TutorComplaintsPage />
                  </TabsContent>
                </Tabs>
              </TabsContent>

              {/* History Tab Content */}
              <TabsContent value='History'>
                <Tabs defaultValue='Account'>
                  <TabsList>
                    <TabsTrigger value='Account'>Account</TabsTrigger>
                    <TabsTrigger value='Plan'>Plan</TabsTrigger>
                    <TabsTrigger value='Rating'>Rating</TabsTrigger>
                    <TabsTrigger value='HistoryNotes'>History Notes</TabsTrigger>
                    <TabsTrigger value='LeadStatus'>Lead Status</TabsTrigger>
                  </TabsList>
                  <TabsContent value='Account'>
                    <div>
                      <h2 className='text-base font-semibold'>Account Status History</h2>
                      <SimpleTable
                        headers={['Agent', 'Status From - To', 'DateTime', 'Action']}
                        rows={[
                          ['CBSE', '10th', 'All Subjects', '5000/hourly'],
                          ['ICSE', '11th', 'Mathematics, Science, English', '7000/hourly'],
                          ['State Board', '12th', 'Mathematics, Science, English', '9000/hourly'],
                          ['International Board', '12th', 'Mathematics, Science, English', '11000/hourly'],
                          ['ICSE', '11th', 'Mathematics, Science, English', '7000/hourly'],
                        ]}
                      />
                    </div>
                  </TabsContent>
                  <TabsContent value='Plan'>
                    <div>
                      <h2 className='text-base font-semibold'>Plan History</h2>
                      <SimpleTable
                        headers={['Agent', 'Status From - To', 'DateTime', 'Action']}
                        rows={[
                          ['CBSE', '10th', 'All Subjects', '5000/hourly'],
                          ['ICSE', '11th', 'Mathematics, Science, English', '7000/hourly'],
                          ['State Board', '12th', 'Mathematics, Science, English', '9000/hourly'],
                          ['International Board', '12th', 'Mathematics, Science, English', '11000/hourly'],
                          ['ICSE', '11th', 'Mathematics, Science, English', '7000/hourly'],
                        ]}
                      />
                    </div>
                  </TabsContent>
                  <TabsContent value='Rating'>
                    <div>
                      <h2 className='text-base font-semibold'>Rating History</h2>
                      <SimpleTable
                        headers={['Agent', 'Rating', 'Title', 'DateTime', 'Action']}
                        rows={[
                          ['CBSE', '10th', 'All Subjects', '5000/hourly'],
                          ['ICSE', '11th', 'Mathematics, Science, English', '7000/hourly'],
                          ['State Board', '12th', 'Mathematics, Science, English', '9000/hourly'],
                          ['International Board', '12th', 'Mathematics, Science, English', '11000/hourly'],
                          ['ICSE', '11th', 'Mathematics, Science, English', '7000/hourly'],
                        ]}
                      />
                    </div>
                  </TabsContent>
                  <TabsContent value='HistoryNotes'>History Notes Details</TabsContent>
                  <TabsContent value='LeadStatus'>Lead Status Details</TabsContent>
                </Tabs>
              </TabsContent>
            </div>
          </Tabs>
        </div>
      </section>
    </div>
  );
};

const TopHeader = () => (
  <div className='flex justify-between items-center p-6 bg-white rounded-3xl'>
    <button className='flex items-center gap-2'>
      <div className='bg-primaryColor-100 text-primaryColor-700 hover:bg-primaryColor-200 rounded size-7 flex items-center justify-center'>
        <ArrowLeft size={20} />
      </div>
      <p className='text-base font-medium'>Back to Tutors List</p>
    </button>
    <div className='flex items-center gap-2'>
      <p className='text-base font-medium'>
        Next, <span className='text-primaryColor'>Jose Multan</span>
      </p>
      <div className='bg-primaryColor-100 text-primaryColor-700 hover:bg-primaryColor-200 rounded size-7 flex items-center justify-center'>
        <ArrowRight size={20} />
      </div>
    </div>
  </div>
);

const toggleEditItem = (setItem: React.Dispatch<React.SetStateAction<boolean>>) => {
  setItem((prevValue) => !prevValue);
};

const PersonalTab = () => {
  const [editGeneralInfo, setEditGeneralInfo] = useState(false);
  const [editCurrentAddress, setEditCurrentAddress] = useState(false);
  const [editPermanentAddress, setEditPermanentAddress] = useState(false);

  const anyEditInfoActive = editGeneralInfo || editCurrentAddress || editPermanentAddress;

  return (
    <div>
      {/* General Information */}
      <div className={cn('bg-white rounded-3xl p-6 w-full relative', anyEditInfoActive && !editGeneralInfo ? 'opacity-50' : '')}>
        <div className='flex items-start justify-between'>
          <h2 className='text-xl font-semibold mb-4'>General Information</h2>
          <button
            onClick={() => toggleEditItem(setEditGeneralInfo)}
            className='py-2 px-3 bg-primaryColor-50 text-primaryColor rounded transition-all duration-300'
          >
            {editGeneralInfo ? <PencilOff size={16} /> : <Edit2 size={16} />}
          </button>
        </div>
        {editGeneralInfo ? (
          <TutorBasicProfileForm />
        ) : (
          <div className='grid grid-cols-2 gap-4'>
            <KeyValueDisplay label='Referral Code' value='ABC1001' />
            <KeyValueDisplay label='Full Name' value='John Doe' />
            <KeyValueDisplay label='Email' value='<EMAIL>' />
            <KeyValueDisplay label='Primary Mobile' value='************' />
            <KeyValueDisplay label='Alternative Mobile' value='************' />
            <KeyValueDisplay label='Primary WhatsApp' value='************' />
            <KeyValueDisplay label='Alternative WhatsApp' value='************' />
            <KeyValueDisplay label='Gender' value='Male' />
            <KeyValueDisplay label='Date of Birth' value='01/01/1990' />
            <KeyValueDisplay label='Location' value='Sector 18, Noida, 201301' />
            <KeyValueDisplay label='How do you came to know about us?' value='Google' />
          </div>
        )}
      </div>
      {/* Current Address */}
      <div className={cn('bg-white rounded-3xl p-6 w-full relative', anyEditInfoActive && !editCurrentAddress ? 'opacity-50' : '')}>
        <div className='flex items-start justify-between'>
          <h2 className='text-xl font-semibold mb-4'>Current Address</h2>
          <button
            onClick={() => toggleEditItem(setEditCurrentAddress)}
            className='py-2 px-3 bg-primaryColor-50 text-primaryColor rounded transition-all duration-300'
          >
            {editCurrentAddress ? <PencilOff size={16} /> : <Edit2 size={16} />}
          </button>
        </div>
        {editCurrentAddress ? (
          <AddressForm />
        ) : (
          <div className='grid grid-cols-2 gap-4'>
            <KeyValueDisplay label='House No' value='123' />
            <KeyValueDisplay label='Locality' value='XYZ Street' />
            <KeyValueDisplay label='Landmark' value='Near ABC Park' />
            <KeyValueDisplay label='Area PIN code' value='123456' />
            <KeyValueDisplay label='City' value='Some City' />
            <KeyValueDisplay label='District' value='Some District' />
            <KeyValueDisplay label='State' value='Some State' />
          </div>
        )}
      </div>
      {/* Permanent Address */}
      <div className={cn('bg-white rounded-3xl p-6 w-full relative', anyEditInfoActive && !editPermanentAddress ? 'opacity-50' : '')}>
        <div className='flex items-start justify-between'>
          <h2 className='text-xl font-semibold mb-4'>Permanent Address</h2>
          <button
            onClick={() => toggleEditItem(setEditPermanentAddress)}
            className='py-2 px-3 bg-primaryColor-50 text-primaryColor rounded transition-all duration-300'
          >
            {editPermanentAddress ? <PencilOff size={16} /> : <Edit2 size={16} />}
          </button>
        </div>
        {editPermanentAddress ? (
          <div>
            <div className='flex items-center gap-2 col-span-2 mb-4'>
              <Switch id='sameCurrentAddress' />
              <Label htmlFor='sameCurrentAddress'>Same as Current Address</Label>
            </div>
            <AddressForm />
          </div>
        ) : (
          <div className='grid grid-cols-2 gap-4'>
            <KeyValueDisplay label='House No' value='456' />
            <KeyValueDisplay label='Locality' value='ABC Street' />
            <KeyValueDisplay label='Landmark' value='Near XYZ Park' />
            <KeyValueDisplay label='Area PIN code' value='654321' />
            <KeyValueDisplay label='City' value='Another City' />
            <KeyValueDisplay label='District' value='Another District' />
            <KeyValueDisplay label='State' value='Another State' />
          </div>
        )}
      </div>
    </div>
  );
};

const StatusIndicator = () => {
  return (
    <div className='flex flex-col p-4 rounded-lg'>
      <div className='flex items-center justify-between mb-2'>
        <span className='text-gray-700 font-semibold'>1 out of 5 Stars</span>
      </div>
      <div className='flex items-center space-x-1'>
        {Array.from({ length: 5 }, (_, i) => (
          <div key={i} className={`flex-1 py-2 text-center rounded-md ${i === 0 ? 'bg-orange-500 text-white' : 'bg-gray-100 text-gray-700'}`}>
            {i + 1}
          </div>
        ))}
      </div>
    </div>
  );
};

const TuitionProfile = () => {
  const [editTuitionInfo, setEditTuitionInfo] = useState(false);

  return (
    <>
      <div className='flex flex-col gap-4 w-full'>
        <div className='flex justify-between items-center mt-2'>
          <h2 className='text-base font-semibold'>Tuition Information</h2>
          <button
            onClick={() => toggleEditItem(setEditTuitionInfo)}
            className='py-2 px-3 bg-primaryColor-50 text-primaryColor rounded transition-all duration-300'
          >
            {editTuitionInfo ? <PencilOff size={16} /> : <Edit2 size={16} />}
          </button>
        </div>
        {editTuitionInfo ? (
          <TutionInfoForm />
        ) : (
          <div className='grid grid-cols-2 gap-4'>
            <KeyValueDisplay label='Mode' value='Online' />
            <KeyValueDisplay label='Location' value='Sherpur, Bangladesh' />
            <KeyValueDisplay label='Distance' value='10 km' />
            <KeyValueDisplay label='Languages' value='English, Bangla' />
            <KeyValueDisplay label='Description' value='Lorem ipsum dolor sit amet, consectetur adipiscing elit.' />
          </div>
        )}
      </div>

      <div className='mt-4 border-t pt-4'>
        <div className='flex justify-end items-start mb-4'>
          <AlertDialog>
            <div className='flex justify-between items-center w-full flex-wrap gap-2'>
              <h2 className='text-base font-semibold'>Teaching Experience Info</h2>
              <AlertDialogTrigger className='btn-default-sm'>Add Experience</AlertDialogTrigger>
            </div>

            <AlertDialogContent className='lg:w-[700px] lg:max-w-none'>
              <TeachingExperienceForm />
              <AlertDialogCancel className='border-0 absolute rounded-none rounded-tr-md right-0 top-0 bg-primaryColor-50 hover:bg-primaryColor-100 flex-shrink-0 p-2 tracking-wider cursor-pointer group overflow-hidden'>
                <p className='text-primaryColor'>
                  <X />
                </p>
              </AlertDialogCancel>
            </AlertDialogContent>
          </AlertDialog>
        </div>

        <div className='max-md:hidden'>
          <SimpleTable
            headers={['Tuition Type', 'Exp. (M)', 'Place Name', 'Location']}
            rows={[
              ['Private Tuition', 16, 'Self', 'Self', <AdminDashActionLinks basePath='/' id='' itemName='a' edit delete />],
              [
                'Institute Tuition',
                24,
                'Sherpur Public School',
                'Sherpur, Bangladesh',
                <AdminDashActionLinks itemName='a' basePath='/' id='' edit delete />,
              ],
              [
                'College Tuition',
                12,
                'Mujibur Rahman Public School',
                'Mujibur Rahman, Bangladesh',
                <AdminDashActionLinks itemName='4' basePath='/' id='' edit delete />,
              ],
              [
                'School Tuition',
                6,
                'Online Tutoring Platform',
                'Online Tutoring Platform, Bangladesh',
                <AdminDashActionLinks itemName='4' basePath='/' id='' edit delete />,
              ],
            ]}
          />
        </div>

        <div className='md:hidden'>
          {[
            ['Private Tuition', 16, 'Self', 'Self'],
            ['Institute Tuition', 24, 'Sherpur Public School', 'Sherpur, Bangladesh'],
            ['College Tuition', 12, 'Mujibur Rahman Public School', 'Mujibur Rahman, Bangladesh'],
            ['School Tuition', 6, 'Online Tutoring Platform', 'Online Tutoring Platform, Bangladesh'],
          ].map((row, rowIndex) => (
            <div key={rowIndex} className='border rounded-lg p-4 mb-4 text-sm'>
              {row.map((cell, cellIndex) => (
                <div key={cellIndex} className='mb-2 flex items-center gap-2 flex-wrap'>
                  {cellIndex === 0 && <Home strokeWidth={1.5} className='text-primaryColor' />}
                  {cellIndex === 1 && <Clock strokeWidth={1.5} className='text-primaryColor' />}
                  {cellIndex === 2 && <MapPin strokeWidth={1.5} className='text-primaryColor' />}
                  {cellIndex === 3 && <School strokeWidth={1.5} className='text-primaryColor' />}

                  <span className='font-medium'>{['Tuition Type', 'Exp. (M)', 'Place Name', 'Location'][cellIndex]}:</span>
                  <span className='text-gray-600'>{cell}</span>
                </div>
              ))}
              <div className='flex justify-end'>
                <AdminDashActionLinks itemName='jj' edit delete basePath='/' id='' />
              </div>
            </div>
          ))}
        </div>
      </div>
    </>
  );
};

import { BadgeCheck, Check, Ellipsis, Plus } from 'lucide-react';
import Link from 'next/link';
import TutorQualificationForm from '@/components/dashboard/tutor-dash/forms/TutorQualificationForm';
import TutorDashActionLinks from '@/components/dashboard/tutor-dash/misc/TutorDashActionLinks';
import { TutorDashLink } from '@/components/dashboard/tutor-dash/misc';
import { format, formatDistanceToNow } from 'date-fns';
import { leads } from '@/constants';
import { CircularProgressBar } from '@/components/dashboard/shared/misc';

const TutorKycPage = () => {
  return (
    <div className='bg-white p-6 rounded-3xl relative'>
      <div>
        <h2 className='text-base font-semibold'>Manual KYC (Doc Upload)</h2>
        <p className='text-sm font-medium my-2'>Upload your documents manually for verification.</p>
      </div>
      <div className='py-4 mt-4'>
        <DocumentUploadPage />
      </div>
    </div>
  );
};

const DocumentUploadPage = () => {
  return (
    <div className='flex flex-col items-start justify-start'>
      <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
        <div className='flex flex-col lg:flex-row gap-4 items-center'>
          <DocumentCard
            placeholder='/icons/aadhar-placeholder.webp'
            title='Aadhar Card'
            description='Government-issued ID card with front and back photos required.'
            status='approved'
            href='/tutor-dash/profiles/kyc/manual/aadhar'
          />
          <Plus size={40} strokeWidth={1.75} />
        </div>
        <div className='flex flex-col lg:flex-row gap-4 items-center'>
          <DocumentCard
            placeholder='/icons/pan-placeholder.webp'
            title='PAN Card'
            description='Issued by Income Tax Department, useful for tax-related documentation.'
            status='pending'
            href='/tutor-dash/profiles/kyc/manual/pan'
          />
          <p className='font-medium text-lg'>OR</p>
        </div>
        <div className='flex flex-col lg:flex-row gap-4 items-center'>
          <DocumentCard
            href='/tutor-dash/profiles/kyc/manual/other'
            title='Other Document'
            description='Any other official document such as Driving License or Voter ID.'
            status='rejected'
          />
          {/* TODO: can remove this */}
          <BadgeCheck size={40} className='text-primaryColor opacity-0 hidden md:block' />
        </div>
      </div>
    </div>
  );
};

interface IDocumentCard {
  title: string;
  description: string;
  status: 'approved' | 'pending' | 'rejected';
  placeholder?: string;
  href: string;
}

const DocumentCard: React.FC<IDocumentCard> = ({ title, description, status, placeholder, href }) => {
  return (
    <div className='border-2 border-dashed border-primaryColor rounded-lg p-4 flex flex-col items-center relative'>
      {status === 'approved' && (
        <div className='bg-primaryColor flex gap-2 justify-start items-center text-white py-1 pl-3 pr-1.5 text-sm rounded-3xl absolute top-4 right-4'>
          <span>Approved</span>
          <div className='bg-white text-primaryColor rounded-full p-0.5'>
            <Check size={14} />
          </div>
        </div>
      )}

      {status === 'pending' && (
        <div className='bg-yellow-500 flex gap-2 justify-start items-center text-white py-1 pl-3 pr-1.5 text-sm rounded-3xl absolute top-4 right-4'>
          <span>Pending</span>
          <div className='bg-white text-yellow-500 rounded-full p-0.5'>
            <Ellipsis size={14} />
          </div>
        </div>
      )}

      {status === 'rejected' && (
        <div className='bg-red-500 flex gap-2 justify-start items-center text-white py-1 pl-3 pr-1.5 text-sm rounded-3xl absolute top-4 right-4'>
          <span>Rejected</span>
          <div className='bg-white text-red-500 rounded-full p-0.5'>
            <X size={14} />
          </div>
        </div>
      )}

      <div className='bg-primaryColor-50 rounded p-2 my-2 mt-10 h-40 w-full'>
        {placeholder && <img src={placeholder} alt={title} className='object-contain w-full h-full' />}
      </div>
      <p className='text-lg font-medium text-gray-800'>{title}</p>
      <p className='text-sm text-gray-500 text-center'>{description}</p>
      <Link href={href} className='btn-default-sm w-full mt-2'>
        Upload {title}
      </Link>
    </div>
  );
};

const EducationCategoryTabs = () => {
  return (
    <Tabs defaultValue='Undergraduate' className='w-full'>
      <TabsList>
        <TabsTrigger value='Undergraduate'>Undergraduate</TabsTrigger>
        <TabsTrigger value='Graduate'>Graduate</TabsTrigger>
        <TabsTrigger value='AnyOther'>Any Other</TabsTrigger>
      </TabsList>

      <TabsContent value='Undergraduate'>
        <div>
          {/* Record Modal */}
          <div className='flex justify-end items-start mb-4'>
            <AlertDialog>
              <div className='flex justify-between items-center w-full'>
                <h2 className='text-base font-semibold'>Undergraduate Qualification Information</h2>
                <AlertDialogTrigger className='btn-default-sm'>Add Record</AlertDialogTrigger>
              </div>
              <AlertDialogContent className='lg:w-[700px] lg:max-w-none'>
                <TutorQualificationForm isUndergraduate />
                <AlertDialogCancel className='border-0 absolute rounded-none rounded-tr-md right-0 top-0 bg-primaryColor-50 hover:bg-primaryColor-100 flex-shrink-0 p-2 tracking-wider cursor-pointer group overflow-hidden'>
                  <p className='text-primaryColor'>
                    <X />
                  </p>
                </AlertDialogCancel>
              </AlertDialogContent>
            </AlertDialog>
          </div>
          {/* Record Table */}
          <SimpleTable
            headers={['Class', 'Branch', 'Start Year', 'End Year/Expected Year', 'Percentage', 'Action']}
            rows={[
              ['10th', 'Science', 2015, 2018, '85%', <TutorDashActionLinks basePath='' id='' edit delete />],
              ['11th', 'Commerce', 2016, 2019, '88%', <TutorDashActionLinks basePath='' id='' edit delete />],
              ['12th', 'Engineering', 2014, 2018, '90%', <TutorDashActionLinks basePath='' id='' edit delete />],
              ['12th', 'Arts', 2013, 2017, '82%', <TutorDashActionLinks basePath='' id='' edit delete />],
              ['11th', 'Education', 2012, 2016, '87%', <TutorDashActionLinks basePath='' id='' edit delete />],
              ['10th', 'Science', 2015, 2018, '-', <TutorDashActionLinks basePath='' id='' edit delete />],
            ]}
          />
        </div>
      </TabsContent>
      <TabsContent value='Graduate'>
        <p>Details and options for Graduate programs.</p>
      </TabsContent>
      <TabsContent value='AnyOther'>
        <p>Details and options for other education categories.</p>
      </TabsContent>
    </Tabs>
  );
};

const Wallet = () => {
  const transactions = [
    { id: 1, type: 'credit', title: 'Plan Purchased - Bronze', coins: '700', date: '20 August 2019', status: 'Completed' },
    { id: 2, type: 'debit', title: 'Coins Deducted - Enquiry ID 1432', coins: '50', date: '25 September 2021', status: 'Pending' },
    { id: 3, type: 'credit', title: 'Coin Reimbursed - Enquiry ID 1432', coins: '50', date: '26 September 2021', status: 'Completed' },
    { id: 4, type: 'debit', title: 'Coins Deducted - Enquiry ID 1532', coins: '70', date: '26 October 2021', status: 'Failed' },
  ];

  const TransactionBox = ({ transaction }: { transaction: any }) => (
    <div className='rounded-xl border p-3 flex flex-col justify-start items-start md:flex-row md:justify-between md:items-center'>
      <div className='flex gap-4 items-start'>
        <div className='bg-primaryColor-50 text-primaryColor size-12 flex items-center justify-center rounded'>
          <Coins size={25} />
        </div>
        <div className='flex flex-col'>
          <h3 className='text-base font-semibold'>{transaction.title}</h3>
          <p className='text-gray-400 text-[15px]'>{transaction.date}</p>
        </div>
      </div>
      <Link href='/tutor-dash/support/transactions/1' className='flex max-md:justify-between max-md:w-full items-center gap-12 mt-4 lg:mt-0'>
        {transaction.type === 'credit' ? (
          <p className='text-sm font-semibold text-primaryColor'>+{transaction.coins} Coins</p>
        ) : (
          <p className='text-sm font-semibold text-red-500'>-{transaction.coins} Coins</p>
        )}
        <button className='text-primaryColor'>
          <ScanEye />
        </button>
      </Link>
    </div>
  );

  const Status = () => (
    <div>
      <h2 className='text-base font-semibold my-4'>Wallet Summary</h2>
      <div className='grid grid-cols-2 gap-4'>
        <KeyValueDisplay label='Plan' value='Bronze' />
        <KeyValueDisplay label='Coins Utilized' value='70' />
        <KeyValueDisplay label='Coins Left' value='630' />
        <KeyValueDisplay label='Plan Purchased on' value='20 Dec 2021' />
        <KeyValueDisplay label='Plan Expiry' value='26 Dec 2021' />
      </div>
    </div>
  );

  return (
    <div className='grid grid-cols-1 gap-4 w-full'>
      <Status />
      {transactions.map((transaction) => (
        <TransactionBox key={transaction.id} transaction={transaction} />
      ))}
    </div>
  );
};

const Batches = () => (
  <section>
    <h2 className='text-base font-semibold my-4'>List of Batches</h2>
    <SimpleTable
      headers={['Batch ID', 'Batch Slot Date', 'Parent/Student Name', 'Location', 'Mode', 'Status', 'Action']}
      rows={[
        [
          'B001',
          '2024-07-22',
          'John Doe',
          'New York, USA',
          'Online',
          'Active',
          <Link
            href='/tutor-dash/batches/B001'
            className='bg-primaryColor-100 text-primaryColor-700 hover:bg-primaryColor-200 rounded size-7 flex items-center justify-center cursor-pointer'
          >
            <Eye size={16} strokeWidth={1.5} />
          </Link>,
        ],
        [
          'B002',
          '2024-07-23',
          'Jane Smith',
          'London, UK',
          'Offline',
          'Completed',
          <Link
            href='/tutor-dash/batches/B001'
            className='bg-primaryColor-100 text-primaryColor-700 hover:bg-primaryColor-200 rounded size-7 flex items-center justify-center cursor-pointer'
          >
            <Eye size={16} strokeWidth={1.5} />
          </Link>,
        ],
        [
          'B003',
          '2024-07-24',
          'Mike Johnson',
          'Sydney, Australia',
          'Online',
          'Pending',
          <Link
            href='/tutor-dash/batches/B001'
            className='bg-primaryColor-100 text-primaryColor-700 hover:bg-primaryColor-200 rounded size-7 flex items-center justify-center cursor-pointer'
          >
            <Eye size={16} strokeWidth={1.5} />
          </Link>,
        ],
        [
          'B004',
          '2024-07-25',
          'Emily Davis',
          'Toronto, Canada',
          'Offline',
          'Active',
          <Link
            href='/tutor-dash/batches/B001'
            className='bg-primaryColor-100 text-primaryColor-700 hover:bg-primaryColor-200 rounded size-7 flex items-center justify-center cursor-pointer'
          >
            <Eye size={16} strokeWidth={1.5} />
          </Link>,
        ],
      ]}
    />
  </section>
);

const Jobs = () => {
  const JobCard: React.FC<{ job: any }> = ({ job }) => (
    <div className='border p-4 rounded-xl grid grid-cols-1 md:grid-cols-5 gap-2'>
      <div className='md:col-span-4'>
        <div className='flex justify-between'>
          <h3 className='text-lg font-semibold'>{job.title}</h3>
          <img src={job.logo} alt={`${job.company} logo`} className='size-12 md:hidden' />
        </div>
        <div className='flex items-center text-gray-600 font-medium text-sm mb-3'>
          <span>{job.company}</span>
          <span className='mx-2'>|</span>
          <span className='flex items-start md:items-center gap-2'>
            <Star fill='orange' stroke='none' size={16} />
            {job.rating} ({job.reviews} Reviews)
          </span>
        </div>
        <div className='flex gap-2 items-center text-sm flex-wrap'>
          <p className='flex gap-1 items-start md:items-center'>
            <Briefcase size={16} className='shrink-0' /> <span>{job.experience}</span>
          </p>
          <span className='text-gray-400'>|</span>
          <p className='flex gap-1 items-start md:items-center'>
            <IndianRupee size={16} className='shrink-0' /> <span>{job.salary}</span>
          </p>
          <span className='text-gray-400'>|</span>
          <p className='flex gap-1 items-start md:items-center'>
            <MapPin size={16} className='shrink-0' /> <span>{job.location}</span>
          </p>
        </div>
        <div className='text-gray-500 my-2'>
          <p className='flex gap-2 items-start md:items-center'>
            <ScrollText size={18} className='shrink-0' />
            <span className='line-clamp-2 md:line-clamp-1'>{job.description}</span>
          </p>
        </div>
        <div className='flex flex-wrap gap-2 mb-4'>
          {job.tags.map((tag: any, index: number) => (
            <span key={index} className='bg-gray-100 text-gray-700 px-4 py-1 rounded capitalize'>
              {tag}
            </span>
          ))}
        </div>
        <p className='font-medium text-xs hidden md:block'>{formatDistanceToNow(new Date(job.date))} ago</p>
      </div>
      <div className='flex justify-between md:flex-col md:justify-center items-center md:col-span-1 gap-4 flex-wrap'>
        <p className='font-medium text-xs md:hidden'>{formatDistanceToNow(new Date(job.date))} ago</p>
        <img src={job.logo} alt={`${job.company} logo`} className='size-12 hidden md:block' />
        <TutorDashLink href='/jobs/1' className='btn-default__outline-sm gap-2 mt-auto flex items-center shrink-0'>
          <ScanEye size={18} className='shrink-0' />
          <span className='shrink-0'>View Detail</span>
        </TutorDashLink>
      </div>
    </div>
  );

  const jobs = [
    {
      id: 1,
      title: 'Data Scientist',
      company: 'Swiggy',
      rating: 3.8,
      reviews: 3574,
      experience: '0-3 Yrs',
      salary: 'Not disclosed',
      location: 'Remote',
      description: 'Qualifications. Bachelors or Masters degree in a quantitative field with 0-2 years of experience.',
      tags: ['Natural Language Processing', 'Machine Learning', 'Deep Learning', 'Neural Networks'],
      date: new Date('2023-07-23T12:00:00Z'),
      logo: '/temp/swiggy-logo.gif',
    },
    {
      id: 2,
      title: 'Full Stack Developer',
      company: 'IBM',
      rating: 4.1,
      reviews: 20105,
      experience: '3-7 Yrs',
      salary: 'Not disclosed',
      location: 'Bengaluru',
      description:
        'Required Technical and Professional Expertise. 3+ years of experience in continuous integration, software development, ci/cd, testing methodologies, and zos.',
      tags: ['continuous integration', 'software development', 'ci/cd', 'testing methodologies', 'zos'],
      date: new Date('2022-07-23T09:00:00Z'),
      logo: '/temp/ibm-logo.gif',
    },
    {
      id: 3,
      title: 'Backend Developer',
      company: 'Google',
      rating: 4.5,
      reviews: 50102,
      experience: '2-5 Yrs',
      salary: 'Not disclosed',
      location: 'Bengaluru',
      description: 'Experience with backend systems and APIs. Proficiency in Java and Python is a must.',
      tags: ['Java', 'Python', 'APIs', 'backend'],
      date: new Date('2023-07-22T10:00:00Z'),
      logo: '/temp/google.png',
    },
    {
      id: 4,
      title: 'Machine Learning Engineer',
      company: 'Amazon',
      rating: 4.2,
      reviews: 30505,
      experience: '1-3 Yrs',
      salary: 'Not disclosed',
      location: 'Hyderabad',
      description: 'Qualifications. Bachelors or Masters degree in a quantitative field with 1-3 years of experience in ML.',
      tags: ['Machine Learning', 'Deep Learning', 'Python', 'AWS'],
      date: new Date('2024-07-21T15:00:00Z'),
      logo: '/temp/amazon.png',
    },
  ];

  return (
    <div className='space-y-4'>
      <h2 className='text-base font-semibold mt-4'>Matched Jobs</h2>
      {jobs.map((job) => (
        <JobCard key={job.id} job={job} />
      ))}
    </div>
  );
};

const TuitionLeads = () => {
  const headers = ['Name', 'Enquiry For', 'Location', 'Distance', 'Action'];

  const rows = leads.map((lead, index) => [
    <div>
      <p>{lead.name}</p>
      <p>{lead.id}000</p>
    </div>,
    <div>
      <p>{lead.enquiryFor}</p>
      <p>{['B.Sc', 'M.Sc'].includes(lead.enquiryFor) ? 'Computer' : 'CBSE'}</p>
    </div>,
    lead.address,
    lead.distance,
    <TutorDashActionLinks basePath='leads' id={lead.id.toString()} view />,
  ]);
  return (
    <div>
      <Tabs defaultValue='FilteredLeads' className='w-full'>
        <TabsList>
          <TabsTrigger value='FilteredLeads'>Filtered Leads</TabsTrigger>
          <TabsTrigger value='ApproachedLeads'>Approached Leads</TabsTrigger>
          <TabsTrigger value='ContactedLeads'>Contacted Leads</TabsTrigger>
        </TabsList>

        <TabsContent value='FilteredLeads'>
          <SimpleTable headers={headers} rows={rows} />
        </TabsContent>
        <TabsContent value='ApproachedLeads'>
          <p>Details about the leads that have been approached.</p>
        </TabsContent>
        <TabsContent value='ContactedLeads'>
          <p>Details about the leads that have been contacted.</p>
        </TabsContent>
      </Tabs>
    </div>
  );
};

const TutorComplaintsPage = () => {
  const complaints = [
    { id: 1, title: 'Unable to Sign Up', status: 'Active' },
    { id: 2, title: 'Payment Failed', status: 'In Progress' },
    { id: 3, title: 'Account Locked', status: 'Closed' },
    { id: 4, title: 'Unable to Reset Password', status: 'Active' },
    { id: 5, title: 'App Crashes', status: 'In Progress' },
    { id: 6, title: 'Feature Request', status: 'Active' },
    { id: 7, title: 'Bug in Dashboard', status: 'Closed' },
    { id: 8, title: 'Login Issues', status: 'In Progress' },
    { id: 9, title: 'Profile Update Failed', status: 'Closed' },
    { id: 10, title: 'Support Ticket Not Responded', status: 'Active' },
  ];

  return (
    <div className='space-y-4'>
      {complaints.map((complaint) => (
        <ComplaintBox key={complaint.id} complaint={complaint} />
      ))}
    </div>
  );
};

const ComplaintBox = ({ complaint }: { complaint: any }) => {
  const notifications = 4;

  const getStatusColor2 = (status: string) => {
    switch (status) {
      case 'Active':
        return 'text-green-500 bg-green-50';
      case 'In Progress':
        return 'text-yellow-500 bg-yellow-50';
      case 'Closed':
        return 'text-red-500 bg-red-50';
      default:
        return 'text-gray-500 bg-gray-50';
    }
  };

  return (
    <div className='rounded-xl border p-4 lg:min-h-24 flex flex-col justify-start items-start md:flex-row md:justify-between md:items-center'>
      <div className='flex gap-4 items-start'>
        <div className='bg-primaryColor-50 text-primaryColor size-14 flex items-center justify-center rounded'>
          <Wrench size={30} />
        </div>
        <div className='flex flex-col gap-2'>
          <div className='flex items-start gap-4'>
            <h3 className='text-base font-semibold'>{complaint.title}</h3>
            <span className={`px-4 py-0.5 rounded-full text-sm whitespace-pre ${getStatusColor2(complaint.status)}`}>{complaint.status}</span>
          </div>
          <p className='text-gray-400 text-[15px] uppercase'>{format(new Date(), 'dd MMM yyyy hh:mm a')}</p>
        </div>
      </div>
      <Link href='/tutor-dash/support/complaints/1' className='flex items-center max-md:justify-between max-md:w-full gap-4'>
        {notifications > 0 && (
          <button className='relative'>
            <MessageSquareText size={25} strokeWidth={1.5} className='text-primaryColor' />
            <span className='absolute -top-1 -right-1 bg-gradient-1 text-white rounded-full text-xs h-4 w-4 flex items-center justify-center'>
              {notifications}
            </span>
          </button>
        )}
        <button className='text-primaryColor'>
          <FileCog />
        </button>
      </Link>
    </div>
  );
};

export default TutorDetailPage;
