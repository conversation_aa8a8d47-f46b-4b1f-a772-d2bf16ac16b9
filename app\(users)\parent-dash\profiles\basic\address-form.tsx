'use client';

import { Form } from '@/components/ui/form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import { PrimaryInput, PrimaryLocationInput, PrimarySwitchInput, SubmitButton } from '@/components/forms';
import { useEffect, useState } from 'react';
import { addressSchema } from '@/validation/schemas/user.schema';
import { useCreateAddress, useUpdateAddress } from '@/hooks/users/user.hooks';
import { toast } from 'react-toastify';

type AddressFormProps = {
  address: Record<string, any>;
  showSameAsCurrentCheckbox?: boolean;
  onSameAsCurrentChange?: (checked: boolean) => void;
  isSameAsCurrent?: boolean;
  currentAddressData?: Record<string, any>;
  addressId?: string;
  addressType?: 'current' | 'permanent';
  onSuccess?: () => void;
};

const AddressForm = ({
  address,
  showSameAsCurrentCheckbox = false,
  onSameAsCurrentChange,
  isSameAsCurrent = false,
  currentAddressData,
  addressId,
  addressType = 'current',
  onSuccess,
}: AddressFormProps) => {
  const { mutateAsync: createAddress, isPending: isCreating } = useCreateAddress();
  const { mutateAsync: updateAddress, isPending: isUpdating } = useUpdateAddress();
  const [parsedAddress, setParsedAddress] = useState({});

  const extendedSchema = addressSchema.extend({
    sameAsCurrentAddress: z.boolean().optional(),
  });

  const form = useForm<z.infer<typeof extendedSchema>>({
    resolver: zodResolver(extendedSchema),
    defaultValues: {
      houseNo: address?.houseNo || '',
      locality: address?.locality || '',
      landmark: address?.landmark || '',
      areaPinCode: address?.areaPinCode || '',
      city: address?.city || '',
      district: address?.district || '',
      state: address?.state || '',
      addressType: addressType,
      sameAsCurrentAddress: isSameAsCurrent,
      lat: address?.lat || 0,
      lng: address?.lng || 0,
    },
  });

  useEffect(() => {
    if (parsedAddress) {
      Object.entries(parsedAddress).forEach(([key, value]) => {
        form.setValue(key as any, value || '');
      });
    }
  }, [parsedAddress, form]);

  useEffect(() => {
    form.setValue('sameAsCurrentAddress', isSameAsCurrent);

    if (isSameAsCurrent && currentAddressData) {
      const fieldsToSkip = ['_id', 'user', '__v', 'createdAt', 'updatedAt', 'addressType'];

      Object.entries(currentAddressData).forEach(([key, value]) => {
        if (!fieldsToSkip.includes(key)) {
          form.setValue(key as any, value || '');
        }
      });

      form.setValue('addressType', addressType);
    }
  }, [isSameAsCurrent, currentAddressData, form, addressType]);

  async function onSubmit(values: z.infer<typeof addressSchema>) {
    try {
      values.addressType = addressType;
      let response;

      if (addressId) {
        response = await updateAddress({ addressId, data: values });
      } else {
        response = await createAddress(values);
      }

      if (onSuccess) {
        onSuccess();
      }

      const message = isSameAsCurrent ? 'Permanent address saved using current address details' : response.message || 'Address saved successfully';
      toast.success(message);

      return response;
    } catch (error: any) {
      toast.error(error.message || 'Failed to save address');
    }
  }

  return (
    <div className='w-full'>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className='grid grid-cols-1 md:grid-cols-2 items-start gap-6'>
          {showSameAsCurrentCheckbox && (
            <div className='md:col-span-2 mb-4'>
              <PrimarySwitchInput
                form={form}
                name='sameAsCurrentAddress'
                label='Same as Current Address'
                description='Checking this will automatically save your permanent address with your current address details'
                onCheckedChange={(checked: boolean) => {
                  if (onSameAsCurrentChange) {
                    onSameAsCurrentChange(checked);
                  }

                  if (checked) {
                    const values = form.getValues();
                    onSubmit(values);
                  }
                }}
                containerClassName='p-3 bg-gray-50 rounded-md'
              />
            </div>
          )}

          <PrimaryInput form={form} name='houseNo' label='House No' placeholder='Enter your house number' required disabled={isSameAsCurrent} />
          <PrimaryLocationInput
            setParsedAddress={setParsedAddress}
            form={form}
            name='locality'
            label='Locality'
            placeholder='Enter your locality'
            required
            disabled={isSameAsCurrent}
          />
          <PrimaryInput form={form} name='landmark' label='Landmark' placeholder='Enter a nearby landmark' disabled={isSameAsCurrent} />
          <PrimaryInput
            form={form}
            name='areaPinCode'
            label='Area PIN code'
            placeholder='Enter your 6-digit PIN code'
            required
            disabled={isSameAsCurrent}
          />

          <PrimaryInput form={form} name='city' label='City' placeholder='Enter your city' required disabled={isSameAsCurrent} />
          <PrimaryInput form={form} name='district' label='District' placeholder='Enter your district' required disabled={isSameAsCurrent} />
          <PrimaryInput form={form} name='state' label='State' placeholder='Enter your state' required disabled={isSameAsCurrent} />

          <input type='hidden' {...form.register('addressType')} />

          {!isSameAsCurrent && (
            <SubmitButton
              isSubmitting={form.formState.isSubmitting || isCreating || isUpdating}
              label='Submit'
              submittingLabel='Saving...'
              className='md:col-span-2 w-fit'
            />
          )}
        </form>
      </Form>
    </div>
  );
};

export default AddressForm;
