'use client';

import { cn } from '@/lib/utils';
import { useSidebarStore } from '@/store/sidebarStore';
import { AlignJustify, Bell, HandCoins, User, Search, ChevronDown } from 'lucide-react';
import { useState, useEffect } from 'react';

const NavBar = () => {
  const { isSidebarOpen, toggleSidebar } = useSidebarStore();
  const [scrolled, setScrolled] = useState(false);
  const [showSearchOnMobile, setShowSearchOnMobile] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 10);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);
  return (
    <nav className='bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 border border-slate-700/50 px-4 py-3 w-full flex justify-between items-center rounded-3xl shadow-2xl relative z-10 sidebar-glow overflow-hidden'>
      {/* Background */}
      <div className='absolute inset-0 bg-gradient-to-br from-emerald-600/10 via-teal-600/10 to-cyan-600/10 opacity-50'></div>
      <div className='absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-emerald-500/10 to-teal-500/10 rounded-full blur-3xl'></div>
      <div className='absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-teal-500/10 to-cyan-500/10 rounded-full blur-2xl'></div>
      {/* Left side */}
      <div className='flex items-center gap-4 relative z-10'>
        <button
          className={cn(
            'flex items-center justify-center p-2.5 rounded-xl transition-all duration-300',
            !isSidebarOpen ? 'bg-gradient-to-r from-emerald-600 to-teal-600 text-white shadow-lg' : 'bg-white/10 text-white hover:bg-white/20'
          )}
          onClick={toggleSidebar}
          aria-label={isSidebarOpen ? 'Hide sidebar' : 'Show sidebar'}
        >
          <AlignJustify size={18} strokeWidth={1.5} className={cn('transition-transform duration-300', !isSidebarOpen ? 'rotate-180' : '')} />
        </button>
        <div className='hidden sm:flex items-center gap-3'>
          <h1 className='font-semibold text-white/90 text-lg'>Dashboard</h1>
          <span className='text-xs px-2 py-0.5 bg-emerald-600/30 text-emerald-200 rounded-xl border border-emerald-500/20'>Tutor</span>
        </div>
      </div>
      {/* Search input */}
      <div
        className={cn(
          'hidden md:block md:w-[260px] lg:w-[320px] xl:w-[380px] mx-4 relative z-10',
          showSearchOnMobile && 'block w-[90%] absolute left-[5%] top-16 p-4 z-50'
        )}
      >
        <div className='relative group'>
          <div className='absolute left-4 top-1/2 -translate-y-1/2 text-white/50 group-focus-within:text-emerald-400 transition-colors z-20'>
            <Search size={16} strokeWidth={1.5} />
          </div>
          <input
            type='text'
            placeholder='Get courses, students, resources...'
            className={cn(
              'w-full bg-white/10 backdrop-blur-sm border border-white/10 rounded-xl py-2.5 pl-10 pr-4 text-sm text-white',
              'outline-none focus:ring-2 focus:ring-emerald-500/30 focus:border-emerald-500/30',
              'transition-all duration-300 placeholder:text-white/50 hover:bg-white/20'
            )}
          />
        </div>
      </div>
      {/* Right side */}
      <div className='flex items-center ml-auto gap-3 relative z-10'>
        {/* Mobile search */}
        <button
          className='md:hidden p-2.5 rounded-xl bg-white/10 text-white hover:bg-white/20 transition-all duration-300'
          onClick={() => setShowSearchOnMobile(!showSearchOnMobile)}
        >
          <Search size={18} strokeWidth={1.5} />
        </button>
        {/* Coins */}
        <div className='flex items-center bg-amber-500/20 backdrop-blur-sm border border-amber-500/30 rounded-xl p-1 pl-2 pr-3 shadow-md hover:shadow-lg transition-all duration-300 group'>
          <div className='bg-gradient-to-br from-amber-500 to-yellow-500 text-white rounded-lg w-7 h-7 flex items-center justify-center mr-2 shadow-inner relative overflow-hidden'>
            <div className='absolute inset-0 bg-white opacity-20 animate-pulse'></div>
            <HandCoins size={14} strokeWidth={1.5} />
          </div>
          <div className='flex flex-col items-start justify-start'>
            <span className='font-medium text-sm text-amber-100'>630</span>
            <span className='text-amber-300/80 text-xs leading-none'>Coins</span>
          </div>
        </div>
        {/* Notifications */}
        <button className='relative p-2.5 rounded-xl bg-white/10 hover:bg-white/20 transition-all duration-300 group'>
          <Bell size={18} className='text-white group-hover:text-emerald-300 transition-colors' strokeWidth={1.5} />
          <span className='absolute top-2 right-2 w-2 h-2 bg-red-500 rounded-full animate-pulse ring-4 ring-red-500/20'></span>
        </button>
        {/* User profile with dropdown indicator */}
        <button className='hidden md:flex items-center gap-2 bg-white/10 hover:bg-white/20 py-1.5 px-3 rounded-xl transition-all duration-300 group border border-white/20 hover:border-white/30'>
          <div className='w-8 h-8 bg-gradient-to-br from-emerald-500 to-teal-600 text-white rounded-lg flex items-center justify-center shadow-lg ring-1 ring-white/10'>
            <User size={16} strokeWidth={1.5} />
          </div>
          <div className='flex items-center gap-1.5'>
            <span className='text-sm font-medium text-white group-hover:text-emerald-300 transition-colors'>Yevhen</span>
            <ChevronDown size={14} className='text-white/50 group-hover:text-emerald-300 transition-colors' strokeWidth={2} />
          </div>
        </button>
        {/* Mobile user icon */}
        <button className='md:hidden w-9 h-9 bg-gradient-to-br from-primaryColor-400 to-primaryColor-600 text-white rounded-full flex items-center justify-center shadow-sm'>
          <User size={16} />
        </button>
      </div>
    </nav>
  );
};

export default NavBar;
