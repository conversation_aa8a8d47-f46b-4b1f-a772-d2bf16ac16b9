import { useQuery, useMutation } from '@tanstack/react-query';
import { QUERY_KEYS } from '@/lib/react-query/queryKeys';
import { queryClient } from '@/lib/react-query/queryClient';

import { boardService, classService, subjectService } from '@/server/services/education/school.service';
import {
  CreateBoardInput,
  UpdateBoardInput,
  CreateClassInput,
  UpdateClassInput,
  CreateSubjectInput,
  UpdateSubjectInput,
} from '@/validation/schemas/education/school.schema';

const educationService = { boards: boardService, classes: classService, subjects: subjectService };

// School Boards Hooks
export function useGetAllBoards(params?: Record<string, any>, options = {}) {
  return useQuery({
    queryKey: [QUERY_KEYS.EDUCATION.BOARDS, params],
    queryFn: () => educationService.boards.getAllBoards(params),
    ...options,
  });
}

export function useGetBoardById(id: string, options = {}) {
  return useQuery({
    queryKey: [QUERY_KEYS.EDUCATION.BOARD, id],
    queryFn: () => educationService.boards.getBoardById(id),
    enabled: !!id,
    ...options,
  });
}

export function useCreateBoard() {
  return useMutation({
    mutationFn: (data: CreateBoardInput) => educationService.boards.createBoard(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.EDUCATION.BOARDS] });
    },
  });
}

export function useUpdateBoard() {
  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateBoardInput }) => educationService.boards.updateBoard(id, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.EDUCATION.BOARDS] });
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.EDUCATION.BOARD, variables.id] });
    },
  });
}

export function useDeleteBoard() {
  return useMutation({
    mutationFn: (id: string) => educationService.boards.deleteBoard(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.EDUCATION.BOARDS] });
    },
  });
}

// School Classes Hooks
export function useGetAllClasses(params?: Record<string, any>, options = {}) {
  return useQuery({
    queryKey: [QUERY_KEYS.EDUCATION.CLASSES, params],
    queryFn: () => educationService.classes.getAllClasses(params),
    ...options,
  });
}

export function useGetClassesByBoard(boardId: string, params?: Record<string, any>, options = {}) {
  return useQuery({
    queryKey: [QUERY_KEYS.EDUCATION.CLASSES_BY_BOARD, boardId, params],
    queryFn: () => educationService.classes.getClassesByBoard(boardId, params),
    enabled: !!boardId,
    ...options,
  });
}

export function useGetClassById(id: string, options = {}) {
  return useQuery({
    queryKey: [QUERY_KEYS.EDUCATION.CLASS, id],
    queryFn: () => educationService.classes.getClassById(id),
    enabled: !!id,
    ...options,
  });
}

export function useCreateClass() {
  return useMutation({
    mutationFn: (data: CreateClassInput) => educationService.classes.createClass(data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.EDUCATION.CLASSES] });
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.EDUCATION.CLASSES_BY_BOARD, variables.board] });
    },
  });
}

export function useUpdateClass() {
  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateClassInput }) => educationService.classes.updateClass(id, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.EDUCATION.CLASSES] });
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.EDUCATION.CLASS, variables.id] });
      if (variables.data.board) {
        queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.EDUCATION.CLASSES_BY_BOARD, variables.data.board] });
      }
    },
  });
}

export function useDeleteClass() {
  return useMutation({
    mutationFn: (id: string) => educationService.classes.deleteClass(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.EDUCATION.CLASSES] });
    },
  });
}

// School Subjects Hooks
export function useGetAllSubjects(params?: Record<string, any>, options = {}) {
  return useQuery({
    queryKey: [QUERY_KEYS.EDUCATION.SUBJECTS, params],
    queryFn: () => educationService.subjects.getAllSubjects(params),
    ...options,
  });
}

export function useGetSubjectsByClass(classId: string, params?: Record<string, any>, options = {}) {
  return useQuery({
    queryKey: [QUERY_KEYS.EDUCATION.SUBJECTS_BY_CLASS, classId, params],
    queryFn: () => educationService.subjects.getSubjectsByClass(classId, params),
    enabled: !!classId,
    ...options,
  });
}

export function useGetSubjectById(id: string, options = {}) {
  return useQuery({
    queryKey: [QUERY_KEYS.EDUCATION.SUBJECT, id],
    queryFn: () => educationService.subjects.getSubjectById(id),
    enabled: !!id,
    ...options,
  });
}

export function useCreateSubject() {
  return useMutation({
    mutationFn: (data: CreateSubjectInput) => educationService.subjects.createSubject(data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.EDUCATION.SUBJECTS] });
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.EDUCATION.SUBJECTS_BY_CLASS, variables.class] });
    },
  });
}

export function useUpdateSubject() {
  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateSubjectInput }) => educationService.subjects.updateSubject(id, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.EDUCATION.SUBJECTS] });
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.EDUCATION.SUBJECT, variables.id] });
      if (variables.data.class) {
        queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.EDUCATION.SUBJECTS_BY_CLASS, variables.data.class] });
      }
    },
  });
}

export function useDeleteSubject() {
  return useMutation({
    mutationFn: (id: string) => educationService.subjects.deleteSubject(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.EDUCATION.SUBJECTS] });
    },
  });
}
