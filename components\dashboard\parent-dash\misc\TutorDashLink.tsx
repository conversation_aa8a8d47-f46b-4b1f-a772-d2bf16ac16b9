import Link, { LinkProps } from 'next/link';
import { PARENT_DASH_PATH } from '@/constants/parent-dash';

interface IParentDashLink extends LinkProps {
  children: React.ReactNode;
  className?: string;
}

const ParentDashLink = ({ children, href, className, ...props }: IParentDashLink) => (
  <Link href={`${PARENT_DASH_PATH}${href}`} className={className} {...props}>
    {children}
  </Link>
);

export default ParentDashLink;
