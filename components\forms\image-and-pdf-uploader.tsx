'use client';

import { useDropzone } from 'react-dropzone';
import { Loader2, X, Upload, File, FileText } from 'lucide-react';
import Image from 'next/image';
import { toast } from 'react-toastify';
import { useState } from 'react';
import { FormField, FormItem, FormLabel, FormControl, FormMessage } from '@/components/ui/form';
import type { UseFormReturn, FieldValues, Path } from 'react-hook-form';
import { cn } from '@/lib/utils';
import { getImageUrl } from '@/lib/string.utils';

interface SimpleFileUploadProps<TFieldValues extends FieldValues> {
  form: UseFormReturn<TFieldValues>;
  name: Path<TFieldValues>;
  label?: string;
  description?: string;
  required?: boolean;
  disabled?: boolean;
  variant?: 'primary' | 'secondary' | 'green' | 'purple';
  accept?: { [key: string]: string[] };
}

const ImageAndPdfUploader = <TFieldValues extends FieldValues>({
  form,
  name,
  label,
  description,
  required = false,
  disabled = false,
  variant = 'primary',
  accept = { 'image/*': ['.jpg', '.jpeg', '.png'], 'application/pdf': ['.pdf'] },
}: SimpleFileUploadProps<TFieldValues>) => {
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);

  const maxSize = 5 * 1024 * 1024; // 5MB

  const file = form.watch(name);
  const hasFile = !!file && file !== '#';

  const colorMap = {
    primary: {
      bg: 'bg-primaryColor-50',
      bgHover: 'hover:bg-primaryColor-100',
      border: 'border-primaryColor-200',
      borderHover: 'hover:border-primaryColor-300',
      text: 'text-primaryColor-600',
      icon: 'text-primaryColor-500',
      iconBg: 'bg-primaryColor-100',
      progress: 'bg-primaryColor-500',
    },
    secondary: {
      bg: 'bg-secondaryColor-50',
      bgHover: 'hover:bg-secondaryColor-100',
      border: 'border-secondaryColor-200',
      borderHover: 'hover:border-secondaryColor-300',
      text: 'text-secondaryColor-600',
      icon: 'text-secondaryColor-500',
      iconBg: 'bg-secondaryColor-100',
      progress: 'bg-secondaryColor-500',
    },
    purple: {
      bg: 'bg-purple-50',
      bgHover: 'hover:bg-purple-100',
      border: 'border-purple-200',
      borderHover: 'hover:border-purple-300',
      text: 'text-purple-600',
      icon: 'text-purple-500',
      iconBg: 'bg-purple-100',
      progress: 'bg-purple-500',
    },
    green: {
      bg: 'bg-green-50',
      bgHover: 'hover:bg-green-100',
      border: 'border-green-200',
      borderHover: 'hover:border-green-300',
      text: 'text-green-600',
      icon: 'text-green-500',
      iconBg: 'bg-green-100',
      progress: 'bg-green-500',
    },
  };

  const colors = colorMap[variant];

  const removeFile = () => {
    if (disabled || uploading) return;
    form.setValue(name, undefined as any, { shouldValidate: true });
  };

  const { getRootProps, getInputProps } = useDropzone({
    onDrop: (acceptedFiles, _, event) => {
      if (event instanceof Event) {
        event.preventDefault();
        event.stopPropagation();
      }

      if (disabled) return;

      if (hasFile) {
        toast.error('Only one file is allowed. Remove the current file first.');
        return;
      }

      if (acceptedFiles.length === 0) {
        toast.error('No valid files were selected.');
        return;
      }

      if (acceptedFiles.length > 1) {
        toast.error('Only one file is allowed.');
        return;
      }

      form.setValue(name, acceptedFiles[0] as any, { shouldValidate: true });

      setUploading(true);
      setUploadProgress(0);

      const interval = setInterval(() => {
        setUploadProgress((prev) => {
          const newProgress = prev + 20;
          if (newProgress >= 100) {
            clearInterval(interval);

            setTimeout(() => {
              setUploading(false);
            }, 300);
          }
          return newProgress > 100 ? 100 : newProgress;
        });
      }, 200);
    },
    accept,
    maxSize,
    maxFiles: 1,
    multiple: false,
    onDropRejected: (rejectedFiles) => {
      rejectedFiles.forEach((file) => {
        file.errors.forEach((error) => {
          switch (error.code) {
            case 'file-too-large':
              toast.error(`File is too large. Maximum size is ${maxSize / 1024 / 1024} MB.`);
              break;
            case 'file-invalid-type':
              toast.error('Invalid file type. Please upload an image or PDF file.');
              break;
            case 'too-many-files':
              toast.error('Only one file is allowed.');
              break;
            default:
              toast.error('File upload error. Please try again.');
          }
        });
      });
    },
  });

  const renderFilePreview = () => {
    if (!file) return null;

    const truncateFilename = (name: string, maxLength = 25) => {
      if (name.length <= maxLength) return name;

      const extension = name.split('.').pop() || '';
      const nameWithoutExt = name.substring(0, name.length - extension.length - 1);

      if (nameWithoutExt.length > maxLength) {
        const start = nameWithoutExt.substring(0, Math.floor(maxLength / 2) - 2);
        const end = nameWithoutExt.substring(nameWithoutExt.length - Math.floor(maxLength / 2) + 2);
        return `${start}...${end}.${extension}`;
      }

      return name;
    };

    if (typeof file === 'string') {
      const isImage = file.match(/\.(jpg|jpeg|png|gif|webp|bmp|svg|ico)$/i);
      const fileName = file.split('/').pop() || 'file';
      const displayName = truncateFilename(fileName);
      const fileType = file.split('.').pop()?.toUpperCase() || 'FILE';

      return (
        <div className='bg-white border rounded-md shadow-sm overflow-hidden'>
          <div className='relative'>
            {isImage ? (
              <div className='w-full h-32 bg-gray-50 flex items-center justify-center overflow-hidden'>
                <img src={getImageUrl(file)} alt={fileName} width={150} height={120} className='object-contain max-h-full' />
              </div>
            ) : (
              <div className='w-full h-24 flex items-center justify-center bg-gray-50'>
                <div className={cn('w-16 h-16 rounded-md flex items-center justify-center', colors.iconBg)}>
                  <FileText size={32} className={colors.icon} />
                </div>
              </div>
            )}

            <button
              type='button'
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                removeFile();
              }}
              className='absolute top-2 right-2 w-6 h-6 rounded-full bg-white/90 flex items-center justify-center hover:bg-white transition-colors flex-shrink-0 shadow-sm'
            >
              <X size={14} className='text-gray-600' />
            </button>
          </div>

          <div className='p-3 border-t border-gray-100'>
            <p className='text-sm font-medium text-gray-800 truncate' title={fileName}>
              {displayName}
            </p>
            <div className='flex items-center gap-1 mt-1'>
              <span className='text-xs text-gray-500'>{fileType}</span>
              {typeof file === 'object' && file.size && <span className='text-xs text-gray-400'>• {Math.round(file.size / 1024)} KB</span>}
            </div>
          </div>
        </div>
      );
    } else if (typeof file === 'object' && file !== null) {
      const isImage = file.type && file.type.startsWith('image/');
      const displayName = truncateFilename(file.name || 'file');

      return (
        <div className='bg-white border rounded-md shadow-sm overflow-hidden'>
          <div className='relative'>
            {isImage ? (
              <div className='w-full h-32 bg-gray-50 flex items-center justify-center overflow-hidden'>
                <Image src={URL.createObjectURL(file)} alt={file.name || 'Preview'} width={150} height={120} className='object-contain max-h-full' />
              </div>
            ) : (
              <div className='w-full h-24 flex items-center justify-center bg-gray-50'>
                <div className={cn('w-16 h-16 rounded-md flex items-center justify-center', colors.iconBg)}>
                  {file.type === 'application/pdf' ? <FileText size={32} className={colors.icon} /> : <File size={32} className={colors.icon} />}
                </div>
              </div>
            )}

            <button
              type='button'
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                removeFile();
              }}
              className='absolute top-2 right-2 w-6 h-6 rounded-full bg-white/90 flex items-center justify-center hover:bg-white transition-colors flex-shrink-0 shadow-sm'
            >
              <X size={14} className='text-gray-600' />
            </button>
          </div>

          <div className='p-3 border-t border-gray-100'>
            <p className='text-sm font-medium text-gray-800 truncate' title={file.name}>
              {displayName}
            </p>
            <div className='flex items-center gap-1 mt-1'>
              <span className='text-xs text-gray-500'>{file.type ? file.type.split('/')[1].toUpperCase() : 'FILE'}</span>
              {file.size && <span className='text-xs text-gray-400'>• {Math.round(file.size / 1024)} KB</span>}
            </div>
          </div>
        </div>
      );
    }

    return null;
  };

  return (
    <FormField
      control={form.control}
      name={name}
      render={() => (
        <FormItem className='w-full'>
          {label && (
            <FormLabel className='primary-label'>
              <span>{label}</span>
              {required && <span className='text-primaryColor ml-1'>*</span>}
            </FormLabel>
          )}
          <FormControl>
            <div className='w-full'>
              {!hasFile ? (
                <div
                  {...getRootProps({
                    className: cn(
                      'border-2 border-dashed rounded-xl p-6 text-center transition-colors cursor-pointer focus:outline-none',
                      colors.border,
                      colors.borderHover,
                      'bg-white',
                      disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'
                    ),
                    onClick: undefined,
                  })}
                >
                  <input {...getInputProps()} disabled={disabled} />
                  <div className='flex flex-col items-center justify-center'>
                    <div className={cn('w-16 h-16 rounded-full flex items-center justify-center mb-3', colors.iconBg)}>
                      <Upload size={28} className={colors.icon} />
                    </div>
                    <p className='text-sm font-medium text-gray-700 mb-1'>{"Drag 'n' drop a file here, or click to select"}</p>
                    <p className='text-xs text-gray-500'>Allowed file types: JPG, JPEG, PNG, PDF</p>
                    {description && <p className='text-xs text-gray-500 mt-2'>{description}</p>}
                  </div>
                </div>
              ) : (
                <div className='mt-1'>{renderFilePreview()}</div>
              )}

              {uploading && (
                <div className='mt-2 bg-white border rounded-md shadow-sm overflow-hidden'>
                  <div className='p-4 flex flex-col'>
                    <div className='flex items-center gap-3 mb-3'>
                      <div className={cn('w-8 h-8 rounded-md flex items-center justify-center', colors.iconBg)}>
                        <Loader2 size={18} className={cn('animate-spin', colors.icon)} />
                      </div>
                      <p className='text-sm font-medium text-gray-800'>Uploading file...</p>
                    </div>

                    <div className='flex items-center gap-2'>
                      <div className='w-full h-2 bg-gray-100 rounded-full overflow-hidden'>
                        <div className={cn('h-full', colors.progress)} style={{ width: `${uploadProgress}%` }}></div>
                      </div>
                      <span className='text-xs font-medium text-gray-500 min-w-[36px]'>{uploadProgress}%</span>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
};

export default ImageAndPdfUploader;
