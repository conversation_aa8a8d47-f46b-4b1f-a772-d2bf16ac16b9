import type React from 'react';
import { Mail, Phone, Briefcase, MapPin } from 'lucide-react';
import { FaWhatsapp } from 'react-icons/fa';
import { RightSideBar } from './right-sidebar';
import TutorForm from './TutorForm';

export const SheetHeader: React.FC = () => {
  return (
    <div className='bg-white p-6 rounded-2xl shadow-lg relative overflow-hidden'>
      <div className='absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-blue-400 to-sky-700'></div>
      <div className='flex items-start gap-6'>
        <div className='relative group'>
          <div className='absolute -inset-0.5 bg-gradient-to-r from-blue-600 to-sky-600 rounded-full blur opacity-75 group-hover:opacity-100 transition duration-1000 group-hover:duration-200 animate-tilt'></div>
          <div className='relative h-24 w-24 rounded-full bg-white ring-2 ring-sky-200 overflow-hidden transition-all duration-300 ease-in-out transform group-hover:scale-110'>
            <img src='https://i.pravatar.cc/200' alt='<PERSON>' className='w-full h-full object-cover' />
          </div>
          <div className='absolute bottom-0 right-0 bg-green-500 h-4 w-4 rounded-full border-2 border-white'></div>
        </div>

        <div className='flex-grow'>
          <div className='flex justify-between items-start mb-2'>
            <h3 className='text-2xl font-bold text-gray-700'>John Doe</h3>
            <div className='flex gap-2'>
              <button className='p-2 rounded-lg bg-blue-100 text-blue-600 hover:bg-blue-200 transition-all duration-200' aria-label='Email'>
                <Mail className='h-5 w-5' />
              </button>
              <button className='p-2 rounded-lg bg-sky-100 text-sky-600 hover:bg-sky-200 transition-all duration-200' aria-label='Call'>
                <Phone className='h-5 w-5' />
              </button>
              <button className='p-2 rounded-lg bg-green-100 text-green-600 hover:bg-green-200 transition-all duration-200' aria-label='WhatsApp'>
                <FaWhatsapp className='h-5 w-5' />
              </button>
            </div>
          </div>

          <div className='flex items-center gap-2 text-gray-600 mb-3'>
            <Briefcase className='h-4 w-4 text-sky-500' />
            <span className='text-sm font-medium'>Senior Developer</span>
            <MapPin className='h-4 w-4 text-sky-500' />
            <span className='text-sm font-medium'>San Francisco, CA</span>
          </div>

          <div className='space-y-2'>
            <p className='flex items-center gap-2 text-sm text-gray-600 hover:text-blue-600 transition-colors duration-200'>
              <Mail className='h-4 w-4 text-blue-500' />
              <span><EMAIL></span>
            </p>

            <div className='flex items-center gap-4'>
              <div className='flex items-center gap-2 text-gray-600'>
                <Phone className='h-4 w-4 text-green-500' />
                <span className='text-sm'>****** 567 8900</span>
              </div>

              <div className='flex items-center gap-2 text-gray-600'>
                <FaWhatsapp className='h-4 w-4 text-green-500' />
                <span className='text-sm'>****** 567 8900</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className='mt-4 pt-4 border-t border-gray-100'>
        <div className='flex gap-2'>
          <span className='px-2 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded'>React</span>
          <span className='px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded'>Node.js</span>
          <span className='px-2 py-1 bg-yellow-100 text-yellow-800 text-xs font-medium rounded'>TypeScript</span>
        </div>
      </div>
    </div>
  );
};

export const SheetContent: React.FC = () => {
  return <RightSideBar />;
};

export const SheetContentAdd: React.FC = () => {
  return (
    <div className='p-6'>
      <TutorForm />
    </div>
  );
};
