'use client';

import { useState, useEffect, useCallback } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { HeadingBar, LimitFilter, MasterFilter, MasterTable, SectionWrapper } from '@/components/dashboard/ascrm/misc';
import { ErrorState } from '@/components/dashboard/ascrm/misc/StateUI';
import { filterTypeMaps, FilterTypeKey } from '@/constants';
import { FilterValues, FilterOption } from '@/types/api';
import { useGetAllServiceCategories } from '@/hooks/service-category.hooks';
import { serviceCategoryMap, IServiceCategoryMap } from '@/validation/schemas/education/index.maps';
import { AdminViewButton, PrimaryFormModal, useModal, StatusToggle } from '@/components/dashboard/shared/misc';
import ServiceForm from './service-form';
import serviceCategoryService from '@/server/services/service-category.service';
import { QUERY_KEYS } from '@/lib/react-query/queryKeys';

const categoriesMap = {
  schools: { href: '/boards', label: 'Boards' },
  colleges: { href: '/streams', label: 'Streams' },
  hobbies: { href: '/hobby-types', label: 'Hobby Types' },
  languages: { href: '/language-types', label: 'Language Types' },
  it_courses: { href: '/course-types', label: 'Course Types' },
  exams: { href: '/exam-categories', label: 'Exam Categories' },
} as const;

const ServiceTable: React.FC = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const addServiceModal = useModal();

  const [filters, setFilters] = useState<FilterValues>({});

  const updateFiltersFromParams = useCallback(() => {
    const initialFilters: FilterValues = {};
    searchParams.forEach((value, key) => {
      if (key !== 'page') {
        initialFilters[key] = value;
      }
    });
    setFilters(initialFilters);
  }, [searchParams]);

  useEffect(() => {
    updateFiltersFromParams();
  }, [updateFiltersFromParams]);

  const page = Number(searchParams.get('page')) || 1;

  const queryParams = { page, ...filters };

  const { data: serviceCategoriesResponse, isLoading, error } = useGetAllServiceCategories(queryParams);

  const serviceCategories = serviceCategoriesResponse?.data?.serviceCategories || [];
  const pagination = serviceCategoriesResponse?.data?.pagination;

  if (error) {
    return <ErrorState message={error.message} />;
  }

  const headers = ['Name', 'Status', 'Actions'];

  const rows = serviceCategories.map((serviceCategory) => {
    const name = serviceCategoryMap[serviceCategory.name as IServiceCategoryMap].label;
    const { href, label } = categoriesMap[serviceCategory.name as IServiceCategoryMap];
    return {
      id: serviceCategory._id,
      values: [
        <p className='font-medium'>{name}</p>,
        <StatusToggle
          id={serviceCategory._id}
          isActive={serviceCategory.isActive}
          updateFn={serviceCategoryService.updateServiceCategory}
          queryKey={[QUERY_KEYS.EDUCATION.SERVICE_CATEGORIES]}
          entityName='Service Category'
        />,
        <AdminViewButton href={href} label={label} />,
      ],
    };
  });

  const filterOptions: FilterOption[] = [
    {
      type: filterTypeMaps.input.key as FilterTypeKey,
      label: 'Service Category Name',
      key: 'name',
    },
  ];

  const handleApplyFilters = (newFilters: FilterValues) => {
    const params = new URLSearchParams(searchParams);
    Object.entries(newFilters).forEach(([key, value]) => {
      if (value && typeof value === 'string') {
        params.set(key, value);
      } else {
        params.delete(key);
      }
    });
    params.set('page', '1');
    router.push(`?${params.toString()}`);
  };

  const handleResetFilters = () => {
    router.push(`?page=1`);
  };

  return (
    <SectionWrapper>
      <HeadingBar
        title='Service Categories'
        elements={[
          <LimitFilter
            key='limit'
            value={(filters.limit as string) || '10'}
            onChange={(value: string) => {
              handleApplyFilters({ limit: value });
            }}
          />,
          <MasterFilter
            key='filter'
            filterOptions={filterOptions}
            onApplyFilters={handleApplyFilters}
            onResetFilters={handleResetFilters}
            initialFilters={filters}
          />,
        ]}
        addAction={addServiceModal.open}
      />
      <MasterTable headers={headers} rows={rows} isLoading={isLoading} pagination={pagination} />

      {/* Add Service Modal */}
      <PrimaryFormModal
        isOpen={addServiceModal.isOpen}
        onClose={addServiceModal.close}
        title='Add New Service Category'
        size='sm'
        showSparkles={true}
      >
        <ServiceForm onSuccess={addServiceModal.close} />
      </PrimaryFormModal>
    </SectionWrapper>
  );
};

export default ServiceTable;
