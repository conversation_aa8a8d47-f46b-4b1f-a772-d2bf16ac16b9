'use client';

import { useState } from 'react';
import { BookOpen, Plus } from 'lucide-react';
import { Backgrounds, VisitorsDeleteModal, PrimaryBadgeTabs } from '@/components/dashboard/shared/misc';
import TeachingSubjectsForm from './teaching-subjects-form';
import TeachingSubjectsList from './teaching-subjects-list';
import { ITeachingSubjectDocument } from '@/server/services/tutor/tuition-profile.service';
import { useDeleteTeachingSubject } from '@/hooks/tutor/tuition-profile.hooks';
import { serviceCategoryMap } from '@/validation/schemas/education/index.maps';

interface TeachingSubjectsSectionProps {
  subjects: ITeachingSubjectDocument[];
}

const TeachingSubjectsSection = ({ subjects }: TeachingSubjectsSectionProps) => {
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingSubject, setEditingSubject] = useState<ITeachingSubjectDocument | undefined>(undefined);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [subjectToDelete, setSubjectToDelete] = useState<ITeachingSubjectDocument | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<string>('schools');

  const deleteSubject = useDeleteTeachingSubject();

  const handleAddNew = () => {
    setEditingSubject(undefined);
    setIsFormOpen(true);
  };

  const handleEdit = (subject: ITeachingSubjectDocument) => {
    setEditingSubject(subject);
    setIsFormOpen(true);
  };

  const handleDeleteClick = (subject: ITeachingSubjectDocument) => {
    setSubjectToDelete(subject);
    setDeleteModalOpen(true);
  };

  const closeForm = () => {
    setEditingSubject(undefined);
    setIsFormOpen(false);
  };

  const subjectCounts = Object.entries(serviceCategoryMap).reduce((acc, [key, { key: categoryKey }]) => {
    const count = subjects.filter((subject) => subject.serviceCategory === categoryKey).length;
    acc[categoryKey] = count;
    return acc;
  }, {} as { [key: string]: number });

  const filteredSubjects = subjects.filter((subject) => subject.serviceCategory === selectedCategory);

  return (
    <>
      <div className='bg-white rounded-2xl p-6 border border-gray-100 shadow-sm relative overflow-hidden'>
        <Backgrounds variant='primary' />
        <div className='relative z-10 flex flex-col md:flex-row justify-between items-start md:items-center'>
          <div>
            <div className='flex items-center gap-2'>
              <h2 className='text-2xl font-bold text-gray-800'>Teaching Subjects</h2>
              <span className='px-3 py-1 bg-purple-50 text-purple-600 rounded-full text-xs font-medium'>
                {subjects.length} {subjects.length === 1 ? 'subject' : 'subjects'}
              </span>
            </div>
            <p className='text-sm text-gray-500 mt-1'>Manage what you teach across different categories</p>
          </div>

          <button
            onClick={handleAddNew}
            className='mt-4 md:mt-0 flex items-center gap-2 px-5 py-2.5 bg-gradient-to-r from-red-500 to-red-600 text-white rounded-lg font-medium hover:from-red-600 hover:to-red-700 transition-all duration-300 shadow-md hover:shadow-lg hover:scale-[1.02]'
          >
            <Plus size={18} />
            <span>Add Subject</span>
          </button>
        </div>
      </div>

      {/* Category Filter Tabs */}
      <div className='my-6'>
        <PrimaryBadgeTabs
          tabs={[
            { id: 'schools', label: 'Schools', count: subjectCounts['schools'] || 0, variant: 'blue' },
            { id: 'colleges', label: 'Colleges', count: subjectCounts['colleges'] || 0, variant: 'green' },
            { id: 'languages', label: 'Languages', count: subjectCounts['languages'] || 0, variant: 'purple' },
            { id: 'hobbies', label: 'Hobbies', count: subjectCounts['hobbies'] || 0, variant: 'orange' },
            { id: 'exams', label: 'Exams', count: subjectCounts['exams'] || 0, variant: 'primary' },
            { id: 'it_courses', label: 'IT Courses', count: subjectCounts['it_courses'] || 0, variant: 'secondary' },
          ]}
          activeTab={selectedCategory}
          onChange={setSelectedCategory}
        />
      </div>

      {subjects.length > 0 ? (
        <TeachingSubjectsList subjects={filteredSubjects} selectedCategory={selectedCategory} onEdit={handleEdit} onDelete={handleDeleteClick} />
      ) : (
        <div className='flex flex-col items-center justify-center py-12 text-center bg-white rounded-xl border border-gray-100 shadow-sm relative overflow-hidden'>
          <div className='absolute top-0 inset-x-0 h-2 bg-gradient-to-r from-purple-400 to-purple-600'></div>
          <div className='absolute -right-12 -top-12 w-48 h-48 bg-purple-50 rounded-full opacity-50'></div>
          <div className='absolute -left-12 -bottom-12 w-48 h-48 bg-blue-50 rounded-full opacity-50'></div>

          <div className='w-24 h-24 rounded-full bg-gradient-to-br from-purple-50 to-purple-100 flex items-center justify-center mb-6 shadow-inner'>
            <BookOpen className='text-purple-500' size={40} />
          </div>

          <h4 className='text-2xl font-bold text-gray-800 mb-2'>No teaching subjects found</h4>
          <p className='text-gray-500 max-w-md mb-8'>
            Add the subjects you teach to help students find you and understand your expertise across different categories.
          </p>

          <button
            onClick={handleAddNew}
            className='flex items-center gap-2 px-7 py-3.5 bg-gradient-to-r from-red-500 to-red-600 text-white rounded-lg font-semibold hover:from-red-600 hover:to-red-700 transition-all duration-300 shadow-lg hover:shadow-xl hover:scale-[1.03] transform-gpu'
          >
            <Plus size={20} className='text-white' />
            <span>Add Teaching Subject</span>
          </button>
        </div>
      )}

      {/* Teaching Subjects Form Modal */}
      <TeachingSubjectsForm isOpen={isFormOpen} onClose={closeForm} subject={editingSubject} selectedCategory={selectedCategory} />

      {/* Delete Confirmation Modal */}
      <VisitorsDeleteModal
        isOpen={deleteModalOpen}
        onClose={() => {
          setDeleteModalOpen(false);
          setSubjectToDelete(null);
        }}
        onDelete={() => deleteSubject.mutateAsync(subjectToDelete?._id || '')}
        itemName={
          subjectToDelete
            ? `${
                serviceCategoryMap[subjectToDelete.serviceCategory as keyof typeof serviceCategoryMap]?.label || subjectToDelete.serviceCategory
              } subject`
            : 'this subject'
        }
      />
    </>
  );
};

export default TeachingSubjectsSection;
