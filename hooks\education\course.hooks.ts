import { useQuery, useMutation } from '@tanstack/react-query';
import { QUERY_KEYS } from '@/lib/react-query/queryKeys';
import { queryClient } from '@/lib/react-query/queryClient';

import { courseTypeService, courseService } from '@/server/services/education/course.service';
import { CreateCourseTypeInput, UpdateCourseTypeInput, CreateCourseInput, UpdateCourseInput } from '@/validation/schemas/education/course.schema';

const courseEducationService = {
  courseTypes: courseTypeService,
  courses: courseService,
};

// Course Type Hooks
export function useGetAllCourseTypes(params?: Record<string, any>, options = {}) {
  return useQuery({
    queryKey: [QUERY_KEYS.EDUCATION.COURSE_TYPES, params],
    queryFn: () => courseEducationService.courseTypes.getAllCourseTypes(params),
    ...options,
  });
}

export function useGetCourseTypeById(id: string, options = {}) {
  return useQuery({
    queryKey: [QUERY_KEYS.EDUCATION.COURSE_TYPE, id],
    queryFn: () => courseEducationService.courseTypes.getCourseTypeById(id),
    enabled: !!id,
    ...options,
  });
}

export function useCreateCourseType() {
  return useMutation({
    mutationFn: (data: CreateCourseTypeInput) => courseEducationService.courseTypes.createCourseType(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.EDUCATION.COURSE_TYPES] });
    },
  });
}

export function useUpdateCourseType() {
  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateCourseTypeInput }) => courseEducationService.courseTypes.updateCourseType(id, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.EDUCATION.COURSE_TYPES] });
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.EDUCATION.COURSE_TYPE, variables.id] });
    },
  });
}

export function useDeleteCourseType() {
  return useMutation({
    mutationFn: (id: string) => courseEducationService.courseTypes.deleteCourseType(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.EDUCATION.COURSE_TYPES] });
    },
  });
}

// Course Hooks
export function useGetAllCourses(params?: Record<string, any>, options = {}) {
  return useQuery({
    queryKey: [QUERY_KEYS.EDUCATION.COURSES, params],
    queryFn: () => courseEducationService.courses.getAllCourses(params),
    ...options,
  });
}

export function useGetCourseById(id: string, options = {}) {
  return useQuery({
    queryKey: [QUERY_KEYS.EDUCATION.COURSE, id],
    queryFn: () => courseEducationService.courses.getCourseById(id),
    enabled: !!id,
    ...options,
  });
}

export function useCreateCourse() {
  return useMutation({
    mutationFn: (data: CreateCourseInput) => courseEducationService.courses.createCourse(data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.EDUCATION.COURSES] });
    },
  });
}

export function useUpdateCourse() {
  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateCourseInput }) => courseEducationService.courses.updateCourse(id, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.EDUCATION.COURSES] });
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.EDUCATION.COURSE, variables.id] });
    },
  });
}

export function useDeleteCourse() {
  return useMutation({
    mutationFn: (id: string) => courseEducationService.courses.deleteCourse(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.EDUCATION.COURSES] });
    },
  });
}
