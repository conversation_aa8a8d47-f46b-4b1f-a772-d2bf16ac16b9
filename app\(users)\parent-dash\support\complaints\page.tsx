'use client';

import React, { useState } from 'react';
import <PERSON> from 'next/link';
import { format } from 'date-fns';
import { AlertDialog, AlertDialogCancel, AlertDialogContent, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { Select, SelectContent, SelectGroup, SelectItem, SelectLabel, SelectTrigger, SelectValue } from '@/components/ui/select';
import TutorComplaintForm from '@/components/dashboard/tutor-dash/forms/TutorComplaintForm';
import { comaplaintStatusMap, complaintCategoryMap } from '@/constants';
import {
  Bell,
  BookOpen,
  CheckCircle,
  Clock,
  DollarSign,
  Eye,
  FileCog,
  Headphones,
  Mail,
  MenuSquare,
  MessageSquareDot,
  MessageSquareText,
  MessagesSquare,
  Plus,
  ShoppingBag,
  Wrench,
  X,
  XCircle,
} from 'lucide-react';

const TutorComplaintsPage = () => {
  const [selectedTab, setSelectedTab] = useState('all');

  const complaints = [
    { id: 1, title: 'Unable to Sign Up', status: 'Active' },
    { id: 2, title: 'Payment Failed', status: 'In Progress' },
    { id: 3, title: 'Account Locked', status: 'Closed' },
    { id: 4, title: 'Unable to Reset Password', status: 'Active' },
    { id: 5, title: 'App Crashes', status: 'In Progress' },
    { id: 6, title: 'Feature Request', status: 'Active' },
    { id: 7, title: 'Bug in Dashboard', status: 'Closed' },
    { id: 8, title: 'Login Issues', status: 'In Progress' },
    { id: 9, title: 'Profile Update Failed', status: 'Closed' },
    { id: 10, title: 'Support Ticket Not Responded', status: 'Active' },
  ];

  const filteredComplaints = complaints.filter((complaint) => (selectedTab === 'all' ? true : complaint.status.toLowerCase() === selectedTab));

  return (
    <div className='flex flex-col gap-4'>
      <HeaderSection />
      <div className='flex flex-col lg:flex-row items-start justify-start gap-4'>
        <div className='lg:w-3/4 grid grid-cols-1 gap-4 w-full'>
          {filteredComplaints.map((complaint) => (
            <ComplaintBox key={complaint.id} complaint={complaint} />
          ))}
        </div>
        <div className='lg:w-1/4 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-1 gap-4 items-start w-full'>
          <Categories />
          <Status />
        </div>
      </div>
    </div>
  );
};

const HeaderSection = () => (
  <div className='rounded-3xl bg-white p-6 flex flex-col md:flex-row justify-between items-center gap-4 lg:gap-0'>
    <div className='flex gap-2 items-start'>
      <Mail size={50} strokeWidth={1.25} />
      <div className='flex flex-col items-start'>
        <h2 className='md:text-xl font-semibold'>Complaints and Resolution</h2>
        <p className='text-gray-500 text-sm'>Make and track your complaints and get quick resolutions</p>
      </div>
    </div>
    <div className='flex gap-2'>
      <AlertDialog>
        <AlertDialogTrigger className='btn-default-sm'>
          <Plus size={24} /> <span>Add Complaint</span>
        </AlertDialogTrigger>
        <AlertDialogContent className='!w-[700px] !max-w-none'>
          <TutorComplaintForm />
          <AlertDialogCancel className='border-0 absolute rounded-none rounded-tr-md right-0 top-0 bg-primaryColor-50 hover:bg-primaryColor-100 flex-shrink-0 p-2 tracking-wider cursor-pointer group overflow-hidden'>
            <p className='text-primaryColor'>
              <X />
            </p>
          </AlertDialogCancel>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  </div>
);

const Categories = () => {
  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'bills':
        return <DollarSign size={20} strokeWidth={1.5} className='text-white' />;
      case 'education':
        return <BookOpen size={20} strokeWidth={1.5} className='text-white' />;
      case 'utility':
        return <Headphones size={20} strokeWidth={1.5} className='text-white' />;
      case 'shopping':
        return <ShoppingBag size={20} strokeWidth={1.5} className='text-white' />;
      default:
        return <MenuSquare size={20} strokeWidth={1.5} className='text-white' />;
    }
  };

  const complaintCategoryMapTwo = {
    bills: { label: 'Bills', complaints: 235, bgColor: 'bg-green-500' },
    education: { label: 'Education', complaints: 125, bgColor: 'bg-yellow-500' },
    utility: { label: 'Utility', complaints: 25, bgColor: 'bg-pink-500' },
    shopping: { label: 'Shopping', complaints: 25, bgColor: 'bg-purple-500' },
  };

  return (
    <div className='bg-white rounded-3xl p-6 flex flex-col gap-4 items-start justify-start w-full'>
      <h3 className='text-xl font-semibold'>Categories</h3>
      {Object.entries(complaintCategoryMapTwo).map(([key, value]) => (
        <div key={key} className='flex items-center gap-4 w-full border-b pb-3'>
          <div className={`flex items-center justify-center size-10 rounded-full ${value.bgColor}`}>{getCategoryIcon(key)}</div>
          <div className='flex flex-col'>
            <span className='font-semibold'>{value.label}</span>
            <span className='text-sm text-gray-500'>Ticket ({value.complaints})</span>
          </div>
        </div>
      ))}
    </div>
  );
};

const Status = () => {
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'Active':
        return <CheckCircle size={20} strokeWidth={1.5} className='text-white' />;
      case 'In Progress':
        return <Clock size={20} strokeWidth={1.5} className='text-white' />;
      case 'Closed':
        return <XCircle size={20} strokeWidth={1.5} className='text-white' />;
      default:
        return <MenuSquare size={20} strokeWidth={1.5} className='text-white' />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Active':
        return 'bg-green-500';
      case 'In Progress':
        return 'bg-yellow-500';
      case 'Closed':
        return 'bg-red-500';
      default:
        return 'bg-gray-500';
    }
  };

  return (
    <div className='flex flex-col gap-4 items-start justify-start w-full bg-white rounded-3xl p-6'>
      <h3 className='text-xl font-semibold'>Status</h3>
      {Object.entries(comaplaintStatusMap).map(([key, value]) => (
        <div key={key} className='flex items-center gap-4 w-full border-b pb-3'>
          <div className={`flex items-center justify-center size-10 rounded-full ${getStatusColor(value.label)}`}>{getStatusIcon(value.label)}</div>
          <div className='flex flex-col'>
            <span className='font-semibold'>{value.label}</span>
            <span className='text-sm text-gray-500'>Complaints ({Math.floor(Math.random() * 5)})</span>
          </div>
        </div>
      ))}
    </div>
  );
};

const ComplaintBox = ({ complaint }: { complaint: any }) => {
  const notifications = 4;

  const getStatusColor2 = (status: string) => {
    switch (status) {
      case 'Active':
        return 'text-green-500 bg-green-50';
      case 'In Progress':
        return 'text-yellow-500 bg-yellow-50';
      case 'Closed':
        return 'text-red-500 bg-red-50';
      default:
        return 'text-gray-500 bg-gray-50';
    }
  };

  return (
    <div className='rounded-3xl bg-white p-6 lg:min-h-24 flex flex-col justify-start items-start md:flex-row md:justify-between md:items-center'>
      <div className='flex gap-4 items-start'>
        <div className='bg-primaryColor-50 text-primaryColor size-14 flex items-center justify-center rounded'>
          <Wrench size={30} />
        </div>
        <div className='flex flex-col gap-2'>
          <div className='flex items-start gap-4'>
            <h3 className='text-base font-semibold'>{complaint.title}</h3>
            <span className={`px-4 py-0.5 rounded-full text-sm whitespace-pre ${getStatusColor2(complaint.status)}`}>{complaint.status}</span>
          </div>
          <p className='text-gray-400 text-[15px] uppercase'>{format(new Date(), 'dd MMM yyyy hh:mm a')}</p>
        </div>
      </div>
      <Link href='/tutor-dash/support/complaints/1' className='flex items-center max-md:justify-between max-md:w-full gap-4'>
        {notifications > 0 && (
          <button className='relative'>
            <MessageSquareText size={25} strokeWidth={1.5} className='text-primaryColor' />
            <span className='absolute -top-1 -right-1 bg-gradient-1 text-white rounded-full text-xs h-4 w-4 flex items-center justify-center'>
              {notifications}
            </span>
          </button>
        )}
        <button className='text-primaryColor'>
          <FileCog />
        </button>
      </Link>
    </div>
  );
};

export default TutorComplaintsPage;
