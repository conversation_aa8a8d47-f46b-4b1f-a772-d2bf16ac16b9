import { useQuery, useMutation } from '@tanstack/react-query';
import { QUERY_KEYS } from '@/lib/react-query/queryKeys';
import { queryClient } from '@/lib/react-query/queryClient';
import serviceCategoryService from '@/server/services/service-category.service';
import { CreateServiceCategoryInput, UpdateServiceCategoryInput } from '@/validation/schemas/service-category.schema';

// Service Categories Hooks
export function useGetAllServiceCategories(params?: Record<string, any>, options = {}) {
  return useQuery({
    queryKey: [QUERY_KEYS.EDUCATION.SERVICE_CATEGORIES, params],
    queryFn: () => serviceCategoryService.getAllServiceCategories(params),
    ...options,
  });
}

export function useGetServiceCategoryById(id: string, options = {}) {
  return useQuery({
    queryKey: [QUERY_KEYS.EDUCATION.SERVICE_CATEGORY, id],
    queryFn: () => serviceCategoryService.getServiceCategoryById(id),
    enabled: !!id,
    ...options,
  });
}

export function useCreateServiceCategory() {
  return useMutation({
    mutationFn: (data: CreateServiceCategoryInput) => serviceCategoryService.createServiceCategory(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.EDUCATION.SERVICE_CATEGORIES] });
    },
  });
}

export function useUpdateServiceCategory() {
  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateServiceCategoryInput }) => serviceCategoryService.updateServiceCategory(id, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.EDUCATION.SERVICE_CATEGORIES] });
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.EDUCATION.SERVICE_CATEGORY, variables.id] });
    },
  });
}
