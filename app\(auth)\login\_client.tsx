'use client';

import Image from 'next/image';
import leftImage from '@/public/temp/login.svg';
import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { UserTypeSelection } from '../user-type-selection';
import { LoginForms } from './login-forms';
import { StepIndicator } from '@/components/misc';
import type React from 'react';

const LoginPage: React.FC = () => {
  const [selectedUserType, setSelectedUserType] = useState<string>('');
  const [step, setStep] = useState<number>(1);

  const handleUserTypeChange = (userType: string) => {
    setSelectedUserType(userType);
  };

  const goToStep = (nextStep: number) => {
    setStep(nextStep);
  };

  const stepLabels = ['Select Type', 'Login'];

  return (
    <section className='relative overflow-hidden bg-gray-50 min-h-screen flex items-center justify-center'>
      <div className='max-container padding-container'>
        <div className='grid lg:grid-cols-2 items-center'>
          <div className='hidden lg:block h-full'>
            <Image src={leftImage || '/placeholder.svg'} alt='Login Page' className='object-cover w-full h-full' />
          </div>
          <div className='bg-white p-8 flex flex-col justify-center'>
            <div className='w-full'>
              <StepIndicator currentStep={step} totalSteps={2} labels={stepLabels} />
              <AnimatePresence mode='wait'>
                <motion.div
                  key={step}
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  transition={{ duration: 0.3 }}
                >
                  {step === 1 ? (
                    <UserTypeSelection onUserTypeChange={handleUserTypeChange} setStep={() => goToStep(2)} />
                  ) : (
                    <LoginForms selectedUserType={selectedUserType} goToStep={() => goToStep(1)} />
                  )}
                </motion.div>
              </AnimatePresence>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default LoginPage;
