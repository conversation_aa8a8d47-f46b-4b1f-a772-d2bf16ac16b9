import { Award, Calendar, Edit, FileText, GraduationCap, MapPin, School, Trash2, CheckCircle2 } from 'lucide-react';
import { IEducationDetailDocument } from '@/server/services/profile.service';
import { educationTypeMap, scoreTypeMap } from '@/validation/schemas/parent/education.maps';
import { formatDate } from '@/lib/date.utils';

interface EducationCardProps {
  education: IEducationDetailDocument;
  onEdit?: (education: IEducationDetailDocument) => void;
  onDelete?: () => void;
}

const EducationCard = ({ education, onEdit, onDelete }: EducationCardProps) => {
  const theme =
    education.educationType === educationTypeMap.school.key
      ? {
          primary: 'from-blue-500 to-blue-600',
          light: 'from-blue-50 to-blue-100',
          accent: 'bg-blue-500',
          text: 'text-blue-600',
          bg: 'bg-blue-50',
          border: 'border-blue-100',
          hover: 'hover:bg-blue-100',
          icon: 'text-blue-500',
        }
      : education.educationType === educationTypeMap.degree.key
      ? {
          primary: 'from-green-500 to-green-600',
          light: 'from-green-50 to-green-100',
          accent: 'bg-green-500',
          text: 'text-green-600',
          bg: 'bg-green-50',
          border: 'border-green-100',
          hover: 'hover:bg-green-100',
          icon: 'text-green-500',
        }
      : {
          primary: 'from-purple-500 to-purple-600',
          light: 'from-purple-50 to-purple-100',
          accent: 'bg-purple-500',
          text: 'text-purple-600',
          bg: 'bg-purple-50',
          border: 'border-purple-100',
          hover: 'hover:bg-purple-100',
          icon: 'text-purple-500',
        };

  const startDate = formatDate(education.startDate);
  const endDate = formatDate(education.endDate);

  return (
    <div className='group bg-white rounded-xl border border-gray-200 overflow-hidden shadow-sm hover:shadow-lg transition-all'>
      <div className={`h-1.5 bg-gradient-to-r ${theme.primary}`}></div>

      <div className='p-6'>
        <div className='flex justify-between items-start mb-5'>
          <div className='flex items-start gap-3'>
            <div className={`relative w-12 h-12 rounded-lg bg-gradient-to-br ${theme.light} p-0.5 shadow-sm`}>
              <div className={`absolute inset-0 rounded-lg bg-gradient-to-br ${theme.primary} opacity-10`}></div>
              <div className='w-full h-full rounded-lg bg-white flex items-center justify-center'>
                {education.educationType === educationTypeMap.school.key ? (
                  <School size={22} className={theme.text} />
                ) : education.educationType === educationTypeMap.degree.key ? (
                  <GraduationCap size={22} className={theme.text} />
                ) : (
                  <Award size={22} className={theme.text} />
                )}
              </div>
            </div>

            <div>
              <div className='flex items-center gap-2 mb-1'>
                <h3 className='font-bold text-gray-800'>
                  {education.educationType === educationTypeMap.school.key
                    ? education.classDetails?.name
                    : education.educationType === educationTypeMap.degree.key
                    ? education.degreeDetails?.name
                    : education.certificateName}
                </h3>
                <span className={`px-2 py-0.5 text-xs font-medium rounded-full ${theme.bg} ${theme.text}`}>
                  {education.educationType === educationTypeMap.school.key
                    ? education.boardDetails?.name
                    : education.educationType === educationTypeMap.degree.key
                    ? education.degreeLevelDetails?.name
                    : educationTypeMap.other.label}
                </span>
              </div>
              <p className='text-sm text-gray-600'>
                {education.businessLocationDetails?.name || education.educationType === educationTypeMap.school.key
                  ? education.schoolName
                  : education.educationType === educationTypeMap.degree.key
                  ? education.collegeName
                  : education.certificateBy}
              </p>
            </div>
          </div>

          <div className={`px-2 py-1 rounded-lg ${theme.bg} ${theme.text} text-xs font-medium flex items-center gap-1`}>
            <CheckCircle2 size={12} />
            <span>Verified</span>
          </div>
        </div>

        <div className='grid grid-cols-1 md:grid-cols-2 gap-3 mb-4'>
          <div className='flex items-center gap-2.5 group/item'>
            <div className={`w-8 h-8 rounded-lg ${theme.bg} flex items-center justify-center group-hover/item:${theme.accent} transition-colors`}>
              <MapPin size={16} className={`${theme.text} transition-colors`} />
            </div>
            <div>
              <p className='text-xs text-gray-500'>Location</p>
              <p className='text-sm font-medium text-gray-800'>{education.businessLocationDetails?.location || education.location}</p>
            </div>
          </div>

          <div className='flex items-center gap-2.5 group/item'>
            <div className={`w-8 h-8 rounded-lg ${theme.bg} flex items-center justify-center group-hover/item:${theme.accent} transition-colors`}>
              <Calendar size={16} className={`${theme.text} transition-colors`} />
            </div>
            <div>
              <p className='text-xs text-gray-500'>Duration</p>
              <p className='text-sm font-medium text-gray-800'>
                {startDate} - {endDate}
              </p>
            </div>
          </div>

          <div className='flex items-center gap-2.5 group/item'>
            <div className={`w-8 h-8 rounded-lg ${theme.bg} flex items-center justify-center group-hover/item:${theme.accent} transition-colors`}>
              <Award size={16} className={`${theme.icon} transition-colors`} />
            </div>
            <div>
              <p className='text-xs text-gray-500'>{education.scoreType}</p>
              <div className='flex items-center gap-2'>
                <p className='text-sm font-medium text-gray-800'>
                  {education.obtainedValue}
                  {education.maximumValue && <span className='text-gray-500'> / {education.maximumValue}</span>}
                </p>

                {education.scoreType === scoreTypeMap.percentage.key && (
                  <div className='w-16 h-1.5 bg-gray-100 rounded-full overflow-hidden'>
                    <div
                      className={theme.accent}
                      style={{
                        width: `${Math.min(parseInt(education.obtainedValue?.toString() || '0'), 100)}%`,
                        height: '100%',
                      }}
                    ></div>
                  </div>
                )}
              </div>
            </div>
          </div>

          {education.certificateNumber && (
            <div className='flex items-center gap-2.5 group/item'>
              <div className={`w-8 h-8 rounded-lg ${theme.bg} flex items-center justify-center group-hover/item:${theme.accent} transition-colors`}>
                <FileText size={16} className={`${theme.icon} transition-colors`} />
              </div>
              <div>
                <p className='text-xs text-gray-500'>Certificate</p>
                <p className='text-sm font-medium text-gray-800'>{education.certificateNumber}</p>
              </div>
            </div>
          )}
        </div>

        <div className='mt-4 pt-4 border-t border-gray-100 flex justify-between items-center'>
          {education.attachmentUrl ? (
            <a
              href={education.attachmentUrl}
              className={`flex items-center gap-1.5 px-3 py-1.5 rounded-lg text-xs font-medium ${theme.text} ${theme.bg} ${theme.hover} transition-colors`}
              target='_blank'
              rel='noopener noreferrer'
            >
              <FileText size={14} />
              <span>View Certificate</span>
            </a>
          ) : (
            <span className={`flex items-center gap-1.5 px-3 py-1.5 rounded-lg text-xs font-medium text-gray-500 bg-gray-100 transition-colors`}>
              <FileText size={14} />
              <span>No Certificate</span>
            </span>
          )}

          {(onEdit || onDelete) && (
            <div className='flex gap-2'>
              {onEdit && (
                <button
                  onClick={() => onEdit(education)}
                  className='flex items-center gap-1.5 px-3 py-1.5 text-blue-600 bg-blue-50 hover:bg-blue-100 rounded-lg text-xs font-medium transition-colors'
                >
                  <Edit size={14} />
                  <span>Edit</span>
                </button>
              )}
              {onDelete && (
                <button
                  onClick={onDelete}
                  className='flex items-center gap-1.5 px-3 py-1.5 text-red-600 bg-red-50 hover:bg-red-100 rounded-lg text-xs font-medium transition-colors'
                >
                  <Trash2 size={14} />
                  <span>Delete</span>
                </button>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default EducationCard;
