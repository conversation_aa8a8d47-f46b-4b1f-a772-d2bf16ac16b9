import type React from 'react';
import { useState } from 'react';
import { FormField, FormItem, FormControl, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Eye, EyeOff } from 'lucide-react';
import type { UseFormReturn, FieldValues, Path } from 'react-hook-form';
import { inputClassName } from './form.constants';

interface PrimaryInputProps<TFieldValues extends FieldValues> {
  form: UseFormReturn<TFieldValues>;
  name: Path<TFieldValues>;
  label?: string;
  placeholder?: string;
  required?: boolean;
  type?: string;
  onFocus?: () => void;
  disabled?: boolean;
  variant?: 'primary' | 'secondary' | 'green' | 'purple';
}

const PrimaryInput = <TFieldValues extends FieldValues>({
  form,
  name,
  label,
  placeholder,
  required = false,
  type = 'text',
  onFocus,
  disabled = false,
  variant = 'primary',
}: PrimaryInputProps<TFieldValues>) => {
  const [showPassword, setShowPassword] = useState(false);

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => (
        <FormItem className='w-full'>
          {label && (
            <FormLabel className='primary-label'>
              <span>{label}</span>
              {required && <span className='text-primaryColor'>*</span>}
            </FormLabel>
          )}
          <FormControl>
            <div className='relative'>
              <Input
                className={inputClassName[variant]}
                type={type === 'password' && showPassword ? 'text' : ['number', 'email'].includes(type) ? 'text' : type}
                placeholder={placeholder}
                {...field}
                onChange={(e) => {
                  const value =
                    type === 'number' ? (Number.isNaN(Number.parseFloat(e.target.value)) ? 0 : Number.parseFloat(e.target.value)) : e.target.value;
                  field.onChange(value);
                }}
                onFocus={onFocus}
                disabled={disabled}
              />
              {type === 'password' && (
                <button type='button' className='absolute inset-y-0 right-0 pr-3 flex items-center' onClick={togglePasswordVisibility}>
                  {showPassword ? <EyeOff className='h-5 w-5 text-gray-400' /> : <Eye className='h-5 w-5 text-gray-400' />}
                </button>
              )}
            </div>
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
};

export default PrimaryInput;
