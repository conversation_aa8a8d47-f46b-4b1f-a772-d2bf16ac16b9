import { useQuery, useMutation } from '@tanstack/react-query';
import { QUERY_KEYS } from '@/lib/react-query/queryKeys';
import { queryClient } from '@/lib/react-query/queryClient';

import { examCategoryService, examService, examSubjectService } from '@/server/services/education/exam.service';
import {
  CreateExamCategoryInput,
  UpdateExamCategoryInput,
  CreateExamInput,
  UpdateExamInput,
  CreateExamSubjectInput,
  UpdateExamSubjectInput,
} from '@/validation/schemas/education/exam.schema';

const examEducationService = {
  examCategories: examCategoryService,
  exams: examService,
  examSubjects: examSubjectService,
};

// Exam Category Hooks
export function useGetAllExamCategories(params?: Record<string, any>, options = {}) {
  return useQuery({
    queryKey: [QUERY_KEYS.EDUCATION.EXAM_CATEGORIES, params],
    queryFn: () => examEducationService.examCategories.getAllExamCategories(params),
    ...options,
  });
}

export function useGetExamCategoryById(id: string, options = {}) {
  return useQuery({
    queryKey: [QUERY_KEYS.EDUCATION.EXAM_CATEGORY, id],
    queryFn: () => examEducationService.examCategories.getExamCategoryById(id),
    enabled: !!id,
    ...options,
  });
}

export function useCreateExamCategory() {
  return useMutation({
    mutationFn: (data: CreateExamCategoryInput) => examEducationService.examCategories.createExamCategory(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.EDUCATION.EXAM_CATEGORIES] });
    },
  });
}

export function useUpdateExamCategory() {
  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateExamCategoryInput }) => examEducationService.examCategories.updateExamCategory(id, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.EDUCATION.EXAM_CATEGORIES] });
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.EDUCATION.EXAM_CATEGORY, variables.id] });
    },
  });
}

export function useDeleteExamCategory() {
  return useMutation({
    mutationFn: (id: string) => examEducationService.examCategories.deleteExamCategory(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.EDUCATION.EXAM_CATEGORIES] });
    },
  });
}

// Exam Hooks
export function useGetAllExams(params?: Record<string, any>, options = {}) {
  return useQuery({
    queryKey: [QUERY_KEYS.EDUCATION.EXAMS, params],
    queryFn: () => examEducationService.exams.getAllExams(params),
    ...options,
  });
}

export function useGetExamById(id: string, options = {}) {
  return useQuery({
    queryKey: [QUERY_KEYS.EDUCATION.EXAM, id],
    queryFn: () => examEducationService.exams.getExamById(id),
    enabled: !!id,
    ...options,
  });
}

export function useCreateExam() {
  return useMutation({
    mutationFn: (data: CreateExamInput) => examEducationService.exams.createExam(data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.EDUCATION.EXAMS] });
    },
  });
}

export function useUpdateExam() {
  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateExamInput }) => examEducationService.exams.updateExam(id, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.EDUCATION.EXAMS] });
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.EDUCATION.EXAM, variables.id] });
    },
  });
}

export function useDeleteExam() {
  return useMutation({
    mutationFn: (id: string) => examEducationService.exams.deleteExam(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.EDUCATION.EXAMS] });
    },
  });
}

// Exam Subject Hooks
export function useGetAllExamSubjects(params?: Record<string, any>, options = {}) {
  return useQuery({
    queryKey: [QUERY_KEYS.EDUCATION.EXAM_SUBJECTS, params],
    queryFn: () => examEducationService.examSubjects.getAllExamSubjects(params),
    ...options,
  });
}

export function useGetExamSubjectById(id: string, options = {}) {
  return useQuery({
    queryKey: [QUERY_KEYS.EDUCATION.EXAM_SUBJECT, id],
    queryFn: () => examEducationService.examSubjects.getExamSubjectById(id),
    enabled: !!id,
    ...options,
  });
}

export function useCreateExamSubject() {
  return useMutation({
    mutationFn: (data: CreateExamSubjectInput) => examEducationService.examSubjects.createExamSubject(data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.EDUCATION.EXAM_SUBJECTS] });
    },
  });
}

export function useUpdateExamSubject() {
  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateExamSubjectInput }) => examEducationService.examSubjects.updateExamSubject(id, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.EDUCATION.EXAM_SUBJECTS] });
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.EDUCATION.EXAM_SUBJECT, variables.id] });
    },
  });
}

export function useDeleteExamSubject() {
  return useMutation({
    mutationFn: (id: string) => examEducationService.examSubjects.deleteExamSubject(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.EDUCATION.EXAM_SUBJECTS] });
    },
  });
}
