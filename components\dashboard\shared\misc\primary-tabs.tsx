import React from 'react';
import { LucideIcon } from 'lucide-react';
import { cn } from '@/lib/utils';

const variantMap = {
  primary: {
    activeBg: 'bg-primaryColor-500',
    inactiveBg: 'bg-primaryColor-50',
    icon: 'text-primaryColor-500',
    activeIcon: 'text-white',
    badge: 'bg-primaryColor-100 text-primaryColor-700',
    activeBadge: 'bg-white text-primaryColor-600',
    text: 'text-primaryColor-700',
    activeText: 'text-white',
    inactiveIconBg: 'bg-primaryColor-100',
    inactiveBorder: 'border-primaryColor-200',
  },
  secondary: {
    activeBg: 'bg-gray-700',
    inactiveBg: 'bg-gray-100',
    icon: 'text-gray-700',
    activeIcon: 'text-white',
    badge: 'bg-gray-200 text-gray-700',
    activeBadge: 'bg-white text-gray-700',
    text: 'text-gray-700',
    activeText: 'text-white',
    inactiveIconBg: 'bg-gray-200',
    inactiveBorder: 'border-gray-200',
  },
  blue: {
    activeBg: 'bg-blue-500',
    inactiveBg: 'bg-blue-50',
    icon: 'text-blue-500',
    activeIcon: 'text-white',
    badge: 'bg-blue-100 text-blue-700',
    activeBadge: 'bg-white text-blue-600',
    text: 'text-blue-700',
    activeText: 'text-white',
    inactiveIconBg: 'bg-blue-100',
    inactiveBorder: 'border-blue-200',
  },
  green: {
    activeBg: 'bg-green-500',
    inactiveBg: 'bg-green-50',
    icon: 'text-green-500',
    activeIcon: 'text-white',
    badge: 'bg-green-100 text-green-700',
    activeBadge: 'bg-white text-green-600',
    text: 'text-green-700',
    activeText: 'text-white',
    inactiveIconBg: 'bg-green-100',
    inactiveBorder: 'border-green-200',
  },
  purple: {
    activeBg: 'bg-purple-500',
    inactiveBg: 'bg-purple-50',
    icon: 'text-purple-500',
    activeIcon: 'text-white',
    badge: 'bg-purple-100 text-purple-700',
    activeBadge: 'bg-white text-purple-600',
    text: 'text-purple-700',
    activeText: 'text-white',
    inactiveIconBg: 'bg-purple-100',
    inactiveBorder: 'border-purple-200',
  },
};

export type TabItem = {
  id: string;
  label: string;
  icon: LucideIcon;
  description?: string;
  variant?: keyof typeof variantMap;
  count?: number;
};

interface PrimaryTabsProps {
  tabs: TabItem[];
  selectedTab: string;
  setSelectedTab: (tab: string) => void;
  defaultVariant?: keyof typeof variantMap;
}

const PrimaryTabs: React.FC<PrimaryTabsProps> = ({ tabs, selectedTab, setSelectedTab, defaultVariant = 'primary' }) => {
  return (
    <div className='space-y-3'>
      {tabs.map((tab) => {
        const isActive = selectedTab === tab.id;
        const variant = tab.variant || defaultVariant;
        const color = variantMap[variant] || variantMap.primary;
        const Icon = tab.icon;
        const count = typeof tab.count === 'number' ? tab.count : undefined;
        return (
          <button
            key={tab.id}
            onClick={() => setSelectedTab(tab.id)}
            className={cn(
              'w-full p-4 rounded-xl border transition-all duration-300 text-left flex items-center justify-between',
              isActive
                ? `${color.activeBg} border-transparent shadow-md scale-[1.01]`
                : `${color.inactiveBg} ${color.inactiveBorder} hover:bg-opacity-80 hover:shadow-sm`
            )}
          >
            <div className='flex items-center gap-3'>
              <div className={cn('w-10 h-10 rounded-lg flex items-center justify-center', isActive ? 'bg-white/20' : color.inactiveIconBg)}>
                <Icon size={20} className={cn(isActive ? color.activeIcon : color.icon)} />
              </div>
              <div>
                <h3 className={cn('font-semibold text-sm', isActive ? color.activeText : color.text)}>{tab.label}</h3>
                {tab.description && <p className={cn('text-xs', isActive ? 'text-white/80' : 'text-gray-500')}>{tab.description}</p>}
              </div>
            </div>
            {typeof count === 'number' && (
              <span className={cn('ml-2 px-2 py-0.5 rounded-full text-xs font-medium', isActive ? color.activeBadge : color.badge)}>{count}</span>
            )}
          </button>
        );
      })}
    </div>
  );
};

export default PrimaryTabs;
