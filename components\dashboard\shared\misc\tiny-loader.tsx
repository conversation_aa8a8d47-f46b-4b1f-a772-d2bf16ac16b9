'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { User } from 'lucide-react';
import { cn } from '@/lib/utils';

interface TinyLoaderProps {
  userType?: 'parent' | 'tutor' | 'school' | 'college' | 'admin';
  message?: string;
  className?: string;
  fullScreen?: boolean;
}

const TinyLoader: React.FC<TinyLoaderProps> = ({ userType = 'parent', message = 'Welcome', className, fullScreen = false }) => {
  const colorMap = {
    parent: 'bg-red-500',
    tutor: 'bg-blue-500',
    school: 'bg-green-500',
    college: 'bg-purple-500',
    admin: 'bg-amber-500',
  };

  const textColorMap = {
    parent: 'text-red-500',
    tutor: 'text-blue-500',
    school: 'text-green-500',
    college: 'text-purple-500',
    admin: 'text-amber-500',
  };

  const bgColor = colorMap[userType];
  const textColor = textColorMap[userType];

  return (
    <div
      className={cn('flex flex-col items-center justify-center', fullScreen ? 'fixed inset-0 z-50 bg-white' : 'min-h-[400px] bg-white', className)}
    >
      <div className='relative w-16 h-16'>
        <motion.div className='absolute inset-0 rounded-full border-2 border-gray-100' />

        <motion.div
          className={`absolute inset-0 rounded-full border-2 border-transparent ${textColor}`}
          style={{
            borderTopColor: 'currentColor',
            borderRightColor: 'transparent',
            borderBottomColor: 'transparent',
            borderLeftColor: 'transparent',
          }}
          animate={{ rotate: 360 }}
          transition={{
            duration: 1,
            repeat: Infinity,
            ease: 'linear',
          }}
        />

        <div className={`absolute inset-0 flex items-center justify-center`}>
          <div className={`w-10 h-10 rounded-full ${bgColor} flex items-center justify-center`}>
            <User className='text-white' size={20} />
          </div>
        </div>
      </div>

      <div className='flex mt-3 mb-2'>
        {[0, 1, 2].map((i) => (
          <motion.span
            key={i}
            className={textColor}
            animate={{
              opacity: [0.3, 1, 0.3],
            }}
            transition={{
              duration: 1.5,
              repeat: Infinity,
              delay: i * 0.3,
              ease: 'easeInOut',
            }}
          >
            •
          </motion.span>
        ))}
      </div>

      <h3 className={`text-lg font-semibold ${textColor}`}>{message}</h3>
    </div>
  );
};

export default TinyLoader;
