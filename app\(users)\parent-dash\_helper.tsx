'use client';

import { <PERSON><PERSON><PERSON>, <PERSON>es, Eye, HandCoins, TriangleAlert, Users } from 'lucide-react';

import { cn } from '@/lib/utils';
import Link from 'next/link';
import { MobileLeadItemCard, SimpleTable } from '@/components/dashboard/tutor-dash/misc';

import { Swiper, SwiperSlide } from 'swiper/react';
import { Autoplay } from 'swiper/modules';
import 'swiper/css';
import 'swiper/css/autoplay';

import { ParentDashActionLinks } from '@/components/dashboard/parent-dash/misc';
import { leads } from '@/constants';

const headers = ['Name', 'Enquiry For', 'Location', 'Created Date', 'Action'];

const rows = leads.map((lead, index) => [
  <div>
    <p>{lead.name}</p>
    <p>{lead.id}000</p>
  </div>,
  <div>
    <p>{lead.enquiryFor}</p>
    <p>{['B.Sc', 'M.Sc'].includes(lead.enquiryFor) ? 'Computer' : 'CBSE'}</p>
  </div>,
  lead.address,
  '06 Jan 2021',
  <ParentDashActionLinks basePath='leads' id={lead.id.toString()} view />,
]);

interface IMetricsCard {
  title: string;
  value: string;
  progress: string;
}

interface RevenueCardProps {
  title: string;
  value: string;
  progress: string;
}

interface IDashboardSlideCard {
  icon: React.ReactNode;
  label: string;
  value: number;
  bgColor: string;
  iconBg: string;
}

const metricsCardData: IMetricsCard[] = [
  { title: 'Profile', value: '99%', progress: '+99%' },
  { title: 'KYC', value: '98%', progress: '+98%' },
  { title: 'Education Profile', value: '97%', progress: '+97%' },
];

const dashboardSlidesData = [
  {
    icon: <Aperture strokeWidth={1.5} className='text-amber-500 size-6  md:size-8' />,
    iconBg: 'bg-amber-50',
    label: 'Tuition Enquiries',
    value: 20,
    bgColor: 'bg-amber-100',
  },
  {
    icon: <Boxes strokeWidth={1.5} className='text-purple-500 size-6  md:size-8' />,
    iconBg: 'bg-purple-50',
    label: 'Batches',
    value: 15,
    bgColor: 'bg-purple-100',
  },
  {
    icon: <Eye strokeWidth={1.5} className='text-teal-500 size-6  md:size-8' />,
    iconBg: 'bg-teal-50',
    label: 'Admission Enq',
    value: 10,
    bgColor: 'bg-teal-100',
  },
  {
    icon: <Users strokeWidth={1.5} className='text-pink-500 size-6  md:size-8' />,
    iconBg: 'bg-pink-50',
    label: 'Complaints',
    value: 5,
    bgColor: 'bg-pink-100',
  },
];

export const PromotionalSlides = () => (
  <Swiper
    className='h-80 w-full'
    modules={[Autoplay]}
    slidesPerView={1}
    autoplay={{ delay: 1500, disableOnInteraction: false }}
    breakpoints={{
      768: {
        slidesPerView: 2,
        spaceBetween: 20,
      },
      1024: {
        slidesPerView: 3,
        spaceBetween: 30,
      },
      1280: {
        slidesPerView: 1,
        spaceBetween: 20,
      },
    }}
  >
    {Array.from({ length: 4 }).map((_, index) => (
      <SwiperSlide key={index}>
        <div className='bg-gradient-1 h-80 rounded-3xl flex items-center justify-center text-white'>Slide {index + 1}</div>
      </SwiperSlide>
    ))}
  </Swiper>
);

export const WalletOverview = () => (
  <div className='relative w-full h-56'>
    <div className='absolute top-0 left-0 w-full h-56 rounded-lg bg-gradient-1 shadow-lg transform'></div>
    <div
      className='relative w-[95%] h-56 rounded-lg transform -rotate-6'
      style={{
        backgroundColor: '#00000000',
        borderRadius: '1rem',
        backdropFilter: 'blur(50px)',
        boxShadow: '10px 10px 20px rgba(0, 0, 0, 0.3)',
      }}
    >
      <div className='p-6 text-white'>
        <div className='text-sm'>Total Due Balance</div>
        <div className='text-2xl font-semibold flex gap-2 items-center'>
          <HandCoins />
          <span>630</span>
        </div>
        <div className='absolute bottom-4 left-6'>
          <p className='text-xs'>Your Wallet Balance</p>
          <p className='text-sm'>26,000</p>
        </div>
        <div className='absolute bottom-4 right-6'>
          <p className='text-xs'>Due Since</p>
          <p className='text-sm'>26 Dec 2021</p>
        </div>
        <div className='absolute top-4 right-6'>
          <img src='/temp/master-card.png' alt='MasterCard Logo' className='w-12 h-auto' />
        </div>
      </div>
    </div>
  </div>
);

export const AppDownloadCard = () => (
  <div className='p-6 bg-white rounded-3xl flex flex-col items-center gap-y-4'>
    <div className='flex items-start gap-x-4'>
      <div className='size-20 bg-gray-200 rounded' />
      <div>
        <h2 className='text-base font-semibold'>Scan to Download</h2>
        <p className='text-gray-500 text-sm'>Cashfree Payments App</p>
      </div>
    </div>
    <div className='flex justify-between mt-2 w-full'>
      <p>Google Play</p>
      <p>App Store</p>
    </div>
    <div className='w-full border-t border-gray-200 pt-2'>
      <p className='text-2xl font-semibold text-primaryColor'>4K+</p>
      <p className='text-gray-600'>Users are already using perfect tutor for their education needs!</p>
    </div>
  </div>
);

export const DashboardMetrics = () => (
  <div className='grid grid-cols-1 gap-4 md:grid-cols-2'>
    {metricsCardData.map((card, index) => (
      <MetricCard key={index} title={card.title} value={card.value} progress={card.progress} />
    ))}
  </div>
);

export const DashboardTopCard = () => (
  <div className='p-6 bg-white rounded-3xl flex flex-col md:flex-row justify-between items-start lg:items-center'>
    <div className='flex flex-col items-start shrink-0'>
      <h1 className='text-xl font-bold mb-2'>Hello, Rahul 👋</h1>
      <p className='text-sm text-gray-600'>
        User Id: <span className='font-medium'>1545</span>
      </p>
      <p className='text-sm text-gray-600'>
        Account Status <span className='bg-green-50 text-green-600 py-1.5 px-5 rounded-3xl'>Active</span>
      </p>
    </div>
    <div className='mt-4 xl:mt-0 xl:ml-auto w-full max-w-[450px] xl:max-w-[400px] 2xl:max-w-[450px]'>
      <Swiper
        className='max-w-full'
        modules={[Autoplay]}
        spaceBetween={20}
        slidesPerView={1}
        slidesPerGroup={2}
        autoplay={{ delay: 2000, disableOnInteraction: false }}
        breakpoints={{
          390: {
            slidesPerView: 2,
            spaceBetween: 10,
          },
          768: {
            slidesPerView: 2,
            spaceBetween: 20,
          },
        }}
      >
        {dashboardSlidesData.map((card, index) => (
          <SwiperSlide key={index}>
            <DashboardSlideCard {...card} />
          </SwiperSlide>
        ))}
      </Swiper>
    </div>
  </div>
);

export const RecentLeads = () => {
  return (
    <div className='bg-white rounded-3xl p-6'>
      <div className='flex justify-between items-center'>
        <h2 className='text-lg font-semibold mb-4'>Your Tuition Enquiries</h2>
        <Link href='/tutor-dash/leads' className='btn-default-sm animate-bounce bg-gradient-1'>
          View All Leads
        </Link>
      </div>
      <div className='flex flex-col gap-4 md:hidden'>
        {leads.map((lead, index) => (
          <MobileLeadItemCard key={index} lead={lead} />
        ))}
      </div>
      <div className='max-md:hidden'>
        <SimpleTable headers={headers} rows={rows} />
      </div>
    </div>
  );
};

const MetricCard: React.FC<RevenueCardProps> = ({ title, value, progress }) => (
  <div className='p-6 bg-white rounded-3xl flex justify-between items-center'>
    <div>
      <h2 className='text-xl font-semibold'>{title}</h2>
      <p className='text-gray-500 text-sm flex items-center gap-2'>
        <TriangleAlert size={14} />
        <span>Lorem ipsum dolor sit amet.</span>
      </p>
    </div>
    <div className='relative'>
      <div className='size-16 rounded-full bg-gradient-1 flex items-center justify-center text-white'>
        <span className='text-base font-semibold'>{progress}</span>
      </div>
      <div className='absolute top-0 left-0 w-16 h-16 rounded-full border-4 border-white'></div>
    </div>
  </div>
);

const DashboardSlideCard: React.FC<IDashboardSlideCard> = ({ icon, label, value, bgColor, iconBg }) => (
  <div className={`flex items-center ${bgColor} rounded-lg p-3`}>
    <div className={cn(iconBg, 'p-3 rounded-full')}>{icon}</div>
    <div className='ml-4'>
      <p className={cn('text-gray-700', ['Admission Enq', 'Tuition Enquiries'].includes(label) ? 'text-xs' : 'text-xs sm:text-sm md:text-base')}>
        {label}
      </p>
      <p className='text-xl md:text-2xl font-semibold'>{value}</p>
    </div>
  </div>
);
