'use client';

import { BookA, MapPin, Calendar, School, Users } from 'lucide-react';
import { IEnquiryDocument } from '@/server/services/enquiry.service';
import { serviceCategoryMap } from '@/validation/schemas/education/index.maps';
import { formatDate } from '@/lib/date.utils';
import { getSubjectDisplay, statusThemes } from './utils';

interface LeadCardProps {
  enquiry: IEnquiryDocument;
  onView: (enquiry: IEnquiryDocument) => void;
  onClose: (enquiry: IEnquiryDocument) => void;
}

const LeadCard = ({ enquiry, onView, onClose }: LeadCardProps) => {
  const uiStatus = enquiry.isActive ? 'active' : 'closed';
  const theme = statusThemes[uiStatus];
  const categoryDisplay = serviceCategoryMap[enquiry.category]?.label || 'Unknown';
  const subjectDisplay = getSubjectDisplay(enquiry);
  const studentName = enquiry.childProfileDetails?.fullName || 'Unknown Student';
  const boardName = enquiry.boardDetails?.name;
  const className = enquiry.classDetails?.name;

  return (
    <div className='group bg-white rounded-xl border border-gray-200 overflow-hidden shadow-sm hover:shadow-lg transition-all'>
      <div className={`h-1.5 bg-gradient-to-r ${theme.primary}`}></div>

      <div className='p-6'>
        <div className='flex justify-between items-start mb-5'>
          <div className='flex items-start gap-3'>
            <div className={`relative w-12 h-12 rounded-lg bg-gradient-to-br ${theme.light} p-0.5 shadow-sm`}>
              <div className={`absolute inset-0 rounded-lg bg-gradient-to-br ${theme.primary} opacity-10`}></div>
              <div className='w-full h-full rounded-lg bg-white flex items-center justify-center'>
                <School size={22} className={theme.text} />
              </div>
            </div>

            <div>
              <div className='flex items-center gap-2 mb-1'>
                <h3 className='font-bold text-gray-800'>{studentName}</h3>
                <span className={`px-2 py-0.5 text-xs font-medium rounded-full ${theme.bg} ${theme.text}`}>ID: {enquiry._id.substring(0, 6)}</span>
              </div>
              <p className='text-sm text-gray-600'>
                {categoryDisplay}
                {enquiry.category === 'schools' && boardName && className && (
                  <span className='ml-1 text-gray-500'>
                    • {boardName} • {className}
                  </span>
                )}
              </p>
            </div>
          </div>

          <div className={`px-2 py-1 rounded-lg ${theme.bg} ${theme.text} text-xs font-medium flex items-center gap-1`}>
            <span>{theme.label}</span>
          </div>
        </div>

        <div className='grid grid-cols-1 md:grid-cols-2 gap-3 mb-4'>
          <div className='flex items-center gap-2.5 group/item'>
            <div className={`w-8 h-8 rounded-lg ${theme.bg} flex items-center justify-center group-hover/item:${theme.accent} transition-colors`}>
              <MapPin size={16} className={`${theme.text} transition-colors`} />
            </div>
            <div>
              <p className='text-xs text-gray-500'>Location</p>
              <p className='text-sm font-medium text-gray-800 truncate max-w-[150px]'>{enquiry.location.address}</p>
            </div>
          </div>

          <div className='flex items-center gap-2.5 group/item'>
            <div className={`w-8 h-8 rounded-lg ${theme.bg} flex items-center justify-center group-hover/item:${theme.accent} transition-colors`}>
              <Calendar size={16} className={`${theme.text} transition-colors`} />
            </div>
            <div>
              <p className='text-xs text-gray-500'>Date</p>
              <p className='text-sm font-medium text-gray-800'>{formatDate(enquiry.createdAt, 'PPP')}</p>
            </div>
          </div>

          <div className='flex items-center gap-2.5 group/item'>
            <div className={`w-8 h-8 rounded-lg ${theme.bg} flex items-center justify-center group-hover/item:${theme.accent} transition-colors`}>
              <BookA size={16} className={`${theme.icon} transition-colors`} />
            </div>
            <div>
              <p className='text-xs text-gray-500'>Subjects</p>
              <p className='text-sm font-medium text-gray-800 truncate max-w-[150px]'>{enquiry.allSubjects ? 'All Subjects' : subjectDisplay}</p>
            </div>
          </div>

          <div className='flex items-center gap-2.5 group/item'>
            <div className={`w-8 h-8 rounded-lg ${theme.bg} flex items-center justify-center group-hover/item:${theme.accent} transition-colors`}>
              <Users size={16} className={`${theme.icon} transition-colors`} />
            </div>
            <div>
              <p className='text-xs text-gray-500'>Classes/Week</p>
              <p className='text-sm font-medium text-gray-800'>{enquiry.classesPerWeek}</p>
            </div>
          </div>
        </div>

        <div className='mt-4 pt-4 border-t border-gray-100 flex justify-end items-center gap-2'>
          <button
            onClick={() => onView(enquiry)}
            className='flex items-center gap-1.5 px-3 py-1.5 text-blue-600 bg-blue-50 hover:bg-blue-100 rounded-lg text-xs font-medium transition-colors'
          >
            View Enquiry
          </button>

          {enquiry.isActive === true && (
            <button
              onClick={() => onClose(enquiry)}
              className='flex items-center gap-1.5 px-3 py-1.5 text-red-600 bg-red-50 hover:bg-red-100 rounded-lg text-xs font-medium transition-colors'
            >
              Close Enquiry
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default LeadCard;
