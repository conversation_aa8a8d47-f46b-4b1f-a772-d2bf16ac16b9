import type React from 'react';
import { cn } from '@/lib/utils';
import { cva, type VariantProps } from 'class-variance-authority';
import { Loader2, Loader, Search } from 'lucide-react';
import { motion } from 'framer-motion';

const sectionLoaderVariants = cva('relative flex flex-col items-center justify-center overflow-hidden transition-all', {
  variants: {
    variant: {
      default: 'bg-white/80 backdrop-blur-sm',
      glass: 'bg-white/40 backdrop-blur-md',
      gradient: 'bg-gradient-to-b from-white via-white/90 to-white/80 backdrop-blur-sm',
      minimal: 'bg-transparent',
    },
    size: {
      sm: 'p-4 min-h-[200px]',
      md: 'p-6 min-h-[300px]',
      lg: 'p-8 min-h-[400px]',
      full: 'p-8 min-h-[600px]',
      auto: 'p-6',
    },
  },
  defaultVariants: {
    variant: 'default',
    size: 'md',
  },
});

interface LoaderProps extends React.HTMLAttributes<HTMLDivElement>, VariantProps<typeof sectionLoaderVariants> {
  title?: string;
  message?: string;
  loaderType?: 'spinner' | 'dots' | 'skeleton' | 'progress';
  accentColor?: 'purple' | 'red' | 'blue' | 'green' | 'amber' | 'pink' | 'gradient';
}

function VisitorsLoader({ title, message, variant, size, className, loaderType = 'dots', accentColor = 'gradient', ...props }: LoaderProps) {
  const defaultMessage = title ? `Loading ${title} section...` : '';

  const displayMessage = message || defaultMessage;

  const colorMap = {
    purple: {
      primary: 'bg-purple-600',
      secondary: 'bg-purple-400',
      light: 'bg-purple-200',
      text: 'text-purple-600',
      gradient: 'from-purple-600 to-purple-400',
    },
    red: {
      primary: 'bg-red-600',
      secondary: 'bg-red-400',
      light: 'bg-red-200',
      text: 'text-red-600',
      gradient: 'from-red-600 to-red-400',
    },
    blue: {
      primary: 'bg-blue-600',
      secondary: 'bg-blue-400',
      light: 'bg-blue-200',
      text: 'text-blue-600',
      gradient: 'from-blue-600 to-blue-400',
    },
    green: {
      primary: 'bg-green-600',
      secondary: 'bg-green-400',
      light: 'bg-green-200',
      text: 'text-green-600',
      gradient: 'from-green-600 to-green-400',
    },
    amber: {
      primary: 'bg-amber-600',
      secondary: 'bg-amber-400',
      light: 'bg-amber-200',
      text: 'text-amber-600',
      gradient: 'from-amber-600 to-amber-400',
    },
    pink: {
      primary: 'bg-pink-600',
      secondary: 'bg-pink-400',
      light: 'bg-pink-200',
      text: 'text-pink-600',
      gradient: 'from-pink-600 to-pink-400',
    },
    gradient: {
      primary: 'bg-gradient-to-r from-purple-600 to-red-500',
      secondary: 'bg-gradient-to-r from-purple-400 to-red-300',
      light: 'bg-gradient-to-r from-purple-200 to-red-100',
      text: 'text-transparent bg-clip-text bg-gradient-to-r from-purple-600 to-red-500',
      gradient: 'from-purple-600 to-red-500',
    },
  };

  const colors = colorMap[accentColor];

  const renderLoader = () => {
    switch (loaderType) {
      case 'dots':
        return (
          <div className='flex space-x-2'>
            {[0, 1, 2].map((i) => (
              <div key={i} className={`w-3 h-3 rounded-full ${colors.primary} animate-bounce`} style={{ animationDelay: `${i * 0.15}s` }} />
            ))}
          </div>
        );

      case 'skeleton':
        return (
          <div className='w-full max-w-md space-y-3 my-4'>
            <div className={`h-6 rounded-md ${colors.light} animate-pulse w-3/4 mx-auto`} />
            <div className={`h-4 rounded-md ${colors.light} animate-pulse w-1/2 mx-auto`} />
            <div className='flex justify-center space-x-2 mt-6'>
              {[0, 1, 2].map((i) => (
                <div key={i} className={`w-8 h-8 rounded-full ${colors.light} animate-pulse`} style={{ animationDelay: `${i * 0.15}s` }} />
              ))}
            </div>
          </div>
        );

      case 'progress':
        return (
          <div className='w-full max-w-md my-4'>
            <div className='h-1.5 w-full bg-gray-200 rounded-full overflow-hidden'>
              <div className={`h-full ${colors.primary} animate-progress-indeterminate`} style={{ width: '30%' }} />
            </div>
          </div>
        );

      case 'spinner':
      default:
        return (
          <div className='relative my-4'>
            <div className={`absolute inset-0 rounded-full blur-md opacity-30 ${colors.primary}`} />
            <Loader2 className={`animate-spin ${colors.text}`} size={40} strokeWidth={2} />
          </div>
        );
    }
  };

  if (size === 'full') {
    return <LoadingState message={displayMessage} />;
  }

  return (
    <div className={cn(sectionLoaderVariants({ variant, size }), 'select-none', className)} {...props}>
      <div className={`absolute -z-10 w-[30%] h-[30%] top-[15%] left-[15%] rounded-full opacity-75 animate-pulse blur-2xl ${colors.light}`} />
      <div
        className={`absolute -z-10 w-[25%] h-[25%] bottom-[20%] right-[20%] rounded-full opacity-75 animate-pulse blur-xl ${colors.light}`}
        style={{ animationDuration: '3s' }}
      />

      <div className='flex flex-col items-center justify-center gap-4 z-10'>
        {renderLoader()}

        <div className='text-center space-y-2'>
          {title && <h3 className={`text-lg font-semibold ${colors.text}`}>{title}</h3>}

          {displayMessage && <p className='text-gray-600 font-medium'>{displayMessage}</p>}
        </div>
      </div>

      <div
        className={`absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-transparent via-${
          accentColor === 'gradient' ? 'purple' : accentColor
        }-400 to-transparent opacity-30`}
      />
    </div>
  );
}

export default VisitorsLoader;

interface ILoadingState {
  message?: string;
}

export const LoadingState: React.FC<ILoadingState> = ({ message = 'Loading, please wait...' }) => (
  <div className='relative overflow-hidden min-h-[650px] flex items-center justify-center py-12 bg-gradient-to-b from-white to-purple-50'>
    <div className='absolute top-0 right-0 w-full h-full overflow-hidden opacity-10 pointer-events-none'>
      <div className='absolute -right-10 -top-10 w-72 h-72 bg-purple-300 rounded-full'></div>
      <div className='absolute right-20 top-20 w-40 h-40 bg-purple-400 rounded-full'></div>
      <div className='absolute left-20 bottom-20 w-56 h-56 bg-purple-300 rounded-full'></div>
      <div className='absolute -left-10 -bottom-10 w-72 h-72 bg-purple-400 rounded-full'></div>
    </div>

    <div className='flex flex-col items-center w-full max-w-3xl mx-auto px-6 z-10'>
      <motion.div
        initial={{ scale: 1, opacity: 1 }}
        animate={{ scale: 1, opacity: 1 }}
        transition={{ duration: 0, ease: 'easeOut' }}
        className='mb-10 relative'
      >
        <div className='relative'>
          <svg className='w-72 h-72' viewBox='0 0 400 400' fill='none' xmlns='http://www.w3.org/2000/svg'>
            <motion.circle cx='200' cy='200' r='160' fill='#F5F3FF' initial={{ scale: 1 }} animate={{ scale: 1 }} transition={{ duration: 0 }} />

            <motion.path
              d='M200 40C111.6 40 40 111.6 40 200C40 288.4 111.6 360 200 360C288.4 360 360 288.4 360 200C360 111.6 288.4 40 200 40Z'
              stroke='#DDD6FE'
              strokeWidth='8'
              strokeLinecap='round'
              initial={{ pathLength: 1 }}
              animate={{ pathLength: 1 }}
              transition={{ duration: 0, ease: 'easeInOut' }}
            />

            <motion.circle
              cx='200'
              cy='200'
              r='100'
              stroke='#C4B5FD'
              strokeWidth='16'
              strokeDasharray='628'
              strokeDashoffset='628'
              strokeLinecap='round'
              animate={{
                strokeDashoffset: [628, 0],
                rotate: [0, 360],
              }}
              transition={{
                strokeDashoffset: { duration: 3, repeat: Infinity, delay: 0 },
                rotate: { duration: 3, repeat: Infinity, ease: 'linear', delay: 0 },
              }}
            />

            <motion.circle
              cx='200'
              cy='200'
              r='70'
              stroke='#A78BFA'
              strokeWidth='12'
              strokeDasharray='440'
              strokeDashoffset='440'
              strokeLinecap='round'
              animate={{
                strokeDashoffset: [440, 0],
                rotate: [360, 0],
              }}
              transition={{
                strokeDashoffset: { duration: 3, repeat: Infinity, delay: 0 },
                rotate: { duration: 3, repeat: Infinity, ease: 'linear', delay: 0 },
              }}
            />

            <motion.circle
              cx='200'
              cy='200'
              r='40'
              fill='#8B5CF6'
              initial={{ scale: 1 }}
              animate={{ scale: [0.8, 1, 0.8] }}
              transition={{
                duration: 2,
                repeat: Infinity,
                repeatType: 'reverse',
                times: [0, 0.5, 1],
                delay: 0,
              }}
            />

            <motion.path
              d='M120 80L140 100M280 80L260 100M120 320L140 300M280 320L260 300'
              stroke='#A78BFA'
              strokeWidth='6'
              strokeLinecap='round'
              initial={{ pathLength: 0, opacity: 0.7 }}
              animate={{ pathLength: 1, opacity: 0.7 }}
              transition={{ duration: 0.8, delay: 0 }}
            />
          </svg>

          <motion.div
            className='absolute -bottom-4 -right-4 bg-gradient-to-br from-purple-500 to-purple-600 text-white p-4 rounded-2xl shadow-lg'
            initial={{ scale: 1, rotate: 0 }}
            animate={{
              rotate: 360,
              scale: [1, 1.1, 1],
            }}
            transition={{
              rotate: { duration: 2, repeat: Infinity, ease: 'linear', delay: 0 },
              scale: { duration: 1.5, repeat: Infinity, repeatType: 'reverse', delay: 0 },
            }}
          >
            <Loader size={28} />
          </motion.div>
        </div>
      </motion.div>

      <motion.h1
        initial={{ y: 0, opacity: 1 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0, delay: 0 }}
        className='text-4xl font-bold text-purple-800 text-center mb-4'
      >
        {message}
      </motion.h1>

      <motion.p
        initial={{ y: 0, opacity: 1 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0, delay: 0 }}
        className='text-lg text-gray-600 text-center max-w-xl mb-10'
      >
        We are fetching your data. Please be patient, it may take a moment. This won't take long.
      </motion.p>

      <motion.div
        initial={{ opacity: 1 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0, delay: 0 }}
        className='flex items-center gap-2 text-gray-500 text-sm'
      >
        <Search size={14} />
        <span>Searching through database records...</span>
      </motion.div>
    </div>
  </div>
);
