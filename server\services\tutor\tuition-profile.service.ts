import apiClient from '@/server/apiClient';
import { CreateTuitionInfoInput, UpdateTuitionInfoInput } from '@/validation/schemas/tutor/profiles/tuition-info.schema';
import { CreateTeachingExperienceInput, UpdateTeachingExperienceInput } from '@/validation/schemas/tutor/profiles/teaching-experience.schema';
import { CreateTeachingSubjectInput, UpdateTeachingSubjectInput } from '@/validation/schemas/tutor/profiles/teaching-subjects.schema';
import { IDeliveryModeMap } from '@/validation/schemas/enquiry.maps';
import { IBusinessLocationDocument } from '../user.service';
import { ITuitionTypeMap } from '@/validation/schemas/tutor/tuition.maps';
import { IServiceCategoryMap } from '@/validation/schemas/education/index.maps';
import { IBoardDocument, IClassDocument, ISubjectDocument } from '../education/school.service';
import { IStreamDocument, IDegreeLevelDocument, IDegreeDocument, IBranchDocument, ICollegeSubjectDocument } from '../education/college.service';
import { ICourseTypeDocument, ICourseDocument } from '../education/course.service';
import { IExamCategoryDocument, IExamDocument, IExamSubjectDocument } from '../education/exam.service';
import { IHobbyTypeDocument, IHobbyDocument } from '../education/hobby.service';
import { ILanguageTypeDocument, ILanguageDocument } from '../education/language.service';
import { IPagination } from '@/types/api';

export interface ITuitionInfoDocument {
  _id: string;
  userId: string;
  totalTeachingExperience: number;
  isFullTimeTeacher: boolean;
  teachesSpecialStudents: boolean;
  maxTravelDistance: number;
  spokenLanguages: string[];
  deliveryModes: IDeliveryModeMap[];
  location: string;
  coordinates?: { lat: number; lng: number };
  description: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface ITeachingExperienceDocument {
  _id: string;
  userId: string;
  tuitionType: ITuitionTypeMap;
  experienceMonths: number;
  placeName: string;
  location: string;
  coordinates?: { lat: number; lng: number };
  businessLocationId?: string;
  businessLocationDetails?: IBusinessLocationDocument;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface ITeachingSubjectDocument {
  _id: string;
  userId: string;
  serviceCategory: IServiceCategoryMap;
  amount: number;
  budget: number;
  currency: string;
  // School fields
  boardId?: string;
  classId?: string;
  subjectIds?: string[];
  allSubjects?: boolean;
  // College fields
  streamId?: string;
  degreeLevelId?: string;
  degreeId?: string;
  branchId?: string;
  collegeSubjectIds?: string[];
  // Language fields
  languageTypeId?: string;
  languageId?: string;
  // Hobby fields
  hobbyTypeId?: string;
  hobbyId?: string;
  // Exam fields
  examCategoryId?: string;
  examId?: string;
  examSubjectIds?: string[];
  // IT Course fields
  courseTypeId?: string;
  courseId?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface ITeachingSubjectDocumentPopulated extends ITeachingSubjectDocument {
  // School fields
  boardDetails?: IBoardDocument;
  classDetails?: IClassDocument;
  subjectDetails?: ISubjectDocument[];
  // College fields
  streamDetails?: IStreamDocument;
  degreeLevelDetails?: IDegreeLevelDocument;
  degreeDetails?: IDegreeDocument;
  branchDetails?: IBranchDocument;
  collegeSubjectDetails?: ICollegeSubjectDocument[];
  // Language fields
  languageTypeDetails?: ILanguageTypeDocument;
  languageDetails?: ILanguageDocument;
  // Hobby fields
  hobbyTypeDetails?: IHobbyTypeDocument;
  hobbyDetails?: IHobbyDocument;
  // Exam fields
  examCategoryDetails?: IExamCategoryDocument;
  examDetails?: IExamDocument;
  examSubjectDetails?: IExamSubjectDocument[];
  // IT Course fields
  courseTypeDetails?: ICourseTypeDocument;
  courseDetails?: ICourseDocument;
}

export interface ITuitionInfoResponse {
  tuitionInfo: ITuitionInfoDocument;
}

// Teaching Experience
export interface ITeachingExperienceResponse {
  teachingExperience: ITeachingExperienceDocument;
}
export interface ITeachingExperienceListResponse {
  teachingExperiences: ITeachingExperienceDocument[];
  pagination: IPagination;
}
// Teaching Subjects
export interface ITeachingSubjectResponse {
  teachingSubject: ITeachingSubjectDocument;
}
export interface ITeachingSubjectListResponse {
  teachingSubjects: ITeachingSubjectDocumentPopulated[];
  pagination: IPagination;
}

// Tuition Info
const tuitionInfo = {
  getTuitionInfo: () => apiClient.get<ITuitionInfoResponse>('/tutor/tuition-info'),
  createTuitionInfo: (data: CreateTuitionInfoInput) => apiClient.post<ITuitionInfoResponse>('/tutor/tuition-info', data),
  updateTuitionInfo: (id: string, data: UpdateTuitionInfoInput) => apiClient.patch<ITuitionInfoResponse>(`/tutor/tuition-info/${id}`, data),
  deleteTuitionInfo: (id: string) => apiClient.delete(`/tutor/tuition-info/${id}`),
};

// Teaching Experience
const teachingExperience = {
  getAllTeachingExperience: () => apiClient.get<ITeachingExperienceListResponse>('/tutor/teaching-experience'),
  createTeachingExperience: (data: CreateTeachingExperienceInput) => apiClient.post<ITeachingExperienceResponse>('/tutor/teaching-experience', data),
  updateTeachingExperience: (id: string, data: UpdateTeachingExperienceInput) =>
    apiClient.patch<ITeachingExperienceResponse>(`/tutor/teaching-experience/${id}`, data),
  deleteTeachingExperience: (id: string) => apiClient.delete(`/tutor/teaching-experience/${id}`),
};

// Teaching Subjects
const teachingSubjects = {
  getAllTeachingSubjects: () => apiClient.get<ITeachingSubjectListResponse>('/tutor/teaching-subjects'),
  createTeachingSubject: (data: CreateTeachingSubjectInput) => apiClient.post<ITeachingSubjectDocument>('/tutor/teaching-subjects', data),
  updateTeachingSubject: (id: string, data: UpdateTeachingSubjectInput) =>
    apiClient.patch<ITeachingExperienceDocument>(`/tutor/teaching-subjects/${id}`, data),
  deleteTeachingSubject: (id: string) => apiClient.delete(`/tutor/teaching-subjects/${id}`),
};

const tuitionProfileService = { tuitionInfo, teachingExperience, teachingSubjects };

export default tuitionProfileService;
