interface SelectOptionsConfig<T> {
  items: T[] | undefined;
  keyMapping: keyof T | Record<string, keyof T>;
  labelKey?: keyof T;
  labelFormatter?: (item: T) => string;
}

export function createSelectOptions<T extends Record<string, any>>(
  itemsOrConfig: T[] | undefined | SelectOptionsConfig<T>,
  valueKey?: keyof T | Record<string, keyof T>,
  labelKey?: keyof T,
  labelFormatter?: (item: T) => string
): { value: string; label: string }[] {
  if (itemsOrConfig && typeof itemsOrConfig === 'object' && 'items' in itemsOrConfig) {
    const config = itemsOrConfig as SelectOptionsConfig<T>;
    return createSelectOptions(config.items, config.keyMapping, config.labelKey, config.labelFormatter);
  }

  const items = itemsOrConfig as T[] | undefined;
  if (!items || !items.length) return [];

  if (valueKey && typeof valueKey === 'object') {
    const entries = Object.entries(valueKey);
    if (entries.length > 0) {
      const [actualValueKey, actualLabelKey] = entries[0];

      return items.map((item) => ({
        value: String(item[actualValueKey as keyof T]),
        label: labelFormatter ? labelFormatter(item) : String(item[actualLabelKey as keyof T]),
      }));
    }
  }

  if (valueKey && labelKey) {
    return items.map((item) => ({
      value: String(item[valueKey as keyof T]),
      label: labelFormatter ? labelFormatter(item) : String(item[labelKey]),
    }));
  }

  return [];
}

export function createOptionsKeyMap<T extends Record<string, { key: K; label: L }>, K extends string = string, L extends string = string>(
  map: T
): Array<{ value: T[keyof T]['key']; label: T[keyof T]['label'] }> {
  return Object.values(map).map((item) => ({ value: item.key, label: item.label })) as Array<{
    value: T[keyof T]['key'];
    label: T[keyof T]['label'];
  }>;
}

export function extractId(value: any): string {
  if (!value) return '';
  if (typeof value === 'string') return value;
  if (typeof value === 'object' && value._id) return value._id.toString();
  return '';
}
