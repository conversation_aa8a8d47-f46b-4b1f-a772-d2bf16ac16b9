import { cn } from '@/lib/utils';
import { formatDistanceToNow } from 'date-fns';
import { Briefcase, IndianRupee, MapPin, Star, Clock, Users, CheckCircle, ScrollText } from 'lucide-react';

const job = {
  title: 'Data Scientist',
  company: 'Swiggy',
  rating: 3.8,
  reviews: 3574,
  experience: '0-3 Yrs',
  salary: 'Not disclosed',
  location: 'Remote',
  hiringLocation: 'Bangalore Rural',
  posted: '1 day ago',
  openings: 1,
  applicants: 5418,
  logo: '/temp/swiggy-logo.gif',
  description: `
    Data Science at Swiggy
    Data Science and applied ML is ingrained deeply in decision making and product development at Swiggy. Our data scientists work closely with cross-functional teams to ship end-to-end data products, from formulating the business problem in mathematical/ML terms to iterating on ML/DL methods to taking them to production. We own or co-own several initiatives with a direct line of sight to impact on customer experience as well as business metrics. We also encourage open sharing of ideas and publishing in internal and external avenues.

    About the Ads Monetization Team
    This team is responsible for building and optimizing ML solutions for the full lifecycle of ads across both Food and Instamart Business lines, everything from sourcing the right set of ads to pricing them to targeting them via personalized and multi-objective-optimized user-response models. Ads being one of the highest throughput and lowest latency systems at Swiggy, we are also maniacally focused about delivering pragmatic and scalable solutions.
  `,
  responsibilities: [
    'You will leverage your strong ML/DL/Statistics background to build new and next generation of ML based solutions to improve the quality of ads recommendation and leverage various optimization techniques to improve the campaign performance.',
    'You will mine and extract relevant information from Swiggy’s massive historical data to help ideate and identify solutions to business and CX problems.',
    'You will work closely with engineers/PMs/analysts on detailed requirements, technical designs, and implementation of end-to-end inference solutions at Swiggy scale.',
    'You will stay abreast with the latest in ML research for Ads Bidding algorithms, Recommendation Systems related areas and help adapt it to Swiggy’s problem statements.',
    'You will publish and talk about your work in internal and external forums to both technical and layman audiences.',
  ],
  qualifications: [
    'Bachelors or Masters degree in a quantitative field with 0-2 years of industry/research lab experience.',
    'Required: Excellent problem solving skills, ability to deconstruct and formulate solutions from first-principles.',
    'Required: Depth and hands-on experience in applying ML/DL, statistical techniques to business problems.',
    'Preferred: Experience working with big data and shipping ML/DL models to production.',
    'Required: Strong proficiency in Python, SQL, Spark, Tensorflow.',
    'Required: Strong spoken and written communication skills.',
    'Big plus: Experience in the space of ecommerce and logistics.',
  ],
  role: 'Data Scientist',
  industryType: 'Analytics / KPO / Research',
  department: 'Data Science & Analytics',
  employmentType: 'Full Time, Permanent',
  roleCategory: 'Data Science & Machine Learning',
  education: 'B.Tech/B.E. in Any Specialization',
  keySkills: ['Natural Language Processing', 'Machine Learning', 'Deep Learning', 'Neural Networks', 'Computer Vision'],
};

const recommendedJobs = [
  {
    id: 2,
    title: 'Regional Sales Manager - Colleges/Universities Tieups',
    company: 'Q Academy',
    rating: 2.6,
    reviews: 6,
    location: 'Remote',
    date: new Date('2023-06-28T12:00:00Z'),
    logo: '/temp/swiggy-logo.gif',
  },
  {
    id: 4,
    title: 'Senior/Lead Counsellor',
    company: 'Nxtwave Disruptive Technologies',
    rating: 3.9,
    reviews: 503,
    location: 'Pune, Delhi',
    date: new Date(),
    logo: '/temp/ibm-logo.gif',
  },
  {
    id: 5,
    title: 'Head of Finance & Administration',
    company: 'ABC Bank',
    rating: 4.2,
    reviews: 1000,
    location: 'New York',
    date: new Date('2023-05-20T12:00:00Z'),
    logo: '/temp/swiggy-logo.gif',
  },
  {
    id: 6,
    title: 'Senior Data Scientist',
    company: 'ABC Data Solutions',
    rating: 3.8,
    reviews: 350,
    location: 'New York',
    date: new Date('2023-04-15T12:00:00Z'),
    logo: '/temp/ibm-logo.gif',
  },
];

const premiumJobs = [
  {
    id: 5,
    title: 'Head of Finance & Administration',
    company: 'ABC Bank',
    rating: 4.2,
    reviews: 1000,
    location: 'New York',
    date: new Date('2023-05-20T12:00:00Z'),
    logo: '/temp/swiggy-logo.gif',
  },
  {
    id: 6,
    title: 'Senior Data Scientist',
    company: 'ABC Data Solutions',
    rating: 3.8,
    reviews: 350,
    location: 'New York',
    date: new Date('2023-04-15T12:00:00Z'),
    logo: '/temp/ibm-logo.gif',
  },
];

const PremiumJobs = () => (
  <div className='bg-white p-6 rounded-3xl'>
    <h3 className='text-lg font-semibold mb-4'>Premium Jobs</h3>
    <div className='space-y-4'>
      {premiumJobs.map((job, index) => (
        <div key={job.id} className={cn('flex gap-4 items-start', index !== premiumJobs.length - 1 ? 'border-b pb-4' : '')}>
          {job.logo && <img src={job.logo} alt={`${job.company} logo`} className='w-10 h-10' />}
          <div className='flex-grow'>
            <h4 className='text-base font-semibold'>{job.title}</h4>
            <p className='text-sm text-gray-600 mt-1'>{job.company}</p>
            <div className='flex items-center text-gray-500 text-sm flex-wrap my-1'>
              {job.rating && (
                <>
                  <Star fill='orange' stroke='none' size={16} className='shrink-0' />
                  <span className='ml-1'>{job.rating}</span>
                  <span className='mx-1'>|</span>
                  <span>{job.reviews} reviews</span>
                  <span className='mx-1'>|</span>
                </>
              )}
              <p className='flex gap-2'>
                <MapPin className='shrink-0' size={16} />
                <span>{job.location}</span>
              </p>
            </div>
            <p className='text-sm text-gray-500'>{`Posted ${formatDistanceToNow(new Date(job.date))} ago`}</p>
          </div>
        </div>
      ))}
    </div>
  </div>
);

const RecommendedJobs = () => (
  <div className='bg-white p-6 rounded-3xl'>
    <h3 className='text-lg font-semibold mb-4'>Jobs you might be interested in</h3>
    <div className='space-y-4'>
      {recommendedJobs.map((job, index) => (
        <div key={job.id} className={cn('flex gap-4 items-start', index !== recommendedJobs.length - 1 ? 'border-b pb-4' : '')}>
          {job.logo && <img src={job.logo} alt={`${job.company} logo`} className='w-10 h-10' />}
          <div className='flex-grow'>
            <h4 className='text-base font-semibold'>{job.title}</h4>
            <p className='text-sm text-gray-600 mt-1'>{job.company}</p>
            <div className='flex items-center text-gray-500 text-sm flex-wrap my-1'>
              {job.rating && (
                <>
                  <Star fill='orange' stroke='none' size={16} className='shrink-0' />
                  <span className='ml-1'>{job.rating}</span>
                  <span className='mx-1'>|</span>
                  <span>{job.reviews} reviews</span>
                  <span className='mx-1'>|</span>
                </>
              )}
              <p className='flex gap-2'>
                <MapPin className='shrink-0' size={16} />
                <span>{job.location}</span>
              </p>
            </div>
            <p className='text-sm text-gray-500'>{`Posted ${formatDistanceToNow(new Date(job.date))} ago`}</p>
          </div>
        </div>
      ))}
    </div>
  </div>
);

const TutorJobDetail = () => (
  <section className='flex flex-col md:flex-row items-start gap-6'>
    <main className='w-full md:w-2/3'>
      <div className='bg-white p-6 rounded-3xl'>
        <div className='flex justify-between items-start text-sm'>
          <div className='space-y-2'>
            <h2 className='text-xl font-semibold'>{job.title}</h2>
            <div className='flex items-center gap-2 flex-wrap text-gray-600'>
              <div className='flex gap-2 flex-wrap'>
                <Star fill='orange' stroke='none' size={16} />
                <span>{job.rating}</span>
                <span>|</span>
              </div>
              <div className='flex gap-2 flex-wrap'>
                <span>{job.reviews} Reviews</span>
                <span>|</span>
                <span>{job.company}</span>
              </div>
            </div>
            <div className='flex items-center gap-2 flex-wrap text-gray-500'>
              <div className='flex gap-2'>
                <Briefcase size={16} />
                <span>{job.experience}</span>
                <span>|</span>
              </div>
              <div className='flex gap-2'>
                <IndianRupee size={16} />
                <span>{job.salary}</span>
                <span>|</span>
              </div>
              <div className='flex gap-2'>
                <MapPin size={16} />
                <span>{job.location}</span>
              </div>
            </div>
            <div className='flex items-start gap-2 text-gray-500'>
              <MapPin size={16} />
              <p>
                Hiring office located in <span>{job.hiringLocation}</span>
              </p>
            </div>
          </div>
          <div className='flex flex-col items-center'>
            <img src={job.logo} alt={`${job.company} logo`} className='size-14' />
          </div>
        </div>
        <div className='mt-4 flex justify-between items-center pt-4 border-t text-sm flex-wrap max-w-full w-full'>
          <div className='flex items-center text-gray-500 gap-2 flex-wrap'>
            <div className='flex gap-2 items-start'>
              <Clock size={16} /> Posted: <span className='ml-2 font-semibold text-black'> {job.posted}</span>
              <span>|</span>
            </div>
            <div className='flex gap-2 items-start'>
              <Users size={16} /> Openings: <span className='ml-2 font-semibold text-black'> {job.openings}</span>
              <span>|</span>
            </div>
            <div className='flex gap-2 items-start'>
              <CheckCircle size={16} /> Applicants: <span className='ml-2 font-semibold text-black'> {job.applicants}</span>
            </div>
            <button className='btn-default-sm ml-auto md:hidden'>Apply Now</button>
          </div>
          <div className='ml-auto mt-2 hidden md:block'>
            <button className='btn-default-sm'>Apply Now</button>
          </div>
        </div>
      </div>
      <div className='bg-white p-6 rounded-3xl mt-6'>
        <h3 className='text-lg font-semibold'>Job description</h3>
        <p className='text-gray-700 whitespace-pre-line'>{job.description}</p>
        <div className='mt-4'>
          <h3 className='text-lg font-semibold'>What you will do</h3>
          <ul className='list-disc list-inside text-gray-700'>
            {job.responsibilities.map((resp, index) => (
              <li key={index}>{resp}</li>
            ))}
          </ul>
        </div>
        <div className='mt-4'>
          <h3 className='text-lg font-semibold'>Qualifications</h3>
          <ul className='list-disc list-inside text-gray-700'>
            {job.qualifications.map((qual, index) => (
              <li key={index}>{qual}</li>
            ))}
          </ul>
        </div>
        <div className='mt-4'>
          <h3 className='text-lg font-semibold'>Role</h3>
          <p className='text-gray-700'>{job.role}</p>
        </div>
        <div className='mt-4'>
          <h3 className='text-lg font-semibold'>Industry Type</h3>
          <p className='text-gray-700'>{job.industryType}</p>
        </div>
        <div className='mt-4'>
          <h3 className='text-lg font-semibold'>Department</h3>
          <p className='text-gray-700'>{job.department}</p>
        </div>
        <div className='mt-4'>
          <h3 className='text-lg font-semibold'>Employment Type</h3>
          <p className='text-gray-700'>{job.employmentType}</p>
        </div>
        <div className='mt-4'>
          <h3 className='text-lg font-semibold'>Role Category</h3>
          <p className='text-gray-700'>{job.roleCategory}</p>
        </div>
        <div className='mt-4'>
          <h3 className='text-lg font-semibold'>Education</h3>
          <p className='text-gray-700'>{job.education}</p>
        </div>
        <div className='mt-4'>
          <h3 className='text-lg font-semibold mb-2'>Key Skills</h3>
          <div className='flex flex-wrap gap-2'>
            {job.keySkills.map((skill, index) => (
              <span key={index} className='bg-gray-100 text-gray-700 px-4 py-1 rounded'>
                {skill}
              </span>
            ))}
          </div>
        </div>
      </div>
    </main>
    <aside className='w-full md:w-1/3 space-y-4'>
      <PremiumJobs />
      <RecommendedJobs />
      <div className='w-full h-80 bg-white rounded-3xl p-6 flex justify-center items-center'>Any small banner</div>
    </aside>
  </section>
);

export default TutorJobDetail;
