import { Badge<PERSON>heck, Check, Ellipsis, Plus, X } from 'lucide-react';
import Link from 'next/link';

const TutorKycPage = () => {
  return (
    <div className='bg-white p-6 rounded-3xl relative min-h-[650px]'>
      <div>
        <h2 className='text-base font-semibold'>Manual KYC (Doc Upload)</h2>
        <p className='text-sm font-medium my-2'>Upload your documents manually for verification.</p>
      </div>
      <div className='py-4 mt-4'>
        <DocumentUploadPage />
      </div>
    </div>
  );
};

const DocumentUploadPage = () => {
  return (
    <div className='flex flex-col items-start justify-start'>
      <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4'>
        <div className='flex flex-col lg:flex-row gap-4 items-center'>
          <DocumentCard
            placeholder='/icons/aadhar-placeholder.webp'
            title='Aadhar Card'
            description='Government-issued ID card with front and back photos required.'
            status='approved'
            href='/tutor-dash/profiles/kyc/manual/aadhar'
          />
          <Plus size={40} strokeWidth={1.75} />
        </div>
        <div className='flex flex-col lg:flex-row gap-4 items-center'>
          <DocumentCard
            placeholder='/icons/pan-placeholder.webp'
            title='PAN Card'
            description='Issued by Income Tax Department, useful for tax-related documentation.'
            status='pending'
            href='/tutor-dash/profiles/kyc/manual/pan'
          />
          <p className='font-medium text-lg'>OR</p>
        </div>
        <div className='flex flex-col lg:flex-row gap-4 items-center'>
          <DocumentCard
            href='/tutor-dash/profiles/kyc/manual/other'
            title='Other Document'
            description='Any other official document such as Driving License or Voter ID.'
            status='rejected'
          />
          {/* TODO: can remove this */}
          <BadgeCheck size={40} className='text-primaryColor opacity-0 hidden md:block' />
        </div>
      </div>
    </div>
  );
};

interface IDocumentCard {
  title: string;
  description: string;
  status: 'approved' | 'pending' | 'rejected';
  placeholder?: string;
  href: string;
}

const DocumentCard: React.FC<IDocumentCard> = ({ title, description, status, placeholder, href }) => {
  return (
    <div className='border-2 border-dashed border-primaryColor rounded-lg p-4 flex flex-col items-center relative'>
      {status === 'approved' && (
        <div className='bg-primaryColor flex gap-2 justify-start items-center text-white py-1 pl-3 pr-1.5 text-sm rounded-3xl absolute top-4 right-4'>
          <span>Approved</span>
          <div className='bg-white text-primaryColor rounded-full p-0.5'>
            <Check size={14} />
          </div>
        </div>
      )}

      {status === 'pending' && (
        <div className='bg-yellow-500 flex gap-2 justify-start items-center text-white py-1 pl-3 pr-1.5 text-sm rounded-3xl absolute top-4 right-4'>
          <span>Pending</span>
          <div className='bg-white text-yellow-500 rounded-full p-0.5'>
            <Ellipsis size={14} />
          </div>
        </div>
      )}

      {status === 'rejected' && (
        <div className='bg-red-500 flex gap-2 justify-start items-center text-white py-1 pl-3 pr-1.5 text-sm rounded-3xl absolute top-4 right-4'>
          <span>Rejected</span>
          <div className='bg-white text-red-500 rounded-full p-0.5'>
            <X size={14} />
          </div>
        </div>
      )}

      <div className='bg-primaryColor-50 rounded p-2 my-2 mt-10 h-40 w-full'>
        {placeholder && <img src={placeholder} alt={title} className='object-contain w-full h-full' />}
      </div>
      <p className='text-lg font-medium text-gray-800'>{title}</p>
      <p className='text-sm text-gray-500 text-center'>{description}</p>
      <Link href={href} className='btn-default-sm w-full mt-2'>
        Upload {title}
      </Link>
    </div>
  );
};

export default TutorKycPage;
