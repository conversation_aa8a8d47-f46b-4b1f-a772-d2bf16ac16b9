import { useQuery, useMutation } from '@tanstack/react-query';
import { QUERY_KEYS } from '@/lib/react-query/queryKeys';
import { queryClient } from '@/lib/react-query/queryClient';

import { hobbyTypeService, hobbyService } from '@/server/services/education/hobby.service';
import { CreateHobbyTypeInput, UpdateHobbyTypeInput, CreateHobbyInput, UpdateHobbyInput } from '@/validation/schemas/education/hobby.schema';

const hobbyEducationService = {
  hobbyTypes: hobbyTypeService,
  hobbies: hobbyService,
};

// Hobby Type Hooks
export function useGetAllHobbyTypes(params?: Record<string, any>, options = {}) {
  return useQuery({
    queryKey: [QUERY_KEYS.EDUCATION.HOBBY_TYPES, params],
    queryFn: () => hobbyEducationService.hobbyTypes.getAllHobbyTypes(params),
    ...options,
  });
}

export function useGetHobbyTypeById(id: string, options = {}) {
  return useQuery({
    queryKey: [QUERY_KEYS.EDUCATION.HOBBY_TYPE, id],
    queryFn: () => hobbyEducationService.hobbyTypes.getHobbyTypeById(id),
    enabled: !!id,
    ...options,
  });
}

export function useCreateHobbyType() {
  return useMutation({
    mutationFn: (data: CreateHobbyTypeInput) => hobbyEducationService.hobbyTypes.createHobbyType(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.EDUCATION.HOBBY_TYPES] });
    },
  });
}

export function useUpdateHobbyType() {
  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateHobbyTypeInput }) => hobbyEducationService.hobbyTypes.updateHobbyType(id, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.EDUCATION.HOBBY_TYPES] });
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.EDUCATION.HOBBY_TYPE, variables.id] });
    },
  });
}

export function useDeleteHobbyType() {
  return useMutation({
    mutationFn: (id: string) => hobbyEducationService.hobbyTypes.deleteHobbyType(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.EDUCATION.HOBBY_TYPES] });
    },
  });
}

// Hobby Hooks
export function useGetAllHobbies(params?: Record<string, any>, options = {}) {
  return useQuery({
    queryKey: [QUERY_KEYS.EDUCATION.HOBBIES, params],
    queryFn: () => hobbyEducationService.hobbies.getAllHobbies(params),
    ...options,
  });
}

export function useGetHobbyById(id: string, options = {}) {
  return useQuery({
    queryKey: [QUERY_KEYS.EDUCATION.HOBBY, id],
    queryFn: () => hobbyEducationService.hobbies.getHobbyById(id),
    enabled: !!id,
    ...options,
  });
}

export function useCreateHobby() {
  return useMutation({
    mutationFn: (data: CreateHobbyInput) => hobbyEducationService.hobbies.createHobby(data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.EDUCATION.HOBBIES] });
    },
  });
}

export function useUpdateHobby() {
  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateHobbyInput }) => hobbyEducationService.hobbies.updateHobby(id, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.EDUCATION.HOBBIES] });
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.EDUCATION.HOBBY, variables.id] });
    },
  });
}

export function useDeleteHobby() {
  return useMutation({
    mutationFn: (id: string) => hobbyEducationService.hobbies.deleteHobby(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.EDUCATION.HOBBIES] });
    },
  });
}
