'use client';

import { FormField, FormItem, FormControl, Form, FormLabel, FormMessage } from '@/components/ui/form';
import { zodResolver } from '@hookform/resolvers/zod';
import { whatDoYouTeachSchema } from '@/lib/validations/tutor-dash/zod';
import { useForm } from 'react-hook-form';
import { Loader2 } from 'lucide-react';
import * as z from 'zod';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { studySectionMap, studySchoolBoardMap, studySchoolClassMap, studySubjectMap, studyCollegeBoardMap, studyAmountTypeMap } from '@/constants';
import MultiSelectWithSearch from './MultiSelectWithSearch';
import { useState } from 'react';
import { Skeleton } from '@/components/ui/skeleton';
import { Input } from '@/components/ui/input';

interface IMyMap {
  [key: string]: { label: string; key: string };
}

const WhatDoYouTeachForm = () => {
  const form = useForm<z.infer<typeof whatDoYouTeachSchema>>({
    resolver: zodResolver(whatDoYouTeachSchema),
    defaultValues: {
      subjects: [],
    },
  });

  const [step2, setStep2] = useState<IMyMap>({});
  const [step3, setStep3] = useState<IMyMap>({});
  const [step4, setStep4] = useState<IMyMap>({});
  const [step5, setStep5] = useState<IMyMap>({});

  const [stepsLoadingAndVisible, setStepsLoadingAndVisible] = useState({
    step2Loading: false,
    step2Visible: false,
    step3Loading: false,
    step3Visible: false,
    step4Loading: false,
    step4Visible: false,
    step5Loading: false,
    step5Visible: false,
  });

  const handleStep2 = (value: string) => {
    setStepsLoadingAndVisible((prevState) => ({ ...prevState, step2Loading: true }));

    if (value === 'school') {
      setStep2(studySchoolBoardMap);
      setTimeout(() => {
        setStepsLoadingAndVisible((prevState) => ({ ...prevState, step2Loading: false, step2Visible: true }));
      }, 1000);
    } else if (value === 'college') {
      setStep2(studyCollegeBoardMap);
      setTimeout(() => {
        setStepsLoadingAndVisible((prevState) => ({ ...prevState, step2Loading: false, step2Visible: true }));
      }, 1000);
    }

    setStep3({});
    setStep4({});
    setStep5({});

    setStepsLoadingAndVisible((prevState) => ({
      ...prevState,
      step3Visible: false,
      step4Visible: false,
      step5Visible: false,
    }));
  };

  async function onSubmit(values: z.infer<typeof whatDoYouTeachSchema>) {
    console.log(values);
  }

  return (
    <div>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className='grid grid-cols-1 md:grid-cols-2 items-start gap-6'>
          {/* Study Section */}
          <FormField
            control={form.control}
            name='studySection'
            render={({ field }) => (
              <FormItem className='w-full'>
                <FormLabel className='primary-label'>
                  <span>Study Section</span> <span className='text-primaryColor'>*</span>
                </FormLabel>
                <Select
                  onValueChange={(value) => {
                    handleStep2(value);
                    return field.onChange(value);
                  }}
                >
                  <FormControl>
                    <SelectTrigger className='primary-select'>
                      <SelectValue placeholder='Select a study section' />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {Object.entries(studySectionMap).map(([key, value]) => (
                      <SelectItem key={key} value={key}>
                        {value.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* STEP - 2 */}
          {stepsLoadingAndVisible.step2Loading && (
            <div className='space-y-2'>
              <FormLabel className='primary-label'>
                <span>Study Board</span> <span className='text-primaryColor'>*</span>
                <Loader2 size={20} className='animate-spin text-primaryColor' />
              </FormLabel>
              <Skeleton className='w-full h-12 rounded-xl' />
            </div>
          )}

          {stepsLoadingAndVisible.step2Visible && (
            <FormField
              control={form.control}
              name='studyBoard'
              render={({ field }) => (
                <FormItem className='w-full'>
                  <FormLabel className='primary-label'>
                    <span>Study Board</span> <span className='text-primaryColor'>*</span>
                  </FormLabel>
                  <Select onValueChange={field.onChange}>
                    <FormControl>
                      <SelectTrigger className='primary-select'>
                        <SelectValue placeholder='Select a study board' />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {Object.entries(step2).length > 0 &&
                        Object.entries(step2).map(([key, value]) => (
                          <SelectItem key={key} value={key}>
                            {value.label}
                          </SelectItem>
                        ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
          )}

          {/* Subjects */}
          <FormField
            control={form.control}
            name='subjects'
            render={({ field }) => (
              <FormItem className='w-full md:col-span-2'>
                <FormLabel className='primary-label'>
                  <span>Subjects</span> <span className='text-primaryColor'>*</span>
                </FormLabel>
                <FormControl>
                  <MultiSelectWithSearch
                    selectedOptions={field.value}
                    onChange={field.onChange}
                    options={Object.values(studySubjectMap).map((subject) => ({ label: subject.label, value: subject.key }))}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Study Class */}
          <FormField
            control={form.control}
            name='studyClass'
            render={({ field }) => (
              <FormItem className='w-full'>
                <FormLabel className='primary-label'>
                  <span>Study Class</span> <span className='text-primaryColor'>*</span>
                </FormLabel>
                <Select onValueChange={field.onChange}>
                  <FormControl>
                    <SelectTrigger className='primary-select'>
                      <SelectValue placeholder='Select a study class' />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {Object.entries(studySchoolClassMap).map(([key, value]) => (
                      <SelectItem key={key} value={key}>
                        {value.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Amount type */}
          <FormField
            control={form.control}
            name='amountType'
            render={({ field }) => (
              <FormItem className='w-full'>
                <FormLabel className='primary-label'>
                  <span>Amount Type</span> <span className='text-primaryColor'>*</span>
                </FormLabel>
                <Select onValueChange={field.onChange}>
                  <FormControl>
                    <SelectTrigger className='primary-select'>
                      <SelectValue placeholder='Select a amount type' />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {Object.entries(studyAmountTypeMap).map(([key, value]) => (
                      <SelectItem key={key} value={key}>
                        {value.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Amount */}
          <FormField
            control={form.control}
            name='amount'
            render={({ field }) => (
              <FormItem className='w-full'>
                <FormLabel className='primary-label'>
                  <span>Amount</span> <span className='text-primaryColor'>*</span>
                </FormLabel>
                <FormControl>
                  <Input className='primary-input' type='number' placeholder='Amount' {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <button type='submit' disabled={form.formState.isSubmitting} className='btn-default-md md:col-span-2 w-fit'>
            {form.formState.isSubmitting ? (
              <>
                <Loader2 size={20} className='animate-spin mr-3' />
                <span>Saving...</span>
              </>
            ) : (
              <span>Submit</span>
            )}
          </button>
        </form>
      </Form>
    </div>
  );
};

export default WhatDoYouTeachForm;
