'use client';

import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Form } from '@/components/ui/form';
import { PrimaryInput, PrimarySelect, PrimaryDateInput, PrimaryPhoneInput, PrimaryLocationInput, SubmitButton } from '@/components/forms';
import Link from 'next/link';
import { capitalizeWord } from '@/lib/string.utils';
import {
  IGenderMap,
  IUserTypeMap,
  studentOrTutorGroupMap,
  genderOptions,
  referralSourcesOptions,
  referralSourcesWhichRequiresOther,
} from '@/validation/schemas/maps';
import { RegisterInput, registerSchema } from '@/validation/schemas/auth.schema';
import { toast } from 'react-toastify';
import authService from '@/server/services/auth.service';

interface RegisterFormProps {
  onSuccess: (phone: string) => void;
  selectedUserType: IUserTypeMap;
}

export const RegisterForm = ({ onSuccess, selectedUserType }: RegisterFormProps) => {
  const isTutorOrStudent = Object.keys(studentOrTutorGroupMap).includes(selectedUserType);

  const form = useForm<RegisterInput>({
    resolver: zodResolver(registerSchema),
    defaultValues: {
      fullName: '',
      email: '',
      phone: '',
      password: '',
      confirmPassword: '',
      location: '',
      referralSource: '',
      otherReferralSource: '',
      userType: selectedUserType,
      gender: '' as IGenderMap,
    },
  });

  useEffect(() => {
    form.setValue('userType', selectedUserType);

    if (!isTutorOrStudent) {
      form.setValue('gender', undefined);
    }
  }, [selectedUserType, form]);

  const onSubmit = async (data: RegisterInput) => {
    if (!isTutorOrStudent) {
      data.gender = undefined;
    }

    try {
      const response = await authService.register(data);
      if (response.success) {
        toast.success(response.message);
        onSuccess(data.phone);
      } else {
        toast.error(response.message);
      }
    } catch (error: any) {
      toast.error(error.message);
    }
  };

  return (
    <div className='space-y-6 w-full'>
      <div>
        <h2 className='text-3xl font-bold text-gray-900'>Create your account</h2>
        <p className='mt-2 text-gray-600'>
          Please fill in your details to register as a <span className='text-primaryColor capitalize'>{selectedUserType}</span>
        </p>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className='grid grid-cols-1 md:grid-cols-3 gap-4'>
          <PrimaryInput form={form} name='fullName' label={capitalizeWord(selectedUserType) + ' Name'} placeholder='Enter your full name' required />
          <PrimaryInput form={form} name='email' label='Email Address' placeholder='Enter your email address' type='email' required />
          <PrimaryPhoneInput form={form} name='phone' label='Phone Number' placeholder='Enter your phone number' required />
          <PrimaryInput form={form} name='password' label='Password' placeholder='Create a strong password' type='password' required />
          <PrimaryInput form={form} name='confirmPassword' label='Confirm Password' placeholder='Confirm your password' type='password' required />
          <PrimaryLocationInput form={form} name='location' label='Your Location' placeholder='Enter your location' required />

          {isTutorOrStudent && (
            <div className='w-full'>
              <PrimarySelect form={form} name='gender' label='Gender' placeholder='Select your gender' options={genderOptions} required />
            </div>
          )}

          <PrimaryDateInput form={form} name='dateOfBirth' label={isTutorOrStudent ? 'Date of Birth' : 'Date of Incorporation'} required />

          <div className='w-full'>
            <PrimarySelect
              form={form}
              name='referralSource'
              label='How did you hear about us?'
              placeholder='Select an option'
              options={referralSourcesOptions}
              required
            />
          </div>

          {referralSourcesWhichRequiresOther.includes(form.watch('referralSource' as any)) && (
            <div className='w-full'>
              <PrimaryInput form={form} name='otherReferralSource' label='Please specify' placeholder='Tell us how you heard about us' required />
            </div>
          )}

          <SubmitButton
            disabled={form.formState.isSubmitting}
            isSubmitting={form.formState.isSubmitting}
            label='Create Account'
            submittingLabel='Creating Account...'
            className='w-full h-fit mt-auto !py-4'
          />
        </form>
        <p className='text-center text-sm text-gray-600 mt-4'>
          Already have an account?{' '}
          <Link href='/login' className='font-medium text-primaryColor hover:underline'>
            Login here
          </Link>
        </p>
      </Form>
    </div>
  );
};
