import { axiosInstance } from '@/server';
import { redirect } from 'next/navigation';
import { IUserTypeMap } from '@/validation/schemas/maps';
import { ISessionStaff, ISessionUser } from '@/types/api';

const cache = new Map();

const USER_CONSTANTS = {
  CACHE_KEY_PREFIX: 'user_',
  CACHE_TTL: 5 * 60 * 1000, // 5 minutes
  API_ENDPOINT: '/users/showMe',
  REDIRECT_URL: '/login',
  COOKIE_NAME: 'session',
  ERROR_MESSAGE: 'Failed to fetch user data',
};

const STAFF_CONSTANTS = {
  CACHE_KEY_PREFIX: 'staff_',
  CACHE_TTL: 5 * 60 * 1000, // 5 minutes
  API_ENDPOINT: '/staff/me',
  REDIRECT_URL: '/staff-access',
  COOKIE_NAME: 'staff_session',
  ERROR_MESSAGE: 'Failed to fetch staff data',
};

export async function getCurrentUser(session: string, userType: IUserTypeMap): Promise<ISessionUser> {
  try {
    if (!session) throw new Error('No session found');

    const cacheKey = `${USER_CONSTANTS.CACHE_KEY_PREFIX}${session}`;
    const cachedData = cache.get(cacheKey);

    if (cachedData && Date.now() - cachedData.timestamp < USER_CONSTANTS.CACHE_TTL) {
      return cachedData.data;
    }

    const response = await axiosInstance.get(USER_CONSTANTS.API_ENDPOINT, {
      headers: { Cookie: `${USER_CONSTANTS.COOKIE_NAME}=${session}` },
    });

    if (response.data?.success && response.data?.data?.user) {
      const userData = response.data.data.user;

      if (userData.userType !== userType) {
        throw new Error('Unauthorized user type');
      }

      cache.set(cacheKey, { data: userData, timestamp: Date.now() });

      return userData;
    } else {
      throw new Error('Invalid response structure');
    }
  } catch (error) {
    console.error(USER_CONSTANTS.ERROR_MESSAGE, error);
    redirect(USER_CONSTANTS.REDIRECT_URL);
  }
}

export async function getCurrentStaff(session: string): Promise<ISessionStaff> {
  try {
    if (!session) throw new Error('No session found');

    const cacheKey = `${STAFF_CONSTANTS.CACHE_KEY_PREFIX}${session}`;
    const cachedData = cache.get(cacheKey);

    if (cachedData && Date.now() - cachedData.timestamp < STAFF_CONSTANTS.CACHE_TTL) {
      return cachedData.data;
    }

    const response = await axiosInstance.get(STAFF_CONSTANTS.API_ENDPOINT, {
      headers: { Cookie: `${STAFF_CONSTANTS.COOKIE_NAME}=${session}` },
    });

    if (response.data?.success && response.data?.data?.staff) {
      const staffData = response.data.data.staff;

      cache.set(cacheKey, { data: staffData, timestamp: Date.now() });

      return staffData;
    } else {
      throw new Error('Invalid response structure');
    }
  } catch (error: any) {
    console.error(STAFF_CONSTANTS.ERROR_MESSAGE, error.message);
    redirect(STAFF_CONSTANTS.REDIRECT_URL);
  }
}
