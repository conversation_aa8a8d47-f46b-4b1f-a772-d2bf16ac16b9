import type { Metada<PERSON> } from 'next';
import { Inter } from 'next/font/google';
import '@/app/globals.css';
import ToastProvider from '@/components/providers/ToastProvider';
const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'Perfect Tutor - Authentication',
  description: 'Login and register to access Perfect Tutor',
};

export default function AuthLayout({ children }: Readonly<{ children: React.ReactNode }>) {
  return (
    <html lang='en'>
      <body className={inter.className}>
        <div>{children}</div>
        <ToastProvider />
      </body>
    </html>
  );
}
