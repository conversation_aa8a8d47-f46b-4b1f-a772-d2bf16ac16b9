'use client';

import { useState, useEffect, useRef } from 'react';
import Link from 'next/link';
import { bottomBarLinks } from '@/constants/tutor-dash';

const BottomNavBar = () => {
  const [selectedSection, setSelectedSection] = useState<string | null>(null);
  const navBarRef = useRef<HTMLDivElement>(null);

  const handleSectionClick = (id: string) => {
    setSelectedSection(selectedSection === id ? null : id);
  };

  const handleClickOutside = (event: MouseEvent) => {
    if (navBarRef.current && !navBarRef.current.contains(event.target as Node)) {
      setSelectedSection(null);
    }
  };

  useEffect(() => {
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <>
      {selectedSection && <div className="fixed inset-0 bg-black opacity-50 z-40" onClick={() => setSelectedSection(null)}></div>}
      <div ref={navBarRef} className='fixed lg:hidden bottom-0 left-0 z-50 p-2 flex items-center justify-center w-full gap-8 bg-white'>
        {bottomBarLinks.map((section) => {
          const isActive = selectedSection === section.id;
          return (
            <div key={section.id} className='relative'>
              <button onClick={() => handleSectionClick(section.id)} className='flex flex-col items-center justify-center bg-white text-gray-600'>
                {
                  <div className={isActive ? 'bg-primaryColor rounded-full text-white shadow-3xl p-3' : 'p-1'}>
                    <section.icon className='size-6' />
                  </div>
                }
                <span className={isActive ? 'hidden' : ''}>{section.label}</span>
              </button>
              {isActive && (
                <div className='absolute bottom-full -left-4 mb-2 bg-white px-2 rounded-lg shadow-lg border-t-2 border-t-primaryColor'>
                  {renderSubLinks(section.id)}
                </div>
              )}
            </div>
          );
        })}
      </div>
    </>
  );
};

const renderSubLinks = (sectionId: string) => {
  const section = bottomBarLinks.find((nav) => nav.id === sectionId);
  return section?.links.map((link) => (
    <Link href={`/tutor-dash/${link.href}`} key={link.title} className='flex gap-2 items-start justify-start text-sm text-start whitespace-nowrap py-4 border-b'>
      <link.icon className='size-6 mb-1' strokeWidth={1.5} />
      <span>{link.title}</span>
    </Link>
  ));
};

export default BottomNavBar;
