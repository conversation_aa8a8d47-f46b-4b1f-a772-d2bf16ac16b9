import { z } from 'zod';
import { objectIdSchema } from '../common.schema';

// Hobby Type Schemas
export const createHobbyTypeSchema = z.object({
  name: z.string({ required_error: 'Hobby type name is required' }).min(1, 'Hobby type name is required'),
  isActive: z.boolean().default(true),
});

export type CreateHobbyTypeInput = z.infer<typeof createHobbyTypeSchema>;
export type UpdateHobbyTypeInput = Partial<CreateHobbyTypeInput>;

// Hobby Schemas
export const createHobbySchema = z.object({
  name: z.string({ required_error: 'Hobby name is required' }).min(1, 'Hobby name is required'),
  hobbyType: objectIdSchema,
  isActive: z.boolean().default(true),
});

export type CreateHobbyInput = z.infer<typeof createHobbySchema>;
export type UpdateHobbyInput = Partial<CreateHobbyInput>;
