'use client';

import { Form } from '@/components/ui/form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import { toast } from 'react-toastify';
import { PrimaryInput, PrimarySwitchInput, SubmitButton } from '@/components/forms';
import { IAddUpdateFormProps } from '@/types/api';
import { useCreateStream, useUpdateStream } from '@/hooks/education/college.hooks';
import { IStreamDocument } from '@/server/services/education/college.service';
import { createStreamSchema } from '@/validation/schemas/education/college.schema';

interface IStreamFormProps extends IAddUpdateFormProps<IStreamDocument> {
  onSuccess: () => void;
}

const StreamForm: React.FC<IStreamFormProps> = ({ data, onSuccess }) => {
  const { mutateAsync: createStream, isPending: isCreating } = useCreateStream();
  const { mutateAsync: updateStream, isPending: isUpdating } = useUpdateStream();

  const streamId = data?._id;
  const isSubmitting = isCreating || isUpdating;

  const form = useForm<z.infer<typeof createStreamSchema>>({
    resolver: zodResolver(createStreamSchema),
    defaultValues: {
      name: data?.name || '',
      isActive: data?.isActive !== undefined ? data.isActive : true,
    },
  });

  const onSubmit = async (values: z.infer<typeof createStreamSchema>) => {
    try {
      const result = streamId ? await updateStream({ id: streamId, data: values }) : await createStream(values);
      if (!result.success) throw new Error(result.message || 'Operation failed');

      toast.success(result.message);
      onSuccess();
    } catch (error: any) {
      toast.error(error.message || 'An error occurred');
    }
  };

  return (
    <section className='relative'>
      <div className='bg-white rounded-lg shadow-sm p-6 max-w-2xl'>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className='grid grid-cols-1 gap-6 items-start justify-start'>
            {/* Name field */}
            <PrimaryInput form={form} name='name' label='Stream Name' placeholder='Enter stream name' required />

            {/* Is Active */}
            <PrimarySwitchInput
              form={form}
              name='isActive'
              label='Is this stream active?'
              description='Making it inactive will hide it from the website.'
            />

            <SubmitButton
              submittingLabel={streamId ? 'Updating...' : 'Creating...'}
              label={streamId ? 'Update Stream' : 'Create Stream'}
              isSubmitting={isSubmitting}
              disabled={isSubmitting}
              className='w-fit'
            />
          </form>
        </Form>
      </div>
    </section>
  );
};

export default StreamForm;
