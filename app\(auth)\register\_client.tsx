'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronLeft } from 'lucide-react';
import { RegisterForm } from './register-form';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { UserTypeSelection } from '../user-type-selection';
import { Form } from '@/components/ui/form';
import { PrimaryOTPInput, SubmitButton } from '@/components/forms';
import { StepIndicator } from '@/components/misc';
import { toast } from 'react-toastify';
import { useRouter } from 'next/navigation';
import authService from '@/server/services/auth.service';
import { IUserTypeMap } from '@/validation/schemas/maps';

const otpSchema = z.object({
  otp: z.string().length(4, 'OTP must be exactly 4 digits'),
});

type OtpFormValues = z.infer<typeof otpSchema>;

const RegisterPage = () => {
  const [selectedUserType, setSelectedUserType] = useState<IUserTypeMap>();
  const [step, setStep] = useState<number>(1);
  const [isVerifying, setIsVerifying] = useState(false);
  const [resendTimer, setResendTimer] = useState(300);
  const [registeredPhone, setRegisteredPhone] = useState('');
  const router = useRouter();

  const otpForm = useForm<OtpFormValues>({
    resolver: zodResolver(otpSchema),
    defaultValues: {
      otp: '',
    },
  });

  useEffect(() => {
    if (resendTimer > 0 && step === 3) {
      const timerId = setInterval(() => {
        setResendTimer((prev) => prev - 1);
      }, 1000);

      return () => clearInterval(timerId);
    }
  }, [resendTimer, step]);

  const handleUserTypeChange = (userType: IUserTypeMap) => {
    setSelectedUserType(userType);
  };

  const goToStep = (nextStep: number) => {
    if (nextStep === 3) {
      setResendTimer(300);
    }
    setStep(nextStep);
  };

  const handleRegistrationSuccess = (phone: string) => {
    setRegisteredPhone(phone);
    goToStep(3);
  };

  const handleVerifyOtp = async (data: OtpFormValues) => {
    if (!registeredPhone) {
      toast.error('Phone number is missing. Please try again.');
      goToStep(2);
      return;
    }

    setIsVerifying(true);
    try {
      const response = await authService.verifyOTP({ phone: registeredPhone, otp: data.otp, userType: selectedUserType! });

      if (response.success) {
        toast.success(response.message);
        router.push('/dashboard');
      } else {
        throw new Error(response.message);
      }
    } catch (error: any) {
      toast.error(error.message || 'An error occurred during verification');
    } finally {
      setIsVerifying(false);
    }
  };

  const handleResendOtp = async () => {
    if (!registeredPhone) {
      toast.error('Phone number is missing. Please try again.');
      goToStep(2);
      return;
    }

    setIsVerifying(true);
    try {
      const response = await authService.requestOTP({ phone: registeredPhone, userType: selectedUserType! });
      if (response.success) {
        setResendTimer(300);
      } else {
        throw new Error(response.message);
      }
    } catch (error: any) {
      toast.error(error.message || 'An error occurred while resending OTP');
    } finally {
      setIsVerifying(false);
    }
  };

  const stepLabels = ['Select Type', 'Register', 'Verify'];

  const renderStepContent = () => {
    switch (step) {
      case 1:
        return <UserTypeSelection type='register' onUserTypeChange={handleUserTypeChange} setStep={() => goToStep(2)} />;
      case 2:
        return (
          <div className='space-y-6'>
            <button
              onClick={() => goToStep(1)}
              className='group flex items-center gap-2 px-4 py-2 text-sm font-medium text-primaryColor transition-all hover:text-primaryColor bg-primaryColor-50 border border-primaryColor-200 rounded-lg hover:bg-primaryColor-100'
            >
              <ChevronLeft className='transition-transform group-hover:-translate-x-1' />
              Back to user selection
            </button>
            <RegisterForm selectedUserType={selectedUserType!} onSuccess={handleRegistrationSuccess} />
          </div>
        );
      case 3:
        return (
          <div className='space-y-6'>
            <button
              onClick={() => goToStep(2)}
              className='group flex items-center gap-2 px-4 py-2 text-sm font-medium text-primaryColor transition-all hover:text-primaryColor bg-primaryColor-50 border border-primaryColor-200 rounded-lg hover:bg-primaryColor-100'
            >
              <ChevronLeft className='transition-transform group-hover:-translate-x-1' />
              Back to registration
            </button>
            <div>
              <h2 className='text-3xl font-bold text-gray-900'>Verify your account</h2>
              <p className='mt-2 text-gray-600'>
                We've sent a verification code to your phone number
                {registeredPhone && <span className='font-medium'> {registeredPhone.slice(-4).padStart(10, 'x')}</span>}
              </p>
            </div>
            <div className='w-full'>
              <Form {...otpForm}>
                <form onSubmit={otpForm.handleSubmit(handleVerifyOtp)} className='space-y-6'>
                  <PrimaryOTPInput form={otpForm} name='otp' label='Enter the 4-digit verification code' required length={4} />
                  <div className='flex justify-between items-center text-sm'>
                    <div>
                      {resendTimer > 0 ? (
                        <span className='text-gray-600'>{`Resend OTP in ${Math.floor(resendTimer / 60)}:${String(resendTimer % 60).padStart(
                          2,
                          '0'
                        )}`}</span>
                      ) : (
                        <button
                          type='button'
                          className='text-primaryColor hover:underline'
                          onClick={handleResendOtp}
                          disabled={resendTimer > 0 || isVerifying}
                        >
                          Resend OTP
                        </button>
                      )}
                    </div>
                    <SubmitButton
                      disabled={isVerifying}
                      isSubmitting={isVerifying}
                      label='Verify Account'
                      submittingLabel='Verifying...'
                      className='w-40'
                    />
                  </div>
                </form>
              </Form>
            </div>
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <section className='relative overflow-hidden bg-gray-50'>
      <div className='max-container padding-container'>
        <div className='bg-white p-8 w-full'>
          <div className='w-full'>
            <StepIndicator currentStep={step} totalSteps={3} labels={stepLabels} />
            <AnimatePresence mode='wait'>
              <motion.div
                key={step}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.3 }}
              >
                {renderStepContent()}
              </motion.div>
            </AnimatePresence>
          </div>
        </div>
      </div>
    </section>
  );
};

export default RegisterPage;
