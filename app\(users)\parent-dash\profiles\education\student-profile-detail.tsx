import { useState } from 'react';
import { Calendar, Edit, GraduationCap, User } from 'lucide-react';
import { SlUserFemale } from 'react-icons/sl';
import { calculateAge, formatDateOfBirth } from '@/lib/date.utils';
import { cn } from '@/lib/utils';
import { IChildProfileDocument } from '@/server/services/profile.service';
import StudentForm from './student-form';
import { getImageUrl } from '@/lib/string.utils';

interface StudentProfileDetailProps {
  student: IChildProfileDocument;
}

const StudentProfileDetail = ({ student }: StudentProfileDetailProps) => {
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  return (
    <div className='relative bg-white rounded-2xl p-8 border border-gray-100 shadow-sm overflow-hidden'>
      <div className='absolute top-0 right-0 w-full h-full overflow-hidden pointer-events-none z-0'>
        <div className='absolute -right-20 -top-20 w-64 h-64 bg-red-50 rounded-full opacity-70'></div>
        <div className='absolute right-40 top-10 w-32 h-32 bg-orange-50 rounded-full opacity-80'></div>
        <div className='absolute -left-10 bottom-0 w-40 h-40 bg-red-50 rounded-full opacity-60'></div>
      </div>

      <div className='relative z-10'>
        <div className='flex flex-col md:flex-row gap-8 items-center md:items-start'>
          <div className='relative'>
            <div className='absolute inset-0 rounded-full bg-gradient-to-r from-red-400 to-red-600 blur-sm opacity-20 scale-110'></div>
            <div className='w-32 h-32 rounded-full overflow-hidden border-4 border-white shadow-lg relative'>
              {student.avatar ? (
                <img src={getImageUrl(student.avatar)} alt={student.fullName} className='w-full h-full object-cover' />
              ) : (
                <div className={cn('w-full h-full flex items-center justify-center', 'bg-gradient-to-br from-red-400 to-red-600')}>
                  <span className='text-3xl font-bold text-white'>{student.fullName.charAt(0)}</span>
                </div>
              )}
            </div>
            <div className='absolute bottom-1 right-1 w-8 h-8 rounded-full bg-white shadow-md flex items-center justify-center border-2 border-red-100'>
              <div className='w-full h-full rounded-full bg-gradient-to-r from-red-500 to-red-600'></div>
            </div>
          </div>

          <div className='flex-1'>
            <div className='flex flex-col md:flex-row md:items-center justify-between gap-4 mb-6'>
              <div>
                <h2 className='text-3xl font-bold text-gray-800 mb-1.5'>{student.fullName}</h2>
                <div className='flex items-center gap-2'>
                  <span className='px-2 py-0.5 bg-red-100 text-red-700 text-xs font-medium rounded-md'>
                    ID: {new Date(student.createdAt).getTime()}
                  </span>
                  <span className='px-2 py-0.5 bg-blue-100 text-blue-700 text-xs font-medium rounded-md'>Student</span>
                </div>
              </div>
              <button
                onClick={() => setIsEditModalOpen(true)}
                className={cn(
                  'flex items-center gap-2 text-white px-4 py-2 rounded-lg transition-colors shadow-md',
                  'bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700'
                )}
              >
                <Edit size={16} />
                <span>Edit Profile</span>
              </button>
            </div>

            <div className='grid grid-cols-1 md:grid-cols-3 gap-6'>
              <div className={cn('bg-white rounded-xl p-4 border border-gray-100 shadow-sm hover:shadow transition-all')}>
                <div className='flex items-center gap-3 mb-2'>
                  <div
                    className={cn('w-10 h-10 rounded-full flex items-center justify-center shadow-sm', 'bg-gradient-to-br from-red-500 to-red-600')}
                  >
                    <Calendar size={18} className='text-white' />
                  </div>
                  <div>
                    <p className='text-xs text-gray-500 uppercase tracking-wider'>Date of Birth</p>
                    <p className='text-sm font-medium text-gray-800'>{formatDateOfBirth(student.dateOfBirth)}</p>
                  </div>
                </div>
              </div>

              <div className={cn('bg-white rounded-xl p-4 border border-gray-100 shadow-sm hover:shadow transition-all')}>
                <div className='flex items-center gap-3 mb-2'>
                  <div
                    className={cn('w-10 h-10 rounded-full flex items-center justify-center shadow-sm', 'bg-gradient-to-br from-red-500 to-red-600')}
                  >
                    <GraduationCap size={18} className='text-white' />
                  </div>
                  <div>
                    <p className='text-xs text-gray-500 uppercase tracking-wider'>Age</p>
                    <p className='text-sm font-medium text-gray-800'>{calculateAge(student.dateOfBirth)} years</p>
                  </div>
                </div>
              </div>

              <div className={cn('bg-white rounded-xl p-4 border border-gray-100 shadow-sm hover:shadow transition-all')}>
                <div className='flex items-center gap-3 mb-2'>
                  <div
                    className={cn('w-10 h-10 rounded-full flex items-center justify-center shadow-sm', 'bg-gradient-to-br from-red-500 to-red-600')}
                  >
                    {student.gender === 'male' ? <User size={18} className='text-white' /> : <SlUserFemale size={18} className='text-white' />}
                  </div>
                  <div>
                    <p className='text-xs text-gray-500 uppercase tracking-wider'>Gender</p>
                    <p className='text-sm font-medium text-gray-800'>{student.gender}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Edit Student Modal */}
      <StudentForm isOpen={isEditModalOpen} onClose={() => setIsEditModalOpen(false)} initialValues={student} />
    </div>
  );
};

export default StudentProfileDetail;
