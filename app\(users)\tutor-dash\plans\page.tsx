import Image from 'next/image';
import { Check, CircleCheck, X, XCircle } from 'lucide-react';
import { cn } from '@/lib/utils';

const pricingPlans = [
  {
    name: 'Bronze',
    price: '5999',
    originalPrice: '7500',
    save: '20%',
    features: [true, true, true, true, true, false, false, false, false, false, false, false],
    coins: '700',
    icon: '/temp/wallet.png',
    validity: '3 Months',
  },
  {
    name: 'Gold',
    price: '8999',
    originalPrice: '10899',
    save: '17%',
    features: [true, true, true, true, true, true, true, false, false, false, false, false],
    coins: '2000',
    icon: '/temp/gift.png',
    recommended: true,
    validity: '6 Months',
  },
  {
    name: 'Platinum',
    price: '14999',
    originalPrice: '17599',
    save: '14%',
    features: [true, true, true, true, true, true, true, true, true, true, true, true],
    coins: '3500',
    icon: '/temp/truck.png',
    validity: '12 Months',
  },
];

const featuresList = [
  'Profile Ranking',
  '100% Accurate & Near by Leads',
  'Online Software Training',
  '100% Kyc Verified Users',
  'Lead compensation',
  'Online Platform For our Students',
  'Online Platform For other Students',
  'Teaching Certificate',
  'Get Suggested to Parents & they can call',
  'Profile Branding by running adds on our site',
  'Jobs In Institutes & Schools',
  'Crash Notes & Videos',
];

interface IPlanCard {
  plan: IPlan;
  featuresList: string[];
}

interface IPlan {
  name: string;
  price: string;
  originalPrice: string;
  features: boolean[];
  coins: string;
  icon: string;
  recommended?: boolean;
  validity: string;
}

const TutorPlansPage = () => {
  return (
    <div className='bg-[url("/temp/plan-bg.webp")] bg-cover bg-center bg-no-repeat rounded-3xl min-h-[650px] py-12 justify-center items-center flex'>
      <div className='flex justify-center'>
        <div className='w-[360px] bg-gray-50 rounded-t-3xl rounded-bl-3xl'>
          <div className='text-center h-48 bg-gray-100 flex justify-center items-center rounded-tl-3xl'>
            <Image src='/temp/logo.png' alt='Company Logo' width={150} height={50} />
          </div>
          <ul className='space-y-6 pl-12 pt-6 p-4'>
            {featuresList.map((feature, index) => (
              <li key={index} className='text-gray-700 flex items-center text-sm h-6'>
                {feature}
              </li>
            ))}
          </ul>
        </div>
        {pricingPlans.map((plan, index) => (
          <PlanCard key={index} plan={plan} featuresList={featuresList} />
        ))}
      </div>
    </div>
  );
};

const PlanCard: React.FC<IPlanCard> = ({ plan, featuresList }) => {
  return (
    <div
      className={cn(
        'flex flex-col w-60 items-center pb-6',
        plan.recommended ? 'bg-primaryColor text-white' : 'bg-white',
        plan.name === 'Platinum' ? 'rounded-tr-3xl rounded-br-3xl' : ''
      )}
    >
      <div className='h-48 flex flex-col items-center justify-center text-center gap-4 border-b w-full'>
        <div>
          <Image src={plan.icon} alt={`${plan.name} Icon`} width={64} height={64} />
        </div>
        <div className='space-y-2'>
          <h3 className='text-xl font-semibold'>{plan.name}</h3>
          <h4 className='text-base font-semibold'>{plan.coins} 🪙</h4>
          {plan.recommended && <span className='text-xs'>(Recommended)</span>}
        </div>
      </div>
      <div className='mb-4 w-full flex flex-col items-center pt-3'>
        {featuresList.map((feature, featureIndex) => (
          <div key={featureIndex} className='flex justify-start items-center h-12'>
            {plan.features[featureIndex] ? (
              <CircleCheck size={30} strokeWidth={1.75} className={plan.recommended ? 'text-white' : 'text-primaryColor'} />
            ) : (
              <XCircle size={30} strokeWidth={1.75} className={plan.recommended ? 'text-white' : 'text-gray-500'} />
            )}
          </div>
        ))}
      </div>

      <div className='mb-4'>
        <p className={cn('text-xs text-end line-through font-semibold', plan.recommended ? 'text-white' : 'text-red-500')}>₹{plan.originalPrice}</p>
        <p className={cn('text-3xl text-center font-semibold', plan.recommended ? 'text-white' : 'text-primaryColor')}>
          <sup>₹</sup>
          {plan.price}
        </p>
        <p className={cn('text-sm text-center', plan.recommended ? 'text-white' : 'text-gray-400')}>{plan.validity}</p>
      </div>

      <div className='text-center'>
        <button className={cn('btn-default-md !rounded-full', plan.recommended ? '!bg-white !text-primaryColor' : '')}>Choose Plan</button>
      </div>
    </div>
  );
};

export default TutorPlansPage;
