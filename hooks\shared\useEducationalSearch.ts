import { useState } from 'react';
import { useSearchEnquiries } from '@/hooks/enquiry.hooks';
import { ISearchResponse } from '@/server/services/enquiry.service';
import { IAPIResponse } from '@/types/api';

interface UseEducationalSearchProps {
  onSelectMatch?: (match: ISearchResponse['matches'][0]) => void;
  minSearchLength?: number;
}

interface UseEducationalSearchReturn {
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  selectedMatch: ISearchResponse['matches'][0] | null;
  setSelectedMatch: (match: ISearchResponse['matches'][0] | null) => void;
  searchResults: IAPIResponse<ISearchResponse> | undefined;
  isSearching: boolean;
  handleSelectMatch: (match: ISearchResponse['matches'][0]) => void;
  clearSearch: () => void;
  resetSearch: () => void;
}

export const useEducationalSearch = ({ onSelectMatch, minSearchLength = 2 }: UseEducationalSearchProps = {}): UseEducationalSearchReturn => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedMatch, setSelectedMatch] = useState<ISearchResponse['matches'][0] | null>(null);

  const { data: searchResults, isLoading: isSearching } = useSearchEnquiries(searchTerm.length >= minSearchLength ? searchTerm : '');

  const handleSelectMatch = (match: ISearchResponse['matches'][0]) => {
    setSelectedMatch(match);
    setSearchTerm('');
    onSelectMatch?.(match);
  };

  const clearSearch = () => {
    setSearchTerm('');
    setSelectedMatch(null);
  };

  const resetSearch = () => {
    setSearchTerm('');
    setSelectedMatch(null);
  };

  return { searchTerm, setSearchTerm, selectedMatch, setSelectedMatch, searchResults, isSearching, handleSelectMatch, clearSearch, resetSearch };
};
