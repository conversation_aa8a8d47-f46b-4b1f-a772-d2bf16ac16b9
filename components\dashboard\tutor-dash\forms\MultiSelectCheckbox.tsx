'use client';

import { ChevronDown } from 'lucide-react';
import { useState, useRef, useEffect } from 'react';

interface IMultiSelectCheckbox {
  options: Array<{ label: string; value: string }>;
  values: string[];
  onChange: (value: string[]) => void;
}

const MultiSelectCheckbox: React.FC<IMultiSelectCheckbox> = ({ options, values, onChange }) => {
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const handleCheckboxChange = (value: string) => {
    const newSelectedOptions = values.includes(value) ? values.filter((option) => option !== value) : [...values, value];
    onChange(newSelectedOptions);
  };

  const handleDropdownToggle = () => {
    setDropdownOpen((prev) => !prev);
  };

  const handleClickOutside = (event: MouseEvent) => {
    if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
      setDropdownOpen(false);
    }
  };

  useEffect(() => {
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const getSelectedLabels = () => {
    return options
      .filter((option) => values.includes(option.value))
      .map((option) => option.label)
      .join(', ');
  };

  return (
    <div ref={dropdownRef} className='relative inline-block w-full'>
      <div className='mt-1 p-3 border border-gray-300 rounded-md cursor-pointer' onClick={handleDropdownToggle}>
        <div className='flex justify-between items-center'>
          <span>{getSelectedLabels() || 'Select...'}</span>
          <ChevronDown size={16} className='text-gray-400' />
        </div>
      </div>
      {dropdownOpen && (
        <div className='absolute mt-1 w-full p-4 border-t-[3px] space-y-2 border-primaryColor-50 bg-white rounded-xl shadow-lg z-10'>
          {options.map((option) => (
            <label key={option.value} className='flex items-center cursor-pointer text-sm p-2 bg-primaryColor-50 rounded'>
              <input
                type='checkbox'
                checked={values.includes(option.value)}
                onChange={() => handleCheckboxChange(option.value)}
                className='mr-2 accent-primaryColor size-4'
              />
              {option.label}
            </label>
          ))}
        </div>
      )}
    </div>
  );
};

export default MultiSelectCheckbox;
