'use client';

import Link from 'next/link';
import Image from 'next/image';
import { usePathname } from 'next/navigation';
import logo from '@/public/images/logo.png';
import GoPro from './go-pro';
import { useSidebarStore } from '@/store/sidebarStore';
import { cn } from '@/lib/utils';
import { navigationLinks } from '@/constants/tutor-dash';

const Sidebar = () => {
  const { isSidebarOpen } = useSidebarStore();
  const pathname = usePathname();

  const isActiveLink = (href: string) => {
    if (href === '') return pathname === '/tutor-dash';
    return pathname.includes(href);
  };
  return (
    <aside
      className={cn(
        'sticky top-4 left-0 max-lg:hidden h-[calc(100vh-2rem)] overflow-hidden transition-all duration-300',
        isSidebarOpen ? 'w-80 opacity-100 mr-4' : 'w-0 opacity-0 m-0 p-0'
      )}
    >
      {/* Main Sidebar Container */}
      <div className='relative h-full bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 rounded-3xl shadow-2xl overflow-hidden border border-slate-700/50 sidebar-glow'>
        {/* Backgrounds */}
        <div className='absolute inset-0 bg-gradient-to-br from-emerald-600/10 via-teal-600/10 to-cyan-600/10 opacity-50'></div>
        <div className='absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-emerald-500/20 to-teal-500/20 rounded-full blur-3xl float-animation'></div>
        <div
          className='absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-teal-500/20 to-cyan-500/20 rounded-full blur-2xl float-animation'
          style={{ animationDelay: '2s' }}
        ></div>
        {/* Content */}
        <div className='relative h-full flex flex-col z-10'>
          <div className='p-6 pb-4'>
            <Link href='/' className='flex items-center justify-center group/logo'>
              <div className='relative p-3 bg-gradient-to-br from-white to-gray-100 rounded-2xl shadow-lg group-hover/logo:shadow-xl transition-all duration-300 group-hover/logo:scale-105'>
                <Image src={logo} alt='Logo' className='h-8 w-auto' />
                <div className='absolute inset-0 bg-gradient-to-br from-emerald-500/20 to-teal-500/20 rounded-2xl opacity-0 group-hover/logo:opacity-100 transition-opacity duration-300'></div>
              </div>
            </Link>
          </div>
          {/* Navigation */}
          <div className='flex-1 px-4 pb-4 overflow-hidden hover:overflow-y-auto sidebar-scrollbar'>
            <div className='space-y-1'>
              {navigationLinks.map((section, idx) => (
                <div key={idx} className='mb-4'>
                  <div className='px-4 py-2'>
                    <span className='text-white/70 text-xs font-semibold uppercase tracking-wider'>{section.label}</span>
                  </div>

                  <div className='space-y-1 px-2'>
                    {section.links?.map((link, linkIdx) => {
                      const isActive = isActiveLink(link.href);

                      return (
                        <Link
                          key={linkIdx}
                          href={`/tutor-dash/${link.href}`}
                          className={cn(
                            'group/link relative flex items-center gap-3 px-4 py-3 rounded-xl transition-all duration-300 overflow-hidden',
                            isActive
                              ? 'bg-gradient-to-r from-emerald-600 to-teal-600 text-white shadow-lg shadow-emerald-500/25 active-link-glow'
                              : 'text-white/70 hover:text-white hover:bg-white/10'
                          )}
                        >
                          {isActive && (
                            <div className='absolute left-0 top-0 bottom-0 w-1 bg-gradient-to-b from-emerald-400 to-teal-400 rounded-r-full shadow-lg shadow-emerald-400/50'></div>
                          )}
                          <div
                            className={cn(
                              'relative flex-shrink-0 p-2 rounded-lg transition-all duration-300',
                              isActive ? 'bg-white/20 shadow-lg' : 'bg-white/5 group-hover/link:bg-white/10'
                            )}
                          >
                            <link.icon
                              size={18}
                              strokeWidth={1.5}
                              className={cn('transition-transform duration-300', isActive ? 'scale-110' : 'group-hover/link:scale-105')}
                            />

                            {isActive && <div className='absolute inset-0 bg-white/20 rounded-lg blur-sm scale-150'></div>}
                          </div>
                          <span className={cn('font-medium text-sm capitalize transition-all duration-300', isActive ? 'font-semibold' : '')}>
                            {link.title}
                          </span>
                          <div className='absolute inset-0 bg-gradient-to-r from-emerald-600/0 via-teal-600/0 to-cyan-600/0 group-hover/link:from-emerald-600/10 group-hover/link:via-teal-600/10 group-hover/link:to-cyan-600/10 rounded-xl transition-all duration-300'></div>
                        </Link>
                      );
                    })}
                  </div>
                </div>
              ))}
            </div>
          </div>
          {/* GoPro Section */}
          <div className='px-6 py-4'>
            <GoPro />
          </div>
        </div>
      </div>
    </aside>
  );
};

export default Sidebar;
