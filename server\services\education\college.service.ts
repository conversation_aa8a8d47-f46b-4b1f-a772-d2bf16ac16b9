import apiClient from '@/server/apiClient';
import { IAPIResponse, IPagination } from '@/types/api';
import {
  CreateStreamInput,
  CreateDegreeLevelInput,
  CreateDegreeInput,
  CreateBranchInput,
  CreateCollegeSubjectInput,
  UpdateStreamInput,
  UpdateDegreeLevelInput,
  UpdateDegreeInput,
  UpdateBranchInput,
  UpdateCollegeSubjectInput,
} from '@/validation/schemas/education/college.schema';

// Types for College Streams
export interface IStreamDocument extends CreateStreamInput {
  _id: string;
  createdAt: string;
  updatedAt: string;
}

export interface IStreamsResponse {
  streams: IStreamDocument[];
  pagination: IPagination;
}

export interface IStreamResponse {
  stream: IStreamDocument;
}

// Types for Degree Levels
export interface IDegreeLevelDocument extends CreateDegreeLevelInput {
  _id: string;
  streamDetails?: IStreamDocument;
  createdAt: string;
  updatedAt: string;
}

export interface IDegreeLevelsResponse {
  degreeLevels: IDegreeLevelDocument[];
  pagination: IPagination;
}

export interface IDegreeLevelResponse {
  degreeLevel: IDegreeLevelDocument;
}

// Types for Degrees
export interface IDegreeDocument extends CreateDegreeInput {
  _id: string;
  degreeLevelDetails?: IDegreeLevelDocument;
  streamDetails?: IStreamDocument;
  createdAt: string;
  updatedAt: string;
}

export interface IDegreesResponse {
  degrees: IDegreeDocument[];
  pagination: IPagination;
}

export interface IDegreeResponse {
  degree: IDegreeDocument;
}

// Types for Branches
export interface IBranchDocument extends CreateBranchInput {
  _id: string;
  createdAt: string;
  updatedAt: string;
  degreeDetails?: IDegreeDocument;
}

export interface IBranchesResponse {
  branches: IBranchDocument[];
  pagination: IPagination;
}

export interface IBranchResponse {
  branch: IBranchDocument;
}

// Types for College Subjects
export interface ICollegeSubjectDocument extends CreateCollegeSubjectInput {
  _id: string;
  createdAt: string;
  updatedAt: string;
  branchDetails?: IBranchDocument;
}

export interface ICollegeSubjectsResponse {
  subjects: ICollegeSubjectDocument[];
  pagination: IPagination;
}

export interface ICollegeSubjectResponse {
  subject: ICollegeSubjectDocument;
}

// 1. College Stream Service
const streamService = {
  getAllStreams: async (params?: Record<string, any>) => {
    const queryParams = new URLSearchParams(params);
    return await apiClient.get<IStreamsResponse>(`/education/streams?${queryParams.toString()}`);
  },

  getStreamById: async (id: string) => {
    return await apiClient.get<IStreamResponse>(`/education/streams/${id}`);
  },

  createStream: async (data: CreateStreamInput) => {
    return await apiClient.post<IStreamResponse>('/education/streams', data);
  },

  updateStream: async (id: string, data: UpdateStreamInput) => {
    return await apiClient.patch<IStreamResponse>(`/education/streams/${id}`, data);
  },

  deleteStream: async (id: string) => {
    return await apiClient.delete<IAPIResponse>(`/education/streams/${id}`);
  },
};

// 2. Degree Level Service
const degreeLevelService = {
  getAllDegreeLevels: async (params?: Record<string, any>) => {
    const queryParams = new URLSearchParams(params);
    return await apiClient.get<IDegreeLevelsResponse>(`/education/degree-levels?${queryParams.toString()}`);
  },

  getDegreeLevelById: async (id: string) => {
    return await apiClient.get<IDegreeLevelResponse>(`/education/degree-levels/${id}`);
  },

  createDegreeLevel: async (data: CreateDegreeLevelInput) => {
    return await apiClient.post<IDegreeLevelResponse>('/education/degree-levels', data);
  },

  updateDegreeLevel: async (id: string, data: UpdateDegreeLevelInput) => {
    return await apiClient.patch<IDegreeLevelResponse>(`/education/degree-levels/${id}`, data);
  },

  deleteDegreeLevel: async (id: string) => {
    return await apiClient.delete<IAPIResponse>(`/education/degree-levels/${id}`);
  },
};

// 3. Degree Service
const degreeService = {
  getAllDegrees: async (params?: Record<string, any>) => {
    const queryParams = new URLSearchParams(params);
    return await apiClient.get<IDegreesResponse>(`/education/degrees?${queryParams.toString()}`);
  },

  getDegreeById: async (id: string) => {
    return await apiClient.get<IDegreeResponse>(`/education/degrees/${id}`);
  },

  createDegree: async (data: CreateDegreeInput) => {
    return await apiClient.post<IDegreeResponse>('/education/degrees', data);
  },

  updateDegree: async (id: string, data: UpdateDegreeInput) => {
    return await apiClient.patch<IDegreeResponse>(`/education/degrees/${id}`, data);
  },

  deleteDegree: async (id: string) => {
    return await apiClient.delete<IAPIResponse>(`/education/degrees/${id}`);
  },
};

// 4. Branch Service
const branchService = {
  getAllBranches: async (params?: Record<string, any>) => {
    const queryParams = new URLSearchParams(params);
    return await apiClient.get<IBranchesResponse>(`/education/branches?${queryParams.toString()}`);
  },

  getBranchById: async (id: string) => {
    return await apiClient.get<IBranchResponse>(`/education/branches/${id}`);
  },

  createBranch: async (data: CreateBranchInput) => {
    return await apiClient.post<IBranchResponse>('/education/branches', data);
  },

  updateBranch: async (id: string, data: UpdateBranchInput) => {
    return await apiClient.patch<IBranchResponse>(`/education/branches/${id}`, data);
  },

  deleteBranch: async (id: string) => {
    return await apiClient.delete<IAPIResponse>(`/education/branches/${id}`);
  },
};

// 5. College Subject Service
const collegeSubjectService = {
  getAllCollegeSubjects: async (params?: Record<string, any>) => {
    const queryParams = new URLSearchParams(params);
    return await apiClient.get<ICollegeSubjectsResponse>(`/education/college-subjects?${queryParams.toString()}`);
  },

  getCollegeSubjectById: async (id: string) => {
    return await apiClient.get<ICollegeSubjectResponse>(`/education/college-subjects/${id}`);
  },

  createCollegeSubject: async (data: CreateCollegeSubjectInput) => {
    return await apiClient.post<ICollegeSubjectResponse>('/education/college-subjects', data);
  },

  updateCollegeSubject: async (id: string, data: UpdateCollegeSubjectInput) => {
    return await apiClient.patch<ICollegeSubjectResponse>(`/education/college-subjects/${id}`, data);
  },

  deleteCollegeSubject: async (id: string) => {
    return await apiClient.delete<IAPIResponse>(`/education/college-subjects/${id}`);
  },
};

export { streamService, degreeLevelService, degreeService, branchService, collegeSubjectService };
