import { useQuery, useMutation } from '@tanstack/react-query';
import { QUERY_KEYS } from '@/lib/react-query/queryKeys';
import { queryClient } from '@/lib/react-query/queryClient';
import profileService from '@/server/services/profile.service';
import { CreateEducationDetailInput, UpdateEducationDetailInput } from '@/validation/schemas/parent/education-detail.schema';

// Child Profile Hooks
export function useCreateChildProfile() {
  return useMutation({
    mutationFn: (data: FormData) => profileService.childProfiles.createChildProfile(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.PROFILE.CHILD_PROFILES] });
    },
  });
}

export function useGetAllChildProfiles(params?: Record<string, any>, options = {}) {
  return useQuery({
    queryKey: [QUERY_KEYS.PROFILE.CHILD_PROFILES, params],
    queryFn: () => profileService.childProfiles.getAllChildProfiles(params),
    ...options,
  });
}

export function useGetChildProfileById(id: string, options = {}) {
  return useQuery({
    queryKey: [QUERY_KEYS.PROFILE.CHILD_PROFILE, id],
    queryFn: () => profileService.childProfiles.getChildProfileById(id),
    enabled: !!id,
    ...options,
  });
}

export function useUpdateChildProfile() {
  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: FormData }) => profileService.childProfiles.updateChildProfile(id, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.PROFILE.CHILD_PROFILES] });
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.PROFILE.CHILD_PROFILE, variables.id] });
    },
  });
}

export function useDeleteChildProfile() {
  return useMutation({
    mutationFn: (id: string) => profileService.childProfiles.deleteChildProfile(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.PROFILE.CHILD_PROFILES] });
    },
  });
}

// Education Detail Hooks
export function useCreateEducationDetail() {
  return useMutation({
    mutationFn: (data: CreateEducationDetailInput | FormData) => profileService.educationDetails.createEducationDetail(data),
    onSuccess: (_, variables) => {
      // Invalidate the education details for the specific child profile
      if ('childProfileId' in variables) {
        queryClient.invalidateQueries({
          queryKey: [QUERY_KEYS.PROFILE.EDUCATION_DETAILS, { childProfileId: variables.childProfileId }],
        });
      } else if (variables instanceof FormData && variables.get('childProfileId')) {
        const childProfileId = variables.get('childProfileId');
        queryClient.invalidateQueries({
          queryKey: [QUERY_KEYS.PROFILE.EDUCATION_DETAILS, { childProfileId }],
        });
      }
    },
  });
}

export function useGetAllEducationDetails(params?: Record<string, any>, options = {}) {
  return useQuery({
    queryKey: [QUERY_KEYS.PROFILE.EDUCATION_DETAILS, params],
    queryFn: () => profileService.educationDetails.getAllEducationDetails(params),
    ...options,
  });
}

export function useGetEducationDetailById(id: string, options = {}) {
  return useQuery({
    queryKey: [QUERY_KEYS.PROFILE.EDUCATION_DETAIL, id],
    queryFn: () => profileService.educationDetails.getEducationDetailById(id),
    enabled: !!id,
    ...options,
  });
}

export function useUpdateEducationDetail() {
  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateEducationDetailInput | FormData; childProfileId: string }) =>
      profileService.educationDetails.updateEducationDetail(id, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.PROFILE.EDUCATION_DETAIL, variables.id],
      });
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.PROFILE.EDUCATION_DETAILS, { childProfileId: variables.childProfileId }],
      });
    },
  });
}

export function useDeleteEducationDetail() {
  return useMutation({
    mutationFn: ({ id }: { id: string; childProfileId: string }) => profileService.educationDetails.deleteEducationDetail(id),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.PROFILE.EDUCATION_DETAILS, { childProfileId: variables.childProfileId }],
      });
    },
  });
}
