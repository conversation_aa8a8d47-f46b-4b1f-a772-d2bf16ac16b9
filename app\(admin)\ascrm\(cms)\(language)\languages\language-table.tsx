'use client';

import { useState, useEffect, useCallback } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { AdminDashActionLinks, HeadingBar, LimitFilter, MasterFilter, MasterTable, SectionWrapper } from '@/components/dashboard/ascrm/misc';
import { ErrorState } from '@/components/dashboard/ascrm/misc/StateUI';
import { filterTypeMaps, FilterTypeKey } from '@/constants';
import { FilterValues, FilterOption } from '@/types/api';
import { useGetAllLanguages, useDeleteLanguage, useGetLanguageById, useGetLanguageTypeById } from '@/hooks/education/language.hooks';
import { PrimaryFormModal, useModal, StatusToggle } from '@/components/dashboard/shared/misc';
import LanguageForm from './language-form';
import { languageService } from '@/server/services/education/language.service';
import { QUERY_KEYS } from '@/lib/react-query/queryKeys';

const LanguageTable: React.FC = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const addLanguageModal = useModal();
  const editLanguageModal = useModal();

  const [selectedLanguageId, setSelectedLanguageId] = useState<string | null>(null);
  const [filters, setFilters] = useState<FilterValues>({});
  const [languageTypeName, setLanguageTypeName] = useState<string>('');

  const languageTypeId = searchParams.get('languageType');

  if (!languageTypeId) return <ErrorState message='You are not authorized to view this page.' />;

  const { data: languageTypeData, error: languageTypeError } = useGetLanguageTypeById(languageTypeId || '');

  useEffect(() => {
    if (languageTypeData?.data?.languageType) {
      setLanguageTypeName(languageTypeData.data.languageType.name);
    }
  }, [languageTypeData]);

  const updateFiltersFromParams = useCallback(() => {
    const initialFilters: FilterValues = {};
    searchParams.forEach((value, key) => {
      if (key !== 'page' && key !== 'languageType') {
        initialFilters[key] = value;
      }
    });
    setFilters(initialFilters);
  }, [searchParams]);

  useEffect(() => {
    updateFiltersFromParams();
  }, [updateFiltersFromParams]);

  const page = Number(searchParams.get('page')) || 1;

  const queryParams = { page, languageType: languageTypeId, ...filters };

  const { data: languagesResponse, isLoading, error } = useGetAllLanguages(queryParams);
  const deleteItem = useDeleteLanguage();
  const {
    data: selectedLanguageData,
    isLoading: isLoadingSelectedLanguage,
    error: selectedLanguageError,
  } = useGetLanguageById(selectedLanguageId || '');

  const languages = languagesResponse?.data?.languages || [];
  const pagination = languagesResponse?.data?.pagination;

  const handleEditLanguage = (id: string) => {
    setSelectedLanguageId(id);
    editLanguageModal.open();
  };

  if (error || languageTypeError) {
    return <ErrorState message={error?.message || languageTypeError?.message || 'An unexpected error occurred.'} />;
  }

  const headers = ['Name', 'Status', 'Actions'];

  const rows = languages.map((language) => {
    return {
      id: language._id,
      values: [
        <p className='font-medium'>{language.name}</p>,
        <StatusToggle
          id={language._id}
          isActive={language.isActive}
          updateFn={languageService.updateLanguage}
          queryKey={[QUERY_KEYS.EDUCATION.LANGUAGES, [QUERY_KEYS.EDUCATION.LANGUAGES, { page, languageType: languageTypeId, ...filters }]]}
          entityName='Language'
        />,
        <AdminDashActionLinks
          delete
          editAction={() => handleEditLanguage(language._id)}
          basePath='/languages'
          id={language._id}
          itemName={language.name}
          deleteItem={() => deleteItem.mutateAsync(language._id)}
        />,
      ],
    };
  });

  const filterOptions: FilterOption[] = [
    {
      type: filterTypeMaps.input.key as FilterTypeKey,
      label: 'Language Name',
      key: 'name',
    },
  ];

  const handleApplyFilters = (newFilters: FilterValues) => {
    const params = new URLSearchParams(searchParams);
    Object.entries(newFilters).forEach(([key, value]) => {
      if (value && typeof value === 'string') {
        params.set(key, value);
      } else {
        params.delete(key);
      }
    });
    params.set('page', '1');
    router.push(`?${params.toString()}`);
  };

  const handleResetFilters = () => {
    const params = new URLSearchParams();
    params.set('languageType', languageTypeId);
    params.set('page', '1');
    router.push(`?${params.toString()}`);
  };

  return (
    <SectionWrapper>
      <HeadingBar
        title={languageTypeName ? `Languages of ${languageTypeName}` : 'Languages'}
        elements={[
          <LimitFilter
            key='limit'
            value={(filters.limit as string) || '10'}
            onChange={(value: string) => {
              handleApplyFilters({ limit: value });
            }}
          />,
          <MasterFilter
            key='filter'
            filterOptions={filterOptions}
            onApplyFilters={handleApplyFilters}
            onResetFilters={handleResetFilters}
            initialFilters={filters}
          />,
        ]}
        addAction={addLanguageModal.open}
      />
      <MasterTable headers={headers} rows={rows} isLoading={isLoading} pagination={pagination} />

      {/* Add Language Modal */}
      <PrimaryFormModal
        isOpen={addLanguageModal.isOpen}
        onClose={addLanguageModal.close}
        title={languageTypeName ? `Add New ${languageTypeName} Language` : 'Add New Language'}
        subtitle='Create a new language for your platform'
        size='sm'
        showSparkles={true}
      >
        <LanguageForm onSuccess={addLanguageModal.close} languageTypeId={languageTypeId} />
      </PrimaryFormModal>

      {/* Edit Language Modal */}
      <PrimaryFormModal
        isOpen={editLanguageModal.isOpen}
        onClose={() => {
          editLanguageModal.close();
          setSelectedLanguageId(null);
        }}
        title={languageTypeName ? `Edit ${languageTypeName} Language` : 'Edit Language'}
        subtitle='Update existing language details'
        size='sm'
        isLoading={isLoadingSelectedLanguage}
        loadingTitle='Loading Language Data'
        loadingMessage='Please wait while we fetch the language details...'
        isError={!!selectedLanguageError}
        errorMessage={selectedLanguageError?.message}
      >
        {selectedLanguageData?.data?.language && (
          <LanguageForm data={selectedLanguageData.data.language} onSuccess={editLanguageModal.close} languageTypeId={languageTypeId} />
        )}
      </PrimaryFormModal>
    </SectionWrapper>
  );
};

export default LanguageTable;
