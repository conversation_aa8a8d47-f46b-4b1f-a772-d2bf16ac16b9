'use client';

import { HeadingBar, MasterTable, SectionWrapper } from '@/components/dashboard/ascrm/misc';
import { Send } from 'lucide-react';
import { AiOutlineEye } from 'react-icons/ai';
import MasterSheet from './MasterSheet';
import { SheetContent, SheetContentAdd } from './TutorSheet';
import { users } from './tutors';
import { useSheet } from '@/hooks/users/admin';

const TutorList = () => {
  const viewSheet = useSheet();
  const addSheet = useSheet();

  const headers = ['Name', 'Date', 'Source', 'Allotment', 'Verification Status', 'AC Status'];

  const rows = users.map((user) => ({
    id: user.userId,
    values: [
      <div className='flex items-start gap-3 p-2 hover:bg-gray-50 rounded-lg'>
        <img
          src={`/icons/${user.gender === 'male' ? 'avatar-male.webp' : 'avatar-female.webp'}`}
          alt={`${user.name} profile`}
          className='size-10 rounded-full'
        />
        <div>
          <div className='text-sm font-medium'>{user.name}</div>
          <div className='text-xs text-gray-500'>{user.userId}</div>
        </div>
      </div>,
      <div className='space-y-1'>
        <p className='bg-primaryColor-100 text-primaryColor-700 hover:bg-primaryColor-200 rounded size-7 flex items-center justify-center'>
          <a href={user.referralLink} target='_blank' rel='noopener noreferrer'>
            <Send size={16} />
          </a>
        </p>
        <div className='text-xs text-gray-500'>15/6/2023, 4:15 PM</div>
      </div>,
      <div>
        <p className='text-sm'>{user.source}</p>
        <p className='text-xs text-gray-600'>{user.source === 'Referral' ? 'Lorem Ipsum' : ''}</p>
      </div>,
      user.verificationStatus.isVerified ? (
        <div className='text-center'>
          <p className='text-sm'>{user.verificationStatus.agentName}</p>
          <div className='text-xs text-gray-500'>15/6/2023, 4:15 PM</div>
        </div>
      ) : (
        <p className='text-center'>-</p>
      ),
      user.verificationStatus.isVerified ? (
        <div className='text-center'>
          <p className='text-sm'>{user.verificationStatus.agentName}</p>
          <div className='text-xs text-gray-500'>15/6/2023, 4:15 PM</div>
        </div>
      ) : (
        <div className='text-center'>
          <button className='text-amber-600'>Verify Now</button>
        </div>
      ),
      user.accountStatus === 'Active' ? (
        <div className='bg-primaryColor-50 text-primaryColor-600 w-fit py-1.5 px-6 rounded-3xl'>Active</div>
      ) : (
        <div className='bg-red-50 text-red-600 w-fit py-1.5 px-6 rounded-3xl'>Inactive</div>
      ),
      <button
        type='button'
        onClick={viewSheet.open}
        className='text-blue-600 hover:text-blue-700 size-8 flex items-center justify-center bg-blue-50 rounded-lg transition-colors'
      >
        <AiOutlineEye size={18} />
      </button>,
    ],
  }));
  return (
    <SectionWrapper>
      <HeadingBar title='Tutors Live Data' addAction={addSheet.open} />
      <MasterTable headers={headers} rows={rows} isLoading={false} pagination={undefined} />
      {/* Sheets */}
      <MasterSheet sheet={viewSheet} title='Tutor Details' subtitle='Active since 2 hours ago...'>
        <SheetContent />
      </MasterSheet>
      <MasterSheet sheet={addSheet} title='Add New Tutor' subtitle='Fill in the details below to add a new tutor'>
        <SheetContentAdd />
      </MasterSheet>
      {/* END OF SHEETS */}
    </SectionWrapper>
  );
};

export default TutorList;
