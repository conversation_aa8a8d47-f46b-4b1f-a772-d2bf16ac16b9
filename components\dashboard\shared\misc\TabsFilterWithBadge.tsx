'use client';

import { cn } from '@/lib/utils';
import { BadgeCheck } from 'lucide-react';
import { useState } from 'react';

interface ITabsFilterWithBadge {
  selectedTab: string;
  setSelectedTab: (tab: string) => void;
  tabCounts: { [key: string]: number };
  displayInFullWidth?: boolean;
  showNumbers?: boolean;
  labels?: { [key: string]: string };
  secondLevelLabels?: { [key: string]: { label: string; key: string } };
}

export const TabsFilterWithBadge: React.FC<ITabsFilterWithBadge> = ({
  selectedTab,
  setSelectedTab,
  tabCounts = {},
  displayInFullWidth = false,
  showNumbers = false,
  labels = {},
  secondLevelLabels = {},
}) => {
  return (
    <div
      className={cn(
        'hidden flex-col gap-2 rounded-3xl bg-muted text-muted-foreground max-w-full w-full items-start justify-start py-6 px-4 shrink-0 h-full',
        displayInFullWidth ? 'md:grid md:grid-cols-2 lg:flex lg:w-64' : 'md:flex md:w-64'
      )}
    >
      {Object.keys(tabCounts).map((tab) => (
        <button
          key={tab}
          type='button'
          onClick={() => setSelectedTab(tab)}
          className={cn(
            'w-full py-2.5 capitalize transition-all duration-300 pl-6 text-sm flex justify-between items-center',
            selectedTab === tab ? 'bg-white px-4 rounded-full font-medium text-black' : ''
          )}
        >
          {Object.keys(secondLevelLabels).length === 0 ? labels[tab] || tab : secondLevelLabels[tab]?.label}
          <span className='text-primaryColor border border-primaryColor size-5 text-sm flex items-center justify-center rounded-full shrink-0'>
            {showNumbers ? (
              tabCounts[tab]
            ) : tabCounts[tab] > 0 ? (
              <div className='size-6 rounded-full flex items-center justify-center bg-gradient-1 text-white'>
                <BadgeCheck size={26} strokeWidth={1.5} />
              </div>
            ) : (
              <div className='size-6 rounded-full flex items-center justify-center bg-gray-500 text-white'>
                <BadgeCheck size={26} strokeWidth={1.5} />
              </div>
            )}
          </span>
        </button>
      ))}
    </div>
  );
};

interface ITabsMobileFilterWithBadge {
  selectedTab: string;
  setSelectedTab: (tab: string) => void;
  tabCounts: { [key: string]: number };
  showNumbers?: boolean;
  labels?: { [key: string]: string };
  showSecondLevel?: boolean;
  secondLevelTabCounts?: { [key: string]: number };
  secondLevelLabels?: { [key: string]: { label: string; key: string } };
  selectedSecondTab?: string;
  setSelectedSecondTab?: (tab: string) => void;
  secondLevelShowNumbers?: boolean;
}

export const TabsMobileFilterWithBadge: React.FC<ITabsMobileFilterWithBadge> = ({
  selectedTab,
  setSelectedTab,
  tabCounts,
  showNumbers = false,
  labels = {},
  showSecondLevel = false,
  secondLevelTabCounts = {},
  secondLevelLabels = {},
  selectedSecondTab,
  setSelectedSecondTab,
  secondLevelShowNumbers,
}) => {
  const [isSecondLevelOpen, setIsSecondLevelOpen] = useState(false);

  const handleSecondLevelClick = (tab: string) => {
    // @ts-ignore
    tab && setSelectedSecondTab(tab);
    setIsSecondLevelOpen(false);
  };

  const handleMainTabClick = (tab: string) => {
    setSelectedTab(tab);
    if (tab === 'subjects-classes') {
      setIsSecondLevelOpen(true);
    } else {
      setIsSecondLevelOpen(false);
    }
  };

  return (
    <div className='fixed bottom-0 left-0 w-full z-50 md:hidden'>
      {isSecondLevelOpen && <div className='fixed inset-0 bg-black bg-opacity-50' onClick={() => setIsSecondLevelOpen(false)}></div>}
      <div className='relative flex flex-col w-full bg-white rounded-t-lg border-t border-gray-200 shadow-lg'>
        {showSecondLevel && isSecondLevelOpen && (
          <div className='flex flex-col gap-2 justify-around items-center mb-2 z-10 bg-white py-4 shadow-lg'>
            {Object.keys(secondLevelTabCounts).map((tab) => (
              <button
                key={tab}
                type='button'
                onClick={() => handleSecondLevelClick(tab)}
                className={cn(
                  'flex flex-col items-center justify-center py-2 transition-all duration-300 rounded-lg w-11/12 mx-auto',
                  selectedSecondTab === tab ? 'bg-primaryColor text-white' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                )}
              >
                <span className='text-sm'>
                  {secondLevelLabels[tab].label || tab} {secondLevelShowNumbers && `(${secondLevelTabCounts[tab]})`}
                </span>
                {!secondLevelShowNumbers && (
                  <div className={cn('mt-1 flex items-center justify-center', selectedSecondTab === tab ? 'text-white' : 'text-gray-400')}>
                    <BadgeCheck size={24} />
                  </div>
                )}
              </button>
            ))}
          </div>
        )}
        <div className='w-full flex justify-around items-center py-2 bg-gray-50'>
          {Object.keys(tabCounts).map((tab) => (
            <button
              key={tab}
              type='button'
              onClick={() => handleMainTabClick(tab)}
              className={cn(
                'flex flex-col items-center justify-center py-2 transition-all duration-300',
                selectedTab === tab ? 'text-primaryColor' : 'text-gray-600'
              )}
            >
              {showNumbers ? (
                <span className='text-sm'>{labels[tab] || tab}</span>
              ) : (
                <>
                  <span className='text-sm'>{labels[tab] || tab}</span>
                  <div className={cn('mt-1 flex items-center justify-center', selectedTab === tab ? 'text-primaryColor' : 'text-gray-400')}>
                    <BadgeCheck size={24} />
                  </div>
                </>
              )}
            </button>
          ))}
        </div>
      </div>
    </div>
  );
};
