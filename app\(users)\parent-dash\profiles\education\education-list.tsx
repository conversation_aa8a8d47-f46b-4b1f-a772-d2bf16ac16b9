import { useState } from 'react';
import { GraduationCap, Award, School, Plus } from 'lucide-react';
import EducationCard from './education-card';
import EducationForm from './education-form';
import { VisitorsLoader, VisitorsDeleteModal } from '@/components/dashboard/shared/misc';
import { IEducationDetailDocument } from '@/server/services/profile.service';
import { educationTypeMap } from '@/validation/schemas/parent/education.maps';
import { useGetAllEducationDetails, useDeleteEducationDetail } from '@/hooks/profile/profile.hooks';

interface EducationListProps {
  childProfileId: string;
}

const EducationList = ({ childProfileId }: EducationListProps) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingEducation, setEditingEducation] = useState<IEducationDetailDocument | undefined>(undefined);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [educationToDelete, setEducationToDelete] = useState<IEducationDetailDocument | null>(null);

  const { data: educationData, isLoading: isLoadingEducation } = useGetAllEducationDetails({ childProfileId });
  const deleteEducationDetail = useDeleteEducationDetail();

  const educationList = educationData?.data?.educationDetails || [];

  const schoolCount = educationList.filter((edu) => edu.educationType === educationTypeMap.school.key).length;
  const degreeCount = educationList.filter((edu) => edu.educationType === educationTypeMap.degree.key).length;
  const achievementCount = educationList.filter((edu) => edu.educationType === educationTypeMap.other.key).length;

  const handleEditClick = (education: IEducationDetailDocument) => {
    setEditingEducation(education);
    setIsModalOpen(true);
  };

  const handleDeleteClick = (education: IEducationDetailDocument) => {
    setEducationToDelete(education);
    setDeleteModalOpen(true);
  };

  if (isLoadingEducation) return <VisitorsLoader loaderType='skeleton' title='Education Details' message='Loading education details...' />;

  return (
    <div className='relative rounded-2xl overflow-hidden'>
      <div className='absolute inset-0 bg-white rounded-2xl'></div>

      <div className='relative z-10 p-6 border border-gray-100 rounded-2xl shadow-sm'>
        <div className='flex flex-col md:flex-row justify-between items-start md:items-center mb-6'>
          <div className='flex items-center gap-3'>
            <div className='w-10 h-10 rounded-lg bg-blue-500 flex items-center justify-center'>
              <GraduationCap className='text-white' size={20} />
            </div>

            <div>
              <h3 className='text-xl font-bold text-gray-800 mb-1'>Education Details</h3>
              <p className='text-sm text-gray-500'>Academic records and achievements</p>
            </div>
          </div>

          <div className='flex gap-3 mt-4 md:mt-0'>
            <button
              onClick={() => setIsModalOpen(true)}
              className='flex items-center gap-1.5 px-3 py-1.5 bg-red-50 text-red-600 hover:bg-red-100 rounded-lg text-xs font-medium transition-colors border border-red-100'
            >
              <Plus size={14} />
              <span>Add Education</span>
            </button>
            <div className='flex items-center gap-1.5 px-3 py-1.5 bg-blue-100 text-blue-700 rounded-full text-xs font-medium'>
              <School size={14} />
              <span>{schoolCount} Schools</span>
            </div>
            <div className='flex items-center gap-1.5 px-3 py-1.5 bg-green-100 text-green-700 rounded-full text-xs font-medium'>
              <GraduationCap size={14} />
              <span>{degreeCount} Degrees</span>
            </div>
            <div className='flex items-center gap-1.5 px-3 py-1.5 bg-purple-100 text-purple-700 rounded-full text-xs font-medium'>
              <Award size={14} />
              <span>{achievementCount} Achievements</span>
            </div>
          </div>
        </div>

        <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
          {educationList.map((education: IEducationDetailDocument, index: number) => (
            <EducationCard key={education._id} education={education} onEdit={handleEditClick} onDelete={() => handleDeleteClick(education)} />
          ))}
        </div>

        {educationList.length === 0 && (
          <div className='flex flex-col items-center justify-center py-10 text-center'>
            <div className='w-16 h-16 rounded-full bg-blue-100 flex items-center justify-center mb-4'>
              <GraduationCap className='text-blue-500' size={32} />
            </div>
            <h4 className='text-lg font-medium text-gray-800 mb-1'>No Education Records</h4>
            <p className='text-gray-500 max-w-md'>Add school details or achievements to keep track of educational progress</p>
            <button
              onClick={() => setIsModalOpen(true)}
              className='mt-4 flex items-center gap-2 px-4 py-2 bg-red-500 text-white rounded-lg text-sm font-medium hover:bg-red-600 transition-colors'
            >
              <Plus size={16} />
              <span>Add Education</span>
            </button>
          </div>
        )}
      </div>

      {/* Add/Edit Education Modal */}
      <EducationForm
        isOpen={isModalOpen}
        onClose={() => {
          setEditingEducation(undefined);
          setIsModalOpen(false);
        }}
        education={editingEducation}
        childProfileId={childProfileId}
      />

      {/* Delete Confirmation Modal */}
      <VisitorsDeleteModal
        isOpen={deleteModalOpen}
        onClose={() => {
          setDeleteModalOpen(false);
          setEducationToDelete(null);
        }}
        onDelete={() => deleteEducationDetail.mutateAsync({ id: educationToDelete?._id || '', childProfileId })}
        itemName={
          educationToDelete
            ? educationToDelete.educationType === 'school'
              ? `${educationToDelete.classDetails?.name || 'Class'} at ${educationToDelete.schoolName || 'School'}`
              : educationToDelete.educationType === 'degree'
              ? `${educationToDelete.degreeDetails?.name || 'Degree'} at ${educationToDelete.collegeName || 'College'}`
              : educationToDelete.certificateName || 'Achievement'
            : 'this record'
        }
      />
    </div>
  );
};

export default EducationList;
