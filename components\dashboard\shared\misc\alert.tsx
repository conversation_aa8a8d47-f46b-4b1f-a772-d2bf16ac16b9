'use client';

import { useEffect } from 'react';
import { FaExclamationCircle, FaCheckCircle, FaInfoCircle, FaTimes, FaExclamationTriangle } from 'react-icons/fa';
import { motion, AnimatePresence } from 'framer-motion';

interface AlertProps {
  type: 'error' | 'success' | 'info' | 'warning';
  title?: string;
  message: string;
  onClose?: () => void;
  className?: string;
  autoClose?: boolean;
  duration?: number;
  id?: string;
}

const Alert = ({ type, title, message, onClose, className = '', autoClose = false, duration = 5000, id = 'alert' }: AlertProps) => {
  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (autoClose) {
      timer = setTimeout(() => {
        onClose && onClose();
      }, duration);
    }
    return () => clearTimeout(timer);
  }, [autoClose, duration, onClose]);

  const alertStyles = {
    error: {
      container: 'bg-gradient-to-r from-red-50 to-red-100 border-l-4 border-red-500 shadow-md shadow-red-100',
      icon: <FaExclamationCircle className='h-6 w-6 text-red-500' />,
      title: 'text-red-800 font-semibold',
      message: 'text-red-700',
      closeIcon: 'text-red-400',
    },
    success: {
      container: 'bg-gradient-to-r from-green-50 to-green-100 border-l-4 border-green-500 shadow-md shadow-green-100',
      icon: <FaCheckCircle className='h-6 w-6 text-green-500' />,
      title: 'text-green-800 font-semibold',
      message: 'text-green-700',
      closeIcon: 'text-green-400',
    },
    info: {
      container: 'bg-gradient-to-r from-blue-50 to-blue-100 border-l-4 border-blue-500 shadow-md shadow-blue-100',
      icon: <FaInfoCircle className='h-6 w-6 text-blue-500' />,
      title: 'text-blue-800 font-semibold',
      message: 'text-blue-700',
      closeIcon: 'text-blue-400',
    },
    warning: {
      container: 'bg-gradient-to-r from-amber-50 to-amber-100 border-l-4 border-amber-500 shadow-md shadow-amber-100',
      icon: <FaExclamationTriangle className='h-6 w-6 text-amber-500' />,
      title: 'text-amber-800 font-semibold',
      message: 'text-amber-700',
      closeIcon: 'text-amber-400',
    },
  };

  const styles = alertStyles[type];

  return (
    <AnimatePresence mode='wait'>
      <motion.div
        key={id}
        initial={{ opacity: 0, y: -20 }}
        animate={{
          opacity: 1,
          y: 0,
          transition: {
            duration: 0.3,
            ease: 'easeOut',
          },
        }}
        exit={{
          opacity: 0,
          x: 50,
          transition: {
            duration: 0.2,
            ease: 'easeIn',
          },
        }}
        whileHover={{
          scale: 1.01,
          transition: { duration: 0.2 },
        }}
        className={`
          rounded-lg p-4 mb-4
          ${styles.container} 
          ${className}
        `}
        role='alert'
      >
        <div className='flex items-start'>
          <div className='flex-shrink-0 pt-0.5'>{styles.icon}</div>
          <div className='ml-3 w-full'>
            {title && (
              <div className='flex items-center'>
                <h3 className={`text-sm ${styles.title}`}>{title}</h3>
              </div>
            )}
            <div className={`mt-1 text-sm ${styles.message}`}>{message}</div>
          </div>
          {onClose && (
            <motion.button
              type='button'
              className={`ml-auto -mx-1.5 -my-1.5 rounded-full p-1.5 inline-flex items-center justify-center h-8 w-8 
                bg-opacity-20 hover:bg-opacity-30 focus:outline-none`}
              onClick={onClose}
              aria-label='Close'
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
            >
              <FaTimes className={`h-4 w-4 opacity-60 ${styles.closeIcon}`} />
            </motion.button>
          )}
        </div>
      </motion.div>
    </AnimatePresence>
  );
};

export default Alert;
