import React from 'react';
import { FormField, FormItem, FormControl, FormLabel, FormMessage } from '@/components/ui/form';
import { DatePicker } from '@/components/ui/date-picker';
import type { UseFormReturn, FieldValues, Path } from 'react-hook-form';

interface PrimaryDateInputProps<TFieldValues extends FieldValues> {
  form: UseFormReturn<TFieldValues>;
  name: Path<TFieldValues>;
  label: string;
  required?: boolean;
}

const PrimaryDateInput = <TFieldValues extends FieldValues>({ form, name, label, required = false }: PrimaryDateInputProps<TFieldValues>) => {
  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => (
        <FormItem className='w-full'>
          <FormLabel className='primary-label'>
            <span>{label}</span>
            {required && <span className='text-primaryColor ml-1'>*</span>}
          </FormLabel>
          <FormControl>
            <DatePicker date={field.value} setDate={field.onChange} />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
};

export default PrimaryDateInput;
