import apiClient from '@/server/apiClient';
import { IAPIResponse, IPagination } from '@/types/api';
import { CreateCourseTypeInput, CreateCourseInput, UpdateCourseTypeInput, UpdateCourseInput } from '@/validation/schemas/education/course.schema';

// Types for Course Types
export interface ICourseTypeDocument extends CreateCourseTypeInput {
  _id: string;
  createdAt: string;
  updatedAt: string;
}

export interface ICourseTypesResponse {
  courseTypes: ICourseTypeDocument[];
  pagination: IPagination;
}

export interface ICourseTypeResponse {
  courseType: ICourseTypeDocument;
}

// Types for Courses
export interface ICourseDocument extends CreateCourseInput {
  _id: string;
  courseTypeDetails?: ICourseTypeDocument;
  createdAt: string;
  updatedAt: string;
}

export interface ICoursesResponse {
  courses: ICourseDocument[];
  pagination: IPagination;
}

export interface ICourseResponse {
  course: ICourseDocument;
}

// 1. Course Type Service
const courseTypeService = {
  getAllCourseTypes: async (params?: Record<string, any>) => {
    const queryParams = new URLSearchParams(params);
    return await apiClient.get<ICourseTypesResponse>(`/education/course-types?${queryParams.toString()}`);
  },

  getCourseTypeById: async (id: string) => {
    return await apiClient.get<ICourseTypeResponse>(`/education/course-types/${id}`);
  },

  createCourseType: async (data: CreateCourseTypeInput) => {
    return await apiClient.post<ICourseTypeResponse>('/education/course-types', data);
  },

  updateCourseType: async (id: string, data: UpdateCourseTypeInput) => {
    return await apiClient.patch<ICourseTypeResponse>(`/education/course-types/${id}`, data);
  },

  deleteCourseType: async (id: string) => {
    return await apiClient.delete<IAPIResponse>(`/education/course-types/${id}`);
  },
};

// 2. Course Service
const courseService = {
  getAllCourses: async (params?: Record<string, any>) => {
    const queryParams = new URLSearchParams(params);
    return await apiClient.get<ICoursesResponse>(`/education/courses?${queryParams.toString()}`);
  },

  getCourseById: async (id: string) => {
    return await apiClient.get<ICourseResponse>(`/education/courses/${id}`);
  },

  createCourse: async (data: CreateCourseInput) => {
    return await apiClient.post<ICourseResponse>('/education/courses', data);
  },

  updateCourse: async (id: string, data: UpdateCourseInput) => {
    return await apiClient.patch<ICourseResponse>(`/education/courses/${id}`, data);
  },

  deleteCourse: async (id: string) => {
    return await apiClient.delete<IAPIResponse>(`/education/courses/${id}`);
  },
};

export { courseTypeService, courseService };
