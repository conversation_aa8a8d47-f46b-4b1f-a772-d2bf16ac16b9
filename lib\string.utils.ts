import { z } from 'zod';

import { baseURL } from '@/server';
import { ADMIN_DASHBOARD_PATH } from '@/constants/ascrm';

export const capitalizeWord = (str: string) => str.charAt(0).toUpperCase() + str.slice(1);

export const getImageUrl = (uri: string): string => {
  if (!uri) return '';
  const finalUri = uri.startsWith('/uploads') ? `${baseURL}${uri}` : uri;
  return finalUri;
};

export function normalizeDataBySchema<T extends Record<string, any>>(
  data: T | null | undefined,
  schema: z.ZodObject<any>,
  excludeFields: string[] = []
): Record<string, any> {
  if (!data) return {};

  const schemaShape = schema.shape;

  const normalized: Record<string, any> = {};

  Object.keys(schemaShape).forEach((key) => {
    if (data && key in data) {
      if (!excludeFields.includes(key)) {
        normalized[key] = data[key];
      }
    }
  });

  return normalized;
}

export const constructAdminUrl = (path: string) => `${ADMIN_DASHBOARD_PATH}/${path}`;
