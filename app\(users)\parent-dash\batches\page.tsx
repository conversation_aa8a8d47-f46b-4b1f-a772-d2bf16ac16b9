import { ParentDashLink } from '@/components/dashboard/parent-dash/misc';
import { SimpleTable } from '@/components/dashboard/parent-dash/misc';
import { Eye } from 'lucide-react';

const ParentBatchesPage = () => {
  return (
    <section className='bg-white p-6 rounded-3xl relative lg:min-h-[650px]'>
      <h2 className='text-base font-semibold m-4'>List of Batches</h2>
      <SimpleTable
        headers={['Batch ID', 'Batch Slot Date', 'Tutor Name', 'Location', 'Mode', 'Status', 'Action']}
        rows={[
          [
            'B001',
            '2024-07-22',
            '<PERSON>',
            'New York, USA',
            'Online',
            'Active',
            <ParentDashLink
              href='/batches/B001'
              className='bg-blue-100 text-blue-700 hover:bg-blue-200 rounded size-7 flex items-center justify-center cursor-pointer'
            >
              <Eye size={16} strokeWidth={1.5} />
            </ParentDashLink>,
          ],
          [
            'B002',
            '2024-07-23',
            '<PERSON>',
            'London, UK',
            'Offline',
            'Completed',
            <ParentDashLink
              href='/batches/B001'
              className='bg-blue-100 text-blue-700 hover:bg-blue-200 rounded size-7 flex items-center justify-center cursor-pointer'
            >
              <Eye size={16} strokeWidth={1.5} />
            </ParentDashLink>,
          ],
          [
            'B003',
            '2024-07-24',
            'Mike Johnson',
            'Sydney, Australia',
            'Online',
            'Pending',
            <ParentDashLink
              href='/batches/B001'
              className='bg-blue-100 text-blue-700 hover:bg-blue-200 rounded size-7 flex items-center justify-center cursor-pointer'
            >
              <Eye size={16} strokeWidth={1.5} />
            </ParentDashLink>,
          ],
          [
            'B004',
            '2024-07-25',
            'Emily Davis',
            'Toronto, Canada',
            'Offline',
            'Active',
            <ParentDashLink
              href='/batches/B001'
              className='bg-blue-100 text-blue-700 hover:bg-blue-200 rounded size-7 flex items-center justify-center cursor-pointer'
            >
              <Eye size={16} strokeWidth={1.5} />
            </ParentDashLink>,
          ],
        ]}
      />
    </section>
  );
};

export default ParentBatchesPage;
