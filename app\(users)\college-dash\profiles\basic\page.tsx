// TODO: REMOVE THIS USE CLIENT

'use client';

import Image from 'next/image';
import { Edit2, PencilOff, X } from 'lucide-react';
import PasswordInput from './PasswordInput';
import TutorBasicProfileForm from '@/components/dashboard/tutor-dash/forms/TutorBasicProfileForm';
import { useState } from 'react';
import AddressForm from '@/components/dashboard/tutor-dash/forms/AddressForm';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { cn } from '@/lib/utils';
import { KeyValueDisplay } from '@/components/dashboard/shared/misc';

const TutorBasicProfilePage = () => {
  const [editGeneralInfo, setEditGeneralInfo] = useState(false);
  const [editCurrentAddress, setEditCurrentAddress] = useState(false);
  const [editPermanentAddress, setEditPermanentAddress] = useState(false);

  const anyEditInfoActive = editGeneralInfo || editCurrentAddress || editPermanentAddress;

  const toggleEditItem = (setItem: React.Dispatch<React.SetStateAction<boolean>>) => {
    setItem((prevValue) => !prevValue);
  };

  return (
    <section className='flex gap-8 justify-start items-start'>
      {/* Left Column */}
      <div className='w-[25%] flex flex-col items-start gap-8'>
        {/* Avatar and Modify Button */}
        <div className='space-y-4 bg-white rounded-3xl p-6 relative'>
          <div className='absolute top-0 left-0 transform bg-gradient-1 uppercase tracking-wider text-white text-xs font-semibold px-5 py-1.5 rounded-tl-3xl rounded-br-3xl'>
            Institute
          </div>
          <div className='flex justify-center'>
            <Image
              alt='avatar'
              src='/temp/avatar.jpg'
              height={240}
              width={240}
              className='max-w-60 h-auto object-contain object-center rounded-3xl'
            />
          </div>
          <button className='btn-default-sm w-full justify-center !rounded-3xl'>Modify Picture</button>
        </div>
        {/* User Info */}
        <div className='bg-white rounded-3xl p-6 w-full'>
          <div className='flex items-start justify-between'>
            <h2 className='text-xl font-semibold mb-4'>About</h2>
            <div className='bg-red-500 py-1 px-4 text-white rounded-full animate-pulse text-sm shrink-0'>Promotion Exceed</div>
          </div>
          <div className='space-y-2'>
            <KeyValueDisplay label='User ID' value='123' />
            <KeyValueDisplay label='Referral Code' value='ABC1001' />
            <KeyValueDisplay label='Full Name' value='John Doe' />
            <KeyValueDisplay label='Email' value='<EMAIL>' />
            <KeyValueDisplay label='Primary Mobile' value='************' />
          </div>
        </div>
        {/* Password */}
        <div className='bg-white rounded-3xl p-6 w-full relative'>
          <h2 className='text-xl font-semibold mb-4'>Password</h2>
          <div className='flex flex-col gap-1 items-start justify-start'>
            <span className='font-semibold text-gray-500 text-sm'>Password Last Modified:</span>
            <span className='text-gray-500 text-sm'>1 Jan 2023, 08:47:44 PM</span>
          </div>

          <PasswordInput />
        </div>
      </div>

      {/* Right Column */}
      <div className='w-[75%] flex flex-col items-start gap-8'>
        {/* General Information */}
        <div className={cn('bg-white rounded-3xl p-6 w-full relative', anyEditInfoActive && !editGeneralInfo ? 'opacity-50' : '')}>
          <div className='flex items-start justify-between'>
            <h2 className='text-xl font-semibold mb-4'>General Information</h2>
            <button
              onClick={() => toggleEditItem(setEditGeneralInfo)}
              className='py-2 px-3 bg-primaryColor-50 text-primaryColor rounded transition-all duration-300'
            >
              {editGeneralInfo ? <PencilOff size={16} /> : <Edit2 size={16} />}
            </button>
          </div>
          {editGeneralInfo ? (
            <TutorBasicProfileForm />
          ) : (
            <div className='grid grid-cols-2 gap-4'>
              <KeyValueDisplay label='Alternative Mobile' value='************' />
              <KeyValueDisplay label='Primary WhatsApp' value='************' />
              <KeyValueDisplay label='Alternative WhatsApp' value='************' />
              <KeyValueDisplay label='Gender' value='Male' />
              <KeyValueDisplay label='Date of Birth' value='01/01/1990' />
              <KeyValueDisplay label='Location' value='Sector 18, Noida, 201301' />
              <KeyValueDisplay label='How do you came to know about us?' value='Google' />
            </div>
          )}
        </div>

        {/* Institute Information */}
        <div className={cn('bg-white rounded-3xl p-6 w-full relative', anyEditInfoActive && !editGeneralInfo ? 'opacity-50' : '')}>
          <div className='flex items-start justify-between'>
            <h2 className='text-xl font-semibold mb-4'>Institute Information</h2>
            <button
              onClick={() => toggleEditItem(setEditGeneralInfo)}
              className='py-2 px-3 bg-primaryColor-50 text-primaryColor rounded transition-all duration-300'
            >
              {editGeneralInfo ? <PencilOff size={16} /> : <Edit2 size={16} />}
            </button>
          </div>
          {editGeneralInfo ? (
            <TutorBasicProfileForm />
          ) : (
            <div className='grid grid-cols-2 gap-4'>
              <KeyValueDisplay label='Institute Name' value='Daffodil Public School' />
              <KeyValueDisplay label='Registration No' value='************' />
              <KeyValueDisplay label='GST' value='************' />
              <KeyValueDisplay label='Owner Name' value='ABC' />
              <KeyValueDisplay label='Institute Registration Date' value='01/01/1990' />
              <KeyValueDisplay label='Location' value='Sector 18, Noida, 201301' />
              <KeyValueDisplay label='Owner Contact Number' value='4442223330' />
              <KeyValueDisplay label='Owner Email' value='Google' />
            </div>
          )}
        </div>

        {/* Institute Address */}
        <div className={cn('bg-white rounded-3xl p-6 w-full relative', anyEditInfoActive && !editCurrentAddress ? 'opacity-50' : '')}>
          <div className='flex items-start justify-between'>
            <h2 className='text-xl font-semibold mb-4'>Institute Address</h2>
            <button
              onClick={() => toggleEditItem(setEditCurrentAddress)}
              className='py-2 px-3 bg-primaryColor-50 text-primaryColor rounded transition-all duration-300'
            >
              {editCurrentAddress ? <PencilOff size={16} /> : <Edit2 size={16} />}
            </button>
          </div>
          {editCurrentAddress ? (
            <AddressForm />
          ) : (
            <div className='grid grid-cols-2 gap-4'>
              <KeyValueDisplay label='House No' value='123' />
              <KeyValueDisplay label='Locality' value='XYZ Street' />
              <KeyValueDisplay label='Landmark' value='Near ABC Park' />
              <KeyValueDisplay label='Area PIN code' value='123456' />
              <KeyValueDisplay label='City' value='Some City' />
              <KeyValueDisplay label='District' value='Some District' />
              <KeyValueDisplay label='State' value='Some State' />
            </div>
          )}
        </div>
      </div>
    </section>
  );
};

export default TutorBasicProfilePage;
