import { cn } from '@/lib/utils';

import { LucideIcon } from 'lucide-react';

interface ITabsFilter {
  selectedTab: string;
  setSelectedTab: (tab: string) => void;
  tabCounts: { [key: string]: number };
  label?: string;
}

interface IMobileTabsFilter extends ITabsFilter {
  icon: LucideIcon;
  mobileTabs: string[];
}

// Filter Component for Desktop
const TabsFilter: React.FC<ITabsFilter> = ({ selectedTab, setSelectedTab, tabCounts, label = '' }) => {
  return (
    <div className='hidden lg:flex flex-col gap-2 rounded-3xl bg-muted text-muted-foreground max-w-full w-full items-start justify-start md:w-64 py-6 px-4 shrink-0 h-full'>
      {Object.keys(tabCounts).map((tab) => (
        <button
          key={tab}
          type='button'
          onClick={() => setSelectedTab(tab)}
          className={cn(
            'w-full py-2.5 capitalize transition-all duration-300 pl-6 text-sm flex justify-between items-center',
            selectedTab === tab ? 'bg-white px-4 rounded-full font-medium text-black' : ''
          )}
        >
          {tab} {label}
          <span className='text-primaryColor border border-primaryColor size-5 text-sm flex items-center justify-center rounded-full shrink-0'>
            {tabCounts[tab] || 0}
          </span>
        </button>
      ))}
    </div>
  );
};

// Filter Component for Mobile
const MobileTabsFilter: React.FC<IMobileTabsFilter> = ({ selectedTab, setSelectedTab, tabCounts, icon: Icon, mobileTabs }) => {
  const tabsToDisplay = mobileTabs.filter((tab) => Object.keys(tabCounts).includes(tab));

  return (
    <div className='fixed bottom-0 left-0 z-50 w-full h-16 bg-white rounded-full lg:hidden'>
      <div className='flex justify-around items-center h-full'>
        {tabsToDisplay.map((tab) => (
          <button
            key={tab}
            type='button'
            onClick={() => setSelectedTab(tab)}
            className={cn(
              'flex flex-col items-center justify-center text-sm',
              selectedTab === tab ? 'text-primaryColor font-medium' : 'text-gray-500'
            )}
          >
            <Icon size={20} strokeWidth={1.5} className={selectedTab === tab ? 'text-primaryColor' : ''} />
            <span className='capitalize tracking-wide'>{tab}</span>
          </button>
        ))}
      </div>
    </div>
  );
};

export { TabsFilter, MobileTabsFilter };
