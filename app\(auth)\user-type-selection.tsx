import type React from 'react';
import { useState } from 'react';
import Image from 'next/image';
import { motion } from 'framer-motion';
import { BadgeCheck } from 'lucide-react';
import { cn } from '@/lib/utils';
import { IUserTypeMap, userTypeMap } from '@/validation/schemas/maps';

interface UserType {
  name: IUserTypeMap;
  label: string;
  image: any;
  bgColor: string;
}

const userTypeColors = { student: 'bg-blue-50', tutor: 'bg-green-50', institute: 'bg-red-50', school: 'bg-yellow-50', college: 'bg-purple-50' };

const userTypes: UserType[] = Object.entries(userTypeMap).map(([key, value]) => ({
  name: key as IUserTypeMap,
  label: value.label,
  image: `/temp/${key}.png`,
  bgColor: userTypeColors[key as keyof typeof userTypeColors],
}));

interface UserTypeSelectionProps {
  onUserTypeChange: (userType: IUserTypeMap) => void;
  setStep: () => void;
  type?: string;
}

export const UserTypeSelection: React.FC<UserTypeSelectionProps> = ({ onUserTypeChange, setStep, type = 'login' }) => {
  const [selectedUserType, setSelectedUserType] = useState<IUserTypeMap>();

  const handleSelection = (userType: IUserTypeMap) => {
    setSelectedUserType(userType);
    onUserTypeChange(userType);
  };

  return (
    <div className='space-y-8'>
      <h2 className='text-3xl font-bold text-gray-900'>How would you like to {type}?</h2>
      <div className='grid grid-cols-2 lg:grid-cols-3 gap-4'>
        {userTypes.map((user, index) => (
          <motion.div key={user.name} whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }} className='relative'>
            <label
              className={cn(
                'flex flex-col items-center p-4 rounded-xl cursor-pointer transition-all duration-200',
                user.bgColor,
                selectedUserType === user.name ? 'ring-2 ring-primaryColor' : 'hover:shadow-lg',
                index === 0 ? 'col-span-2' : ''
              )}
              onClick={() => handleSelection(user.name)}
            >
              <input
                type='radio'
                name='userType'
                value={user.name}
                checked={selectedUserType === user.name}
                onChange={() => {}}
                className='sr-only'
              />
              <div>
                <Image src={'/temp/students.png'} alt={user.label} width={100} height={100} className='w-24 h-24 object-contain mb-4' />
                {selectedUserType === user.name && (
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    className='absolute -top-2 -right-2 bg-primaryColor text-white rounded-full p-1'
                  >
                    <BadgeCheck size={20} />
                  </motion.div>
                )}
              </div>
              <h3 className='text-lg font-semibold text-gray-900'>{user.label}</h3>
              <p className='text-sm text-gray-600 text-center mt-2'>Sign in to access your {user.label.toLowerCase()} dashboard</p>
            </label>
          </motion.div>
        ))}
      </div>
      <motion.button
        className='btn-default w-full mt-6'
        disabled={!selectedUserType}
        onClick={setStep}
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
      >
        Continue
      </motion.button>
    </div>
  );
};
