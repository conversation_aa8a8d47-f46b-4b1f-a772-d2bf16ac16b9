'use client';

import { useState, useEffect, useCallback } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { AdminDashActionLinks, HeadingBar, LimitFilter, MasterFilter, MasterTable, SectionWrapper } from '@/components/dashboard/ascrm/misc';
import { ErrorState } from '@/components/dashboard/ascrm/misc/StateUI';
import { filterTypeMaps, FilterTypeKey } from '@/constants';
import { FilterValues, FilterOption } from '@/types/api';
import { useGetAllDegrees, useDeleteDegree, useGetDegreeById, useGetDegreeLevelById } from '@/hooks/education/college.hooks';
import { AdminViewButton, PrimaryFormModal, useModal, StatusToggle } from '@/components/dashboard/shared/misc';
import DegreeForm from './degree-form';
import { degreeService } from '@/server/services/education/college.service';
import { QUERY_KEYS } from '@/lib/react-query/queryKeys';

const DegreeTable: React.FC = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const addDegreeModal = useModal();
  const editDegreeModal = useModal();

  const [selectedDegreeId, setSelectedDegreeId] = useState<string | null>(null);
  const [filters, setFilters] = useState<FilterValues>({});
  const [streamName, setStreamName] = useState<string>('');
  const [degreeLevelName, setDegreeLevelName] = useState<string>('');

  const degreeLevelId = searchParams.get('degreeLevel');

  if (!degreeLevelId) return <ErrorState message='You are not authorized to view this page.' />;

  const { data: degreeLevelData, error: degreeLevelError } = useGetDegreeLevelById(degreeLevelId || '');

  useEffect(() => {
    if (degreeLevelData?.data?.degreeLevel) {
      setDegreeLevelName(degreeLevelData.data.degreeLevel.name);
      setStreamName(degreeLevelData.data.degreeLevel.streamDetails?.name || '');
    }
  }, [degreeLevelData]);

  const updateFiltersFromParams = useCallback(() => {
    const initialFilters: FilterValues = {};
    searchParams.forEach((value, key) => {
      if (key !== 'page' && key !== 'degreeLevel') {
        initialFilters[key] = value;
      }
    });
    setFilters(initialFilters);
  }, [searchParams]);

  useEffect(() => {
    updateFiltersFromParams();
  }, [updateFiltersFromParams]);

  const page = Number(searchParams.get('page')) || 1;

  const queryParams = { page, degreeLevel: degreeLevelId, ...filters };

  const { data: degreesResponse, isLoading, error } = useGetAllDegrees(queryParams);
  const deleteItem = useDeleteDegree();
  const { data: selectedDegreeData, isLoading: isLoadingSelectedDegree, error: selectedDegreeError } = useGetDegreeById(selectedDegreeId || '');

  const degrees = degreesResponse?.data?.degrees || [];
  const pagination = degreesResponse?.data?.pagination;

  const handleEditDegree = (id: string) => {
    setSelectedDegreeId(id);
    editDegreeModal.open();
  };

  if (error || degreeLevelError) {
    return <ErrorState message={error?.message || degreeLevelError?.message || 'An unexpected error occurred.'} />;
  }

  const headers = ['Name', 'Status', 'Actions'];

  const rows = degrees.map((degree) => {
    return {
      id: degree._id,
      values: [
        <p className='font-medium'>{degree.name}</p>,
        <StatusToggle
          id={degree._id}
          isActive={degree.isActive}
          updateFn={degreeService.updateDegree}
          queryKey={[QUERY_KEYS.EDUCATION.DEGREES, [QUERY_KEYS.EDUCATION.DEGREES, { page, degreeLevel: degreeLevelId, ...filters }]]}
          entityName='Degree'
        />,
        <AdminDashActionLinks
          delete
          editAction={() => handleEditDegree(degree._id)}
          basePath='/degrees'
          id={degree._id}
          itemName={degree.name}
          deleteItem={() => deleteItem.mutateAsync(degree._id)}
          manualAction={[<AdminViewButton variant='icon' href={`/branches?degree=${degree._id}`} label={`View ${degree.name} Branches`} />]}
        />,
      ],
    };
  });

  const filterOptions: FilterOption[] = [
    {
      type: filterTypeMaps.input.key as FilterTypeKey,
      label: 'Degree Name',
      key: 'name',
    },
  ];

  const handleApplyFilters = (newFilters: FilterValues) => {
    const params = new URLSearchParams(searchParams);
    Object.entries(newFilters).forEach(([key, value]) => {
      if (value && typeof value === 'string') {
        params.set(key, value);
      } else {
        params.delete(key);
      }
    });
    params.set('page', '1');
    router.push(`?${params.toString()}`);
  };

  const handleResetFilters = () => {
    const params = new URLSearchParams();
    params.set('degreeLevel', degreeLevelId);
    params.set('page', '1');
    router.push(`?${params.toString()}`);
  };

  const title =
    degreeLevelName && streamName
      ? `${degreeLevelName} Degrees in ${streamName} Stream`
      : degreeLevelName
      ? `${degreeLevelName} Degrees`
      : streamName
      ? `Degrees in ${streamName} Stream`
      : 'College Degrees';

  return (
    <SectionWrapper>
      <HeadingBar
        title={title}
        elements={[
          <LimitFilter
            key='limit'
            value={(filters.limit as string) || '10'}
            onChange={(value: string) => {
              handleApplyFilters({ limit: value });
            }}
          />,
          <MasterFilter
            key='filter'
            filterOptions={filterOptions}
            onApplyFilters={handleApplyFilters}
            onResetFilters={handleResetFilters}
            initialFilters={filters}
          />,
        ]}
        addAction={addDegreeModal.open}
      />
      <MasterTable headers={headers} rows={rows} isLoading={isLoading} pagination={pagination} />

      {/* Add Degree Modal */}
      <PrimaryFormModal
        isOpen={addDegreeModal.isOpen}
        onClose={addDegreeModal.close}
        title={`Add New ${degreeLevelName} Degree`}
        subtitle='Create a new college degree for your platform'
        size='sm'
        showSparkles={true}
      >
        <DegreeForm onSuccess={addDegreeModal.close} degreeLevelId={degreeLevelId} />
      </PrimaryFormModal>

      {/* Edit Degree Modal */}
      <PrimaryFormModal
        isOpen={editDegreeModal.isOpen}
        onClose={() => {
          editDegreeModal.close();
          setSelectedDegreeId(null);
        }}
        title={`Edit ${degreeLevelName} Degree`}
        subtitle='Update existing college degree details'
        size='sm'
        isLoading={isLoadingSelectedDegree}
        loadingTitle='Loading Degree Data'
        loadingMessage='Please wait while we fetch the degree details...'
        isError={!!selectedDegreeError}
        errorMessage={selectedDegreeError?.message}
      >
        {selectedDegreeData?.data?.degree && <DegreeForm data={selectedDegreeData.data.degree} onSuccess={editDegreeModal.close} />}
      </PrimaryFormModal>
    </SectionWrapper>
  );
};

export default DegreeTable;
