import React from 'react';
import { cn } from '@/lib/utils';

interface CancelButtonProps {
  onClose: () => void;
  size?: 'sm' | 'md' | 'lg';
}

const sizeStyles = {
  sm: 'px-3 py-1.5 text-sm',
  md: 'px-5 py-2.5',
  lg: 'px-6 py-3 text-lg',
};

const CancelButton: React.FC<CancelButtonProps> = ({ onClose, size = 'md' }) => {
  return (
    <button
      type='button'
      onClick={onClose}
      className={cn('border-2 border-gray-200 rounded-xl text-gray-700 hover:bg-gray-50 transition-colors font-medium', sizeStyles[size])}
    >
      Cancel
    </button>
  );
};

export default CancelButton;
