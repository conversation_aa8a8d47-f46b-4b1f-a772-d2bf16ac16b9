'use client';

import { FormField, FormItem, FormControl, Form, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { zodResolver } from '@hookform/resolvers/zod';
import { Loader2 } from 'lucide-react';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { addStudentEducationSchema } from '@/lib/validations/parent-dash/zod';

const StudentEducationForm = () => {
  const form = useForm<z.infer<typeof addStudentEducationSchema>>({
    resolver: zodResolver(addStudentEducationSchema),
    defaultValues: {
      studentId: '',
      dob: '',
      gender: 'Male',
      board: '',
      branch: '',
      class: '',
      degree: '',
      schoolName: '',
      schoolLocation: '',
      startYear: new Date().getFullYear(),
      endYear: new Date().getFullYear(),
      scoreType: 'In Percentage',
      obtainedValue: '',
      maxValue: '',
    },
  });

  const [isPursuing, setIsPursuing] = useState(false);

  async function onSubmit(values: z.infer<typeof addStudentEducationSchema>) {
    console.log(values);
  }

  return (
    <div>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 items-start gap-6'>
          {/* Student ID */}
          <FormField
            control={form.control}
            name='studentId'
            render={({ field }) => (
              <FormItem className='w-full'>
                <FormLabel className='primary-label'>
                  <span>Student ID</span>
                  <span className='text-primaryColor'>*</span>
                </FormLabel>
                <FormControl>
                  <Input className='primary-input' type='text' placeholder='Enter student ID' {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          {/* Date of Birth */}
          <FormField
            control={form.control}
            name='dob'
            render={({ field }) => (
              <FormItem className='w-full'>
                <FormLabel className='primary-label'>
                  <span>Date of Birth</span>
                  <span className='text-primaryColor'>*</span>
                </FormLabel>
                <FormControl>
                  <Input className='primary-input' type='date' placeholder='Enter date of birth' {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          {/* Gender */}
          <FormField
            control={form.control}
            name='gender'
            render={({ field }) => (
              <FormItem className='w-full'>
                <FormLabel className='primary-label'>
                  <span>Gender</span>
                  <span className='text-primaryColor'>*</span>
                </FormLabel>
                <FormControl>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder='Select gender' />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value='Male'>Male</SelectItem>
                      <SelectItem value='Female'>Female</SelectItem>
                      <SelectItem value='Other'>Other</SelectItem>
                    </SelectContent>
                  </Select>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          {/* Board */}
          <FormField
            control={form.control}
            name='board'
            render={({ field }) => (
              <FormItem className='w-full'>
                <FormLabel className='primary-label'>
                  <span>Board</span>
                  <span className='text-primaryColor'>*</span>
                </FormLabel>
                <FormControl>
                  <Input className='primary-input' type='text' placeholder='Enter the board' {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          {/* Branch */}
          <FormField
            control={form.control}
            name='branch'
            render={({ field }) => (
              <FormItem className='w-full'>
                <FormLabel className='primary-label'>
                  <span>Branch</span>
                  <span className='text-primaryColor'>*</span>
                </FormLabel>
                <FormControl>
                  <Input className='primary-input' type='text' placeholder='Enter the branch' {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          {/* Class */}
          <FormField
            control={form.control}
            name='class'
            render={({ field }) => (
              <FormItem className='w-full'>
                <FormLabel className='primary-label'>
                  <span>Class</span>
                  <span className='text-primaryColor'>*</span>
                </FormLabel>
                <FormControl>
                  <Input className='primary-input' type='text' placeholder='Enter the class' {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          {/* Degree */}
          <FormField
            control={form.control}
            name='degree'
            render={({ field }) => (
              <FormItem className='w-full'>
                <FormLabel className='primary-label'>
                  <span>Degree</span>
                  <span className='text-primaryColor'>*</span>
                </FormLabel>
                <FormControl>
                  <Input className='primary-input' type='text' placeholder='Enter the degree' {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          {/* School Name */}
          <FormField
            control={form.control}
            name='schoolName'
            render={({ field }) => (
              <FormItem className='w-full'>
                <FormLabel className='primary-label'>
                  <span>School Name</span>
                  <span className='text-primaryColor'>*</span>
                </FormLabel>
                <FormControl>
                  <Input className='primary-input' type='text' placeholder='Enter the school name' {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          {/* School Location */}
          <FormField
            control={form.control}
            name='schoolLocation'
            render={({ field }) => (
              <FormItem className='w-full'>
                <FormLabel className='primary-label'>
                  <span>School Location</span>
                  <span className='text-primaryColor'>*</span>
                </FormLabel>
                <FormControl>
                  <Input className='primary-input' type='text' placeholder='Enter the school location' {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          {/* Start Year */}
          <FormField
            control={form.control}
            name='startYear'
            render={({ field }) => (
              <FormItem className='w-full'>
                <FormLabel className='primary-label'>
                  <span>Start Year</span>
                  <span className='text-primaryColor'>*</span>
                </FormLabel>
                <FormControl>
                  <Input className='primary-input' type='number' placeholder='Enter the start year' {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          {/* End Year */}
          <FormField
            control={form.control}
            name='endYear'
            render={({ field }) => (
              <FormItem className='w-full'>
                <FormLabel className='primary-label'>
                  <span>End Year</span>
                  <span className='text-primaryColor'>*</span>
                </FormLabel>
                <FormControl>
                  <Input className='primary-input' type='number' placeholder='Enter the end year' {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          {/* Score Type */}
          <FormField
            control={form.control}
            name='scoreType'
            render={({ field }) => (
              <FormItem className='w-full'>
                <FormLabel className='primary-label'>
                  <span>Score Type</span>
                  <span className='text-primaryColor'>*</span>
                </FormLabel>
                <FormControl>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder='Select score type' />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value='In Marks'>In Marks</SelectItem>
                      <SelectItem value='In Percentage'>In Percentage</SelectItem>
                      <SelectItem value='In CGPA'>In CGPA</SelectItem>
                    </SelectContent>
                  </Select>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          {/* Obtained Value */}
          <FormField
            control={form.control}
            name='obtainedValue'
            render={({ field }) => (
              <FormItem className='w-full'>
                <FormLabel className='primary-label'>
                  <span>Obtained Value</span>
                  <span className='text-primaryColor'>*</span>
                </FormLabel>
                <FormControl>
                  <Input className='primary-input' type='text' placeholder='Enter obtained value' {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />{' '}
          {/* Maximum Value */}
          <FormField
            control={form.control}
            name='maxValue'
            render={({ field }) => (
              <FormItem className='w-full'>
                <FormLabel className='primary-label'>
                  <span>Maximum Value</span>
                  <span className='text-primaryColor'>*</span>
                </FormLabel>
                <FormControl>
                  <Input className='primary-input' type='text' placeholder='Enter maximum value' {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <p>Attachments</p>
          <button type='submit' disabled={form.formState.isSubmitting} className='btn-default-md md:col-span-2 w-fit'>
            {form.formState.isSubmitting ? (
              <>
                <Loader2 size={20} className='animate-spin mr-3' />
                <span>Saving...</span>
              </>
            ) : (
              <span>Submit</span>
            )}
          </button>
        </form>
      </Form>
    </div>
  );
};

export default StudentEducationForm;
