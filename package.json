{"name": "perfecttutor.in", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^3.9.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@tanstack/react-query": "^5.75.5", "@tanstack/react-query-devtools": "^5.75.5", "axios": "^1.9.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "date-fns": "^3.6.0", "framer-motion": "^12.0.6", "input-otp": "^1.2.4", "lucide-react": "^0.408.0", "motion": "^12.0.11", "next": "14.2.5", "react": "^18", "react-day-picker": "^8.10.1", "react-dom": "^18", "react-dropzone": "^14.3.8", "react-hook-form": "^7.52.1", "react-icons": "^5.4.0", "react-phone-input-2": "^2.15.1", "react-toastify": "^11.0.3", "swiper": "^11.1.7", "tailwind-merge": "^2.4.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.23.8", "zustand": "^4.5.4"}, "devDependencies": {"@types/google.maps": "^3.58.1", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/react-dropzone": "^5.1.0", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}