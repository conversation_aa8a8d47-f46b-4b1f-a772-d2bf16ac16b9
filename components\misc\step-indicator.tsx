import type React from 'react';
import { motion } from 'framer-motion';

interface StepIndicatorProps {
  currentStep: number;
  totalSteps: number;
  labels: string[];
}

const StepIndicator: React.FC<StepIndicatorProps> = ({ currentStep, totalSteps, labels }) => {
  return (
    <div className='relative mb-6 pt-10'>
      <div className='absolute top-5 left-0 w-full h-1 bg-primaryColor-100 rounded-full overflow-hidden'>
        <motion.div
          className='h-full bg-gradient-to-r from-primaryColor-300 via-secondaryColor-500 to-primaryColor-500'
          initial={{ width: '0%' }}
          animate={{ width: `${((currentStep - 1) / (totalSteps - 1)) * 100}%` }}
          transition={{ duration: 0.5, ease: 'easeInOut' }}
        />
      </div>
      <div className='flex justify-between'>
        {Array.from({ length: totalSteps }, (_, i) => i + 1).map((step) => (
          <div key={step} className='flex flex-col items-center relative'>
            <motion.div
              className={`w-10 h-10 rounded-lg flex items-center justify-center text-sm font-bold ${
                step <= currentStep
                  ? 'bg-gradient-to-br from-primaryColor-500 to-secondaryColor-500 text-white'
                  : 'bg-white text-gray-400 border border-gray-200'
              }`}
              initial={false}
              animate={{
                scale: step === currentStep ? 1.1 : 1,
                boxShadow: step === currentStep ? '0 0 0 3px rgba(99, 102, 241, 0.3)' : 'none',
              }}
              transition={{ type: 'spring', stiffness: 300, damping: 20 }}
            >
              {step}
            </motion.div>
            <motion.div
              className={`mt-2 text-xs font-medium text-center max-w-[80px] ${step <= currentStep ? 'text-primaryColor-700' : 'text-gray-500'}`}
              initial={{ opacity: 0, y: 5 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 * step, duration: 0.3 }}
            >
              {labels[step - 1]}
            </motion.div>
            {step < currentStep && (
              <motion.div
                className='absolute -top-1 -right-1 w-4 h-4 bg-green-400 rounded-full border-2 border-white flex items-center justify-center'
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ delay: 0.2 * step, type: 'spring', stiffness: 500, damping: 30 }}
              >
                <svg className='w-2 h-2 text-white' fill='none' stroke='currentColor' viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg'>
                  <path strokeLinecap='round' strokeLinejoin='round' strokeWidth={3} d='M5 13l4 4L19 7' />
                </svg>
              </motion.div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default StepIndicator;
