'use client';

import React from 'react';
import { FormField, FormItem, FormControl, FormLabel, FormMessage } from '@/components/ui/form';
import PhoneInput from 'react-phone-input-2';
import 'react-phone-input-2/lib/style.css';
import type { UseFormReturn, FieldValues, Path } from 'react-hook-form';
import { cn } from '@/lib/utils';

interface PrimaryPhoneInputProps<TFieldValues extends FieldValues> {
  form: UseFormReturn<TFieldValues>;
  name: Path<TFieldValues>;
  label: string;
  placeholder?: string;
  required?: boolean;
  defaultCountry?: string;
  onFocus?: () => void;
  variant?: 'primary' | 'secondary' | 'green' | 'purple';
}

const PrimaryPhoneInput = <TFieldValues extends FieldValues>({
  form,
  name,
  label,
  placeholder = 'Enter phone number',
  required = false,
  defaultCountry = 'in',
  onFocus,
  variant = 'primary',
}: PrimaryPhoneInputProps<TFieldValues>) => {
  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => (
        <FormItem className='w-full'>
          <FormLabel className='primary-label'>
            <span>{label}</span>
            {required && <span className='text-primaryColor ml-1'>*</span>}
          </FormLabel>
          <FormControl>
            <div className='phone-input-container'>
              <PhoneInput
                country={defaultCountry}
                value={field.value}
                onChange={(phone, _data: any) => {
                  field.onChange(phone);
                  form.trigger(name);
                }}
                onFocus={onFocus}
                placeholder={placeholder}
                inputClass={cn('primary-input !w-full !h-12 !pl-12 !border-gray-200', {
                  'focus:!ring-primaryColor-500 focus:!ring-offset-primaryColor-50': variant === 'primary',
                  'focus:!ring-secondaryColor-500 focus:!ring-offset-secondaryColor-50': variant === 'secondary',
                  'focus:!ring-green-500 focus:!ring-offset-green-50': variant === 'green',
                  'focus:!ring-purple-500 focus:!ring-offset-purple-50': variant === 'purple',
                })}
                containerClass='!w-full'
                buttonClass='!border-r-0 !bg-transparent'
                dropdownClass='!text-base'
              />
            </div>
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
};

export default PrimaryPhoneInput;
