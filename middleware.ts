import { NextResponse, NextRequest } from 'next/server';
import { getCurrentStaff, getCurrentUser } from '@/lib/auth/getCurrentUser';
import { IUserTypeMap } from './validation/schemas/maps';

export default async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  if (pathname.startsWith('/parent-dash')) {
    return userMiddleware(request, 'student');
  }

  if (pathname.startsWith('/tutor-dash')) {
    return userMiddleware(request, 'tutor');
  }

  if (pathname.startsWith('/institute-dash')) {
    return userMiddleware(request, 'institute');
  }

  if (pathname.startsWith('/school-dash')) {
    return userMiddleware(request, 'school');
  }

  if (pathname.startsWith('/college-dash')) {
    return userMiddleware(request, 'college');
  }

  if (pathname.startsWith('/ascrm')) {
    return adminMiddleware(request);
  }

  return NextResponse.next();
}

async function adminMiddleware(request: NextRequest) {
  try {
    const session = request.cookies.get('staff_session')?.value;
    if (!session) throw new Error('Missing session token');

    const staff = await getCurrentStaff(session);
    if (!staff) throw new Error('Failed to fetch staff data');

    if (staff.role !== 'super_admin') throw new Error('Staff is not an admin');

    if (!staff.isActive) throw new Error('Staff account is not active');

    return NextResponse.next();
  } catch (error: any) {
    console.error(`\x1b[31mMiddleware:${error.message}\x1b[0m`);
    return NextResponse.redirect(new URL('/staff-access', request.url));
  }
}

async function userMiddleware(request: NextRequest, userType: IUserTypeMap) {
  try {
    const session = request.cookies.get('session')?.value;
    if (!session) throw new Error('Missing session token');

    const user = await getCurrentUser(session, userType);
    if (!user) throw new Error('Failed to fetch user data');

    if (user.userType !== userType) throw new Error(`User is not a ${userType}`);

    return NextResponse.next();
  } catch (error: any) {
    console.error(`\x1b[31mMiddleware:${error.message}\x1b[0m`);
    return NextResponse.redirect(new URL('/login', request.url));
  }
}

export const config = {
  matcher: ['/parent-dash/:path*', '/tutor-dash/:path*', '/institute-dash/:path*', '/school-dash/:path*', '/college-dash/:path*', '/ascrm/:path*'],
};
