import type { Metada<PERSON> } from 'next';
import { Inter } from 'next/font/google';
import '@/app/globals.css';
import { NavBar, SideBar } from '@/components/dashboard/tutor-dash/misc';
import BottomNavBar from '@/components/dashboard/tutor-dash/misc/MobileBottomBar';
import ToastProvider from '@/components/providers/ToastProvider';
import QueryProvider from '@/lib/react-query/QueryProvider';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'Tutor Dashboard',
  description: 'Generated by create next app',
};

export default function RootLayout({ children }: Readonly<{ children: React.ReactNode }>) {
  return (
    <html lang='en'>
      <body className={inter.className}>
        <QueryProvider>
          <div className='grid grid-cols-[auto_1fr] bg-gradient-to-br from-slate-50 via-gray-50 to-slate-100 min-h-screen p-4 relative'>
            <SideBar />
            <div className='flex flex-col gap-4 w-full min-h-[calc(100vh-2rem)] relative z-10'>
              <NavBar />
              <main className='flex-1 rounded-3xl max-w-full w-full overflow-auto px-1'>{children}</main>
              <footer className='text-center text-xs text-gray-500 pb-2'>
                <p>© 2025 Perfect Tutor. All rights reserved.</p>
              </footer>
            </div>
          </div>
          <ToastProvider />
        </QueryProvider>
      </body>
    </html>
  );
}
