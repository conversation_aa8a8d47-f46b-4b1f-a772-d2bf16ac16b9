'use client';

import { FormField, FormItem, FormControl, Form, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { zodResolver } from '@hookform/resolvers/zod';
import { teachingExperienceSchema } from '@/lib/validations/tutor-dash/zod';
import { useForm } from 'react-hook-form';
import { Loader2 } from 'lucide-react';
import * as z from 'zod';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { tutorExperienceMap } from '@/constants';
import React, { useState } from 'react';

interface ITeachingExperience {
  isWork?: boolean;
}

const TeachingExperienceForm: React.FC<ITeachingExperience> = ({ isWork = false }) => {
  const form = useForm<z.infer<typeof teachingExperienceSchema>>({
    resolver: zodResolver(teachingExperienceSchema),
    defaultValues: {
      type: '',
      experienceInMonths: '',
      name: '',
      location: '',
    },
  });

  if (!isWork) {
    // @ts-ignore
    delete tutorExperienceMap['other'];
  }

  const [hideNameLocation, setHideNameLocation] = useState(false);

  const handleTypeChange = (value: string) => {
    if (value === 'private') {
      form.setValue('name', 'private');
      form.setValue('location', 'private');
      setHideNameLocation(true);
    } else {
      form.setValue('name', '');
      form.setValue('location', '');
      setHideNameLocation(false);
    }
  };

  async function onSubmit(values: z.infer<typeof teachingExperienceSchema>) {
    console.log(values);
  }

  return (
    <div>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className='grid grid-cols-1 md:grid-cols-2 items-start gap-6'>
          {/* Type */}
          <FormField
            control={form.control}
            name='type'
            render={({ field }) => (
              <FormItem className='w-full'>
                <FormLabel className='primary-label'>
                  <span>{isWork ? 'Work' : 'Tuition'} Type</span> <span className='text-primaryColor'>*</span>
                </FormLabel>
                <Select
                  onValueChange={(value) => {
                    handleTypeChange(value);
                    return field.onChange(value);
                  }}
                >
                  <FormControl>
                    <SelectTrigger className='primary-select'>
                      <SelectValue placeholder='Please choose your experince type' />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {Object.entries(tutorExperienceMap).map(([key, value]) => (
                      <SelectItem key={key} value={value.key}>
                        {value.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Experience in Months */}
          <FormField
            control={form.control}
            name='experienceInMonths'
            render={({ field }) => (
              <FormItem className='w-full'>
                <FormLabel className='primary-label'>
                  <span>Experience (in months)</span> <span className='text-primaryColor'>*</span>
                </FormLabel>
                <FormControl>
                  <Input className='primary-input' type='number' placeholder='Enter experience in months' {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {!hideNameLocation && (
            <>
              {/* Name */}
              <FormField
                control={form.control}
                name='name'
                render={({ field }) => (
                  <FormItem className='w-full'>
                    <FormLabel className='primary-label'>
                      <span>School/College/Institute Name</span> <span className='text-primaryColor'>*</span>
                    </FormLabel>
                    <FormControl>
                      <Input className='primary-input' type='text' placeholder='Enter name' {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Location */}
              <FormField
                control={form.control}
                name='location'
                render={({ field }) => (
                  <FormItem className='w-full'>
                    <FormLabel className='primary-label'>
                      <span>School/College/Institute Location</span> <span className='text-primaryColor'>*</span>
                    </FormLabel>
                    <FormControl>
                      <Input className='primary-input' type='text' placeholder='Enter location' {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </>
          )}
          <button type='submit' disabled={form.formState.isSubmitting} className='btn-default-md md:col-span-2 w-fit'>
            {form.formState.isSubmitting ? (
              <>
                <Loader2 size={20} className='animate-spin mr-3' />
                <span>Saving...</span>
              </>
            ) : (
              <span>Submit</span>
            )}
          </button>
        </form>
      </Form>
    </div>
  );
};

export default TeachingExperienceForm;
