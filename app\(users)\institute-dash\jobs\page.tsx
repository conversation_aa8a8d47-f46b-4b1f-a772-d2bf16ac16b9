'use client';

import { useState } from 'react';
import { Briefcase, ChevronRight, ChevronDown, IndianRupee, MapPin, ScrollText, Star, Filter, ScanEye, UserCircle2, Users2 } from 'lucide-react';
import { Select, SelectContent, SelectGroup, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { formatDistanceToNow } from 'date-fns';
import { InstiuteDashLink } from '@/components/dashboard/institute-dash/misc';
import Link from 'next/link';

type Job = {
  id: number;
  title: string;
  company: string;
  rating: number;
  reviews: number;
  experience: string;
  salary: string;
  location: string;
  description: string;
  tags: string[];
  date: Date;
  logo: string;
};

const initialJobs: Job[] = [
  {
    id: 1,
    title: 'Data Scientist',
    company: 'Swiggy',
    rating: 3.8,
    reviews: 3574,
    experience: '0-3 Yrs',
    salary: 'Not disclosed',
    location: 'Remote',
    description: 'Qualifications. Bachelors or Masters degree in a quantitative field with 0-2 years of experience.',
    tags: ['Natural Language Processing', 'Machine Learning', 'Deep Learning', 'Neural Networks'],
    date: new Date('2023-07-23T12:00:00Z'),
    logo: '/temp/swiggy-logo.gif',
  },
  {
    id: 2,
    title: 'Full Stack Developer',
    company: 'IBM',
    rating: 4.1,
    reviews: 20105,
    experience: '3-7 Yrs',
    salary: 'Not disclosed',
    location: 'Bengaluru',
    description:
      'Required Technical and Professional Expertise. 3+ years of experience in continuous integration, software development, ci/cd, testing methodologies, and zos.',
    tags: ['continuous integration', 'software development', 'ci/cd', 'testing methodologies', 'zos'],
    date: new Date('2022-07-23T09:00:00Z'),
    logo: '/temp/ibm-logo.gif',
  },
  {
    id: 3,
    title: 'Backend Developer',
    company: 'Google',
    rating: 4.5,
    reviews: 50102,
    experience: '2-5 Yrs',
    salary: 'Not disclosed',
    location: 'Bengaluru',
    description: 'Experience with backend systems and APIs. Proficiency in Java and Python is a must.',
    tags: ['Java', 'Python', 'APIs', 'backend'],
    date: new Date('2023-07-22T10:00:00Z'),
    logo: '/temp/google.png',
  },
  {
    id: 4,
    title: 'Machine Learning Engineer',
    company: 'Amazon',
    rating: 4.2,
    reviews: 30505,
    experience: '1-3 Yrs',
    salary: 'Not disclosed',
    location: 'Hyderabad',
    description: 'Qualifications. Bachelors or Masters degree in a quantitative field with 1-3 years of experience in ML.',
    tags: ['Machine Learning', 'Deep Learning', 'Python', 'AWS'],
    date: new Date('2024-07-21T15:00:00Z'),
    logo: '/temp/amazon.png',
  },
];

const TutorJobs = () => {
  const [jobs, setJobs] = useState<Job[]>(initialJobs);
  const [selectedFilters, setSelectedFilters] = useState({
    workMode: [] as string[],
    experience: '',
  });
  const [sortOrder, setSortOrder] = useState('Relevance');

  const handleFilterChange = (category: string, value: string) => {
    setSelectedFilters((prevFilters) => ({
      ...prevFilters,
      //   @ts-ignore
      [category]: prevFilters[category].includes(value) ? prevFilters[category].filter((item) => item !== value) : [...prevFilters[category], value],
    }));
  };

  const filteredJobs = jobs.filter((job) => {
    const matchesWorkMode = selectedFilters.workMode.length ? selectedFilters.workMode.includes(job.location) : true;
    const matchesExperience = selectedFilters.experience ? job.experience.startsWith(selectedFilters.experience) : true;

    return matchesWorkMode && matchesExperience;
  });

  const sortedJobs = filteredJobs.sort((a, b) => {
    if (sortOrder === 'Relevance') {
      return b.rating - a.rating;
    } else if (sortOrder === 'Newest') {
      return new Date(b.date).getTime() - new Date(a.date).getTime();
    } else if (sortOrder === 'Oldest') {
      return new Date(a.date).getTime() - new Date(b.date).getTime();
    }
    return 0;
  });

  const workModeCounts = jobs.reduce((acc: Record<string, number>, job) => {
    acc[job.location] = (acc[job.location] || 0) + 1;
    return acc;
  }, {});

  const experienceCounts = jobs.reduce((acc: Record<string, number>, job) => {
    const expYears = parseInt(job.experience.split('-')[0], 10);
    if (expYears <= 3) {
      acc['0-3 Yrs'] = (acc['0-3 Yrs'] || 0) + 1;
    } else if (expYears <= 5) {
      acc['3-5 Yrs'] = (acc['3-5 Yrs'] || 0) + 1;
    } else {
      acc['5+ Yrs'] = (acc['5+ Yrs'] || 0) + 1;
    }
    return acc;
  }, {});

  return (
    <div className='flex flex-col lg:flex-row gap-8'>
      {/* Filters Section */}
      {/* <div className='lg:w-1/4 max-lg:hidden'>
        <div className='bg-white p-6 rounded-3xl'>
          <h3 className='text-lg font-semibold pb-4 mb-4 border-b'>All Filters</h3>
          <FilterSection title='Work mode'>
            {Object.keys(workModeCounts).map((mode) => (
              <FilterCheckbox key={mode} label={mode} count={workModeCounts[mode]} onChange={() => handleFilterChange('workMode', mode)} />
            ))}
          </FilterSection>
          <FilterSection title='Experience'>
            {Object.keys(experienceCounts).map((exp) => (
              <FilterCheckbox key={exp} label={exp} count={experienceCounts[exp]} onChange={() => handleFilterChange('experience', exp)} />
            ))}
          </FilterSection>
        </div>
      </div> */}
      {/* Jobs Section */}
      <div className='lg:w-3/4'>
        <div className='flex flex-col lg:flex-row gap-6 justify-start items-start lg:justify-between lg:items-center mb-6 bg-white p-6 rounded-3xl'>
          <div>
            <h2 className='text-lg font-semibold'>Teaching Jobs</h2>
            <p className='text-gray-500 text-sm'>Lorem desc Lorem ipsum dolor, sit amet consectetur adi</p>
          </div>
          <div className='flex gap-4 items-center'>
            <div className='bg-gray-100 text-gray-600 p-3 flex items-center justify-center rounded-lg'>
              <Filter size={25} strokeWidth={1.5} />
            </div>
            <Select onValueChange={(value) => setSortOrder(value)}>
              <SelectTrigger className='w-[180px] primary-select'>
                <SelectValue placeholder={sortOrder} />
              </SelectTrigger>
              <SelectContent>
                <SelectGroup>
                  <SelectItem value='Relevance'>Relevance</SelectItem>
                  <SelectItem value='Newest'>Newest</SelectItem>
                  <SelectItem value='Oldest'>Oldest</SelectItem>
                </SelectGroup>
              </SelectContent>
            </Select>

<div className="btn-default-md">Post New Job</div>

          </div>
        </div>
        <div className='space-y-4'>
          {sortedJobs.map((job) => (
            <JobCard key={job.id} job={job} />
          ))}
        </div>
      </div>
    </div>
  );
};

const FilterSection = ({ title, children }: { title: string; children: React.ReactNode }) => {
  const [isOpen, setIsOpen] = useState(true);

  return (
    <div className='mb-4'>
      <div className='flex justify-between items-center cursor-pointer' onClick={() => setIsOpen(!isOpen)}>
        <h4 className='text-base font-semibold'>{title}</h4>
        {isOpen ? <ChevronDown size={16} /> : <ChevronRight size={16} />}
      </div>
      {isOpen && <div className='mt-2 space-y-2'>{children}</div>}
    </div>
  );
};

const FilterCheckbox = ({ label, count, onChange }: { label: string; count: number; onChange: () => void }) => (
  <div className='flex items-center'>
    <input type='checkbox' id={label} className='mr-2' onChange={onChange} />
    <label htmlFor={label} className='flex gap-2 w-full'>
      <span>{label}</span>
      <span className='text-gray-500'>({count})</span>
    </label>
  </div>
);

const JobCard: React.FC<{ job: Job }> = ({ job }) => (
  <div className='bg-white p-6 rounded-3xl grid grid-cols-1 md:grid-cols-5 gap-2'>
    <div className='md:col-span-4'>
      <div className='flex justify-between'>
        <h3 className='text-lg font-semibold'>{job.title}</h3>
        <img src={job.logo} alt={`${job.company} logo`} className='size-12 md:hidden' />
      </div>
      <div className='flex items-center text-gray-600 font-medium text-sm mb-3 gap-2'>
        <span>{job.company}</span>
        <span>|</span>
        <span className='flex items-start md:items-center gap-2'>
          <Star fill='orange' stroke='none' size={16} />
          {job.rating} ({job.reviews} Reviews)
        </span>
        <span>|</span>
        <span className='flex items-start md:items-center gap-2'>
          <UserCircle2 size={16} />
          16 Applied Candidates
        </span>
      </div>
      <div className='flex gap-2 items-center text-sm flex-wrap'>
        <p className='flex gap-1 items-start md:items-center'>
          <Briefcase size={16} className='shrink-0' /> <span>{job.experience}</span>
        </p>
        <span className='text-gray-400'>|</span>
        <p className='flex gap-1 items-start md:items-center'>
          <IndianRupee size={16} className='shrink-0' /> <span>{job.salary}</span>
        </p>
        <span className='text-gray-400'>|</span>
        <p className='flex gap-1 items-start md:items-center'>
          <MapPin size={16} className='shrink-0' /> <span>{job.location}</span>
        </p>
      </div>
      <div className='text-gray-500 my-2'>
        <p className='flex gap-2 items-start md:items-center'>
          <ScrollText size={18} className='shrink-0' />
          <span className='line-clamp-2 md:line-clamp-1'>{job.description}</span>
        </p>
      </div>
      <div className='flex flex-wrap gap-2 mb-4'>
        {job.tags.map((tag, index) => (
          <span key={index} className='bg-gray-100 text-gray-700 px-4 py-1 rounded capitalize'>
            {tag}
          </span>
        ))}
      </div>
      <div className='flex items-center gap-2 w-full'>
        <p className='font-medium text-xs hidden md:block'>{formatDistanceToNow(new Date(job.date))} ago</p>
        <Link href='/parent-dash/admission-enquiries/5/view' className='btn-default-sm gap-2 mt-auto flex items-center ml-auto'>
          <Users2 size={18} /> <span>Qualified Users (57)</span>
        </Link>
      </div>
    </div>
    <div className='flex justify-between md:flex-col md:justify-center items-center md:col-span-1 gap-4 flex-wrap'>
      <p className='font-medium text-xs md:hidden'>{formatDistanceToNow(new Date(job.date))} ago</p>
      <img src={job.logo} alt={`${job.company} logo`} className='size-12 hidden md:block' />
      <InstiuteDashLink href='/jobs/1' className='btn-default__outline-sm gap-2 mt-auto flex items-center'>
        <ScanEye size={18} /> <span>View Detail</span>
      </InstiuteDashLink>
    </div>
  </div>
);

export default TutorJobs;
