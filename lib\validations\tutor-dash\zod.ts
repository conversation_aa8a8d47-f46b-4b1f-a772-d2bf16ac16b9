import {
  complaintCategoryMap,
  gendersMap,
  jobTypesMap,
  keySkillsMap,
  languagesMap,
  preferredJobModesMap,
  studyAmountTypeMap,
  studySchoolBoardMap,
  studySchoolClassMap,
  studySectionMap,
  studySubjectMap,
  subjectsSpecializationMap,
} from '@/constants';

import { z } from 'zod';

export const tutorBasicProfileSchema = z.object({
  alternativeMobile: z
    .string()
    .min(10, { message: 'Please enter a valid phone number (at least 10 digits).' })
    .max(20, { message: 'Please enter a valid phone number (no more than 20 digits).' })
    .optional(),
  primaryWhatsApp: z
    .string()
    .min(10, { message: 'Please enter a valid WhatsApp number (at least 10 digits).' })
    .max(20, { message: 'Please enter a valid WhatsApp number (no more than 20 digits).' }),
  alternativeWhatsApp: z
    .string()
    .min(10, { message: 'Please enter a valid WhatsApp number (at least 10 digits).' })
    .max(20, { message: 'Please enter a valid WhatsApp number (no more than 20 digits).' })
    .optional(),
  gender: z.enum(Object.keys(gendersMap) as [keyof typeof gendersMap], { message: 'Please select a valid gender.' }),
  dateOfBirth: z.date().refine(
    (val) => {
      const date = new Date(val);
      return !isNaN(date.getTime());
    },
    { message: 'Please enter a valid date of birth.' }
  ),
});

export const addressSchema = z.object({
  houseNo: z.string().min(1, { message: 'Please enter your house number.' }),
  locality: z.string().min(1, { message: 'Please enter your locality.' }),
  landmark: z.string().optional(),
  // TODO: Make it optional or not 6 digits to handle worldwide addresses
  areaPinCode: z.string().regex(/^\d{6}$/, { message: 'Please enter a valid 6-digit PIN code.' }),
  city: z.string().min(1, { message: 'Please enter your city.' }),
  district: z.string().min(1, { message: 'Please enter your district.' }),
  state: z.string().min(1, { message: 'Please enter your state.' }),
});

export const tutionInfoSchema = z.object({
  mode: z.array(z.string()).min(1, { message: 'Please select at least one mode.' }),
  location: z.string().min(1, { message: 'Please enter the location.' }),
  distance: z.string().refine(
    (val) => {
      const distance = parseFloat(val);
      return !isNaN(distance) && distance > 0;
    },
    { message: 'Please enter a valid distance.' }
  ),
  language: z.array(z.string()).min(1, { message: 'Please select at least one language.' }),
  description: z.string().min(10, { message: 'Description should be at least 10 characters long.' }),
});

export const teachingExperienceSchema = z.object({
  type: z.string().min(1, { message: 'Please enter the type of experience.' }),
  experienceInMonths: z.string().refine(
    (val) => {
      const experienceInMonths = parseInt(val);
      return !isNaN(experienceInMonths) && experienceInMonths > 0;
    },
    { message: 'Please enter a valid experience in months.' }
  ),
  name: z.string().min(1, { message: 'Please enter the name.' }),
  location: z.string().min(1, { message: 'Please enter the location.' }),
});

export const whatDoYouTeachSchema = z.object({
  studySection: z.enum(Object.keys(studySectionMap) as [keyof typeof studySectionMap], { message: 'Please select a study section.' }),
  studyBoard: z.enum(Object.keys(studySchoolBoardMap) as [keyof typeof studySchoolBoardMap], { message: 'Please select a study board.' }),
  studyClass: z.enum(Object.keys(studySchoolClassMap) as [keyof typeof studySchoolClassMap], { message: 'Please select a study class.' }),
  subjects: z
    .array(z.enum(Object.keys(studySubjectMap) as [keyof typeof studySubjectMap]))
    .min(1, { message: 'Please select at least one subject.' }),
  amountType: z.enum(Object.keys(studyAmountTypeMap) as [keyof typeof studyAmountTypeMap], { message: 'Please select a study amount type.' }),
  amount: z.string().refine(
    (val) => {
      const amount = parseFloat(val);
      return !isNaN(amount) && amount > 0;
    },
    {
      message: 'Please enter a valid study amount.',
    }
  ),
});

export const tutorQualificationSchema = z.object({
  degree: z.string().min(1, { message: 'Please enter your degree.' }).optional(),
  branch: z.string().min(1, { message: 'Please enter your branch.' }).optional(),
  classOfStudy: z.string().min(1, { message: 'Please enter your class of study.' }).optional(),
  startYear: z
    .number()
    .int()
    .min(1900, { message: 'Please enter a valid start year.' })
    .max(new Date().getFullYear(), { message: 'Start year cannot be in the future.' }),
  endYearOrExpectedYear: z
    .number()
    .int()
    .min(1900, { message: 'Please enter a valid end or expected year.' })
    .max(new Date().getFullYear() + 10, { message: 'End year or expected year cannot be more than 10 years in the future.' }),
  percentage: z.string().refine(
    (val) => {
      const percentage = parseFloat(val);
      return !isNaN(percentage) && percentage >= 0 && percentage <= 100;
    },
    { message: 'Please enter a valid percentage.' }
  ),
  collegeOrUniversityName: z.string().min(1, { message: 'Please enter your college or university name.' }),
  location: z.string().min(1, { message: 'Please enter the location.' }),
  isPursuing: z.boolean().optional(),
});

export const tutorManualAadharKycSchema = z.object({
  aadharNumber: z.string().regex(/^\d{12}$/, { message: 'Please enter a valid 12-digit Aadhar number.' }),
  name: z.string().min(1, { message: 'Please enter your name.' }),
  // frontAadhar: z.string().url({ message: 'Please enter a valid URL for the front Aadhar image.' }),
  // backAadhar: z.string().url({ message: 'Please enter a valid URL for the back Aadhar image.' }),
  frontAadhar: z.string().optional(),
  backAadhar: z.string().optional(),
});

export const tutorJobSchema = z.object({
  jobType: z.enum(Object.keys(jobTypesMap) as [keyof typeof jobTypesMap], { message: 'Please select a job type.' }),
  currentPackage: z.string().refine(
    (val) => {
      const currentPackageInINR = parseInt(val);
      return !isNaN(currentPackageInINR) && currentPackageInINR > 0;
    },
    { message: 'Please enter a valid package in INR' }
  ),
  preferredJobMode: z.enum(Object.keys(preferredJobModesMap) as [keyof typeof preferredJobModesMap], {
    message: 'Please select a preferred job type.',
  }),
  preferredJobLocation: z.string().optional(),
  subjectsSpecialization: z
    .array(z.enum(Object.keys(subjectsSpecializationMap) as [keyof typeof subjectsSpecializationMap]))
    .min(1, { message: 'Please select at least one subject.' }),

  keySkills: z.array(z.enum(Object.keys(keySkillsMap) as [keyof typeof keySkillsMap])).min(1, { message: 'Please select at least one key skill.' }),
  languages: z
    .array(
      z.object({
        language: z.enum(Object.keys(languagesMap) as [keyof typeof languagesMap], { message: 'Please select a language.' }),
        level: z.string().min(1, { message: 'Please select a level.' }),
      })
    )
    .min(1, { message: 'Please add at least one language and level.' }),
  description: z.string().min(1, { message: 'Please describe yourself.' }),
});

export const tutorComplaintSchema = z.object({
  category: z.enum(Object.keys(complaintCategoryMap) as [keyof typeof complaintCategoryMap], { message: 'Please select a valid category.' }),
  subject: z.string().min(1, { message: 'Please enter a subject.' }),
  description: z.string().min(1, { message: 'Please enter a description.' }),
  attachments: z.array(z.any()).optional(),
});
