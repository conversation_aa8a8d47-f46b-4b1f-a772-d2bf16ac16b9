import { useQuery, useMutation } from '@tanstack/react-query';
import { QUERY_KEYS } from '@/lib/react-query/queryKeys';
import { queryClient } from '@/lib/react-query/queryClient';

import { languageTypeService, languageService } from '@/server/services/education/language.service';
import {
  CreateLanguageTypeInput,
  UpdateLanguageTypeInput,
  CreateLanguageInput,
  UpdateLanguageInput,
} from '@/validation/schemas/education/language.schema';

const languageEducationService = {
  languageTypes: languageTypeService,
  languages: languageService,
};

// Language Type Hooks
export function useGetAllLanguageTypes(params?: Record<string, any>, options = {}) {
  return useQuery({
    queryKey: [QUERY_KEYS.EDUCATION.LANGUAGE_TYPES, params],
    queryFn: () => languageEducationService.languageTypes.getAllLanguageTypes(params),
    ...options,
  });
}

export function useGetLanguageTypeById(id: string, options = {}) {
  return useQuery({
    queryKey: [QUERY_KEYS.EDUCATION.LANGUAGE_TYPE, id],
    queryFn: () => languageEducationService.languageTypes.getLanguageTypeById(id),
    enabled: !!id,
    ...options,
  });
}

export function useCreateLanguageType() {
  return useMutation({
    mutationFn: (data: CreateLanguageTypeInput) => languageEducationService.languageTypes.createLanguageType(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.EDUCATION.LANGUAGE_TYPES] });
    },
  });
}

export function useUpdateLanguageType() {
  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateLanguageTypeInput }) => languageEducationService.languageTypes.updateLanguageType(id, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.EDUCATION.LANGUAGE_TYPES] });
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.EDUCATION.LANGUAGE_TYPE, variables.id] });
    },
  });
}

export function useDeleteLanguageType() {
  return useMutation({
    mutationFn: (id: string) => languageEducationService.languageTypes.deleteLanguageType(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.EDUCATION.LANGUAGE_TYPES] });
    },
  });
}

// Language Hooks
export function useGetAllLanguages(params?: Record<string, any>, options = {}) {
  return useQuery({
    queryKey: [QUERY_KEYS.EDUCATION.LANGUAGES, params],
    queryFn: () => languageEducationService.languages.getAllLanguages(params),
    ...options,
  });
}

export function useGetLanguageById(id: string, options = {}) {
  return useQuery({
    queryKey: [QUERY_KEYS.EDUCATION.LANGUAGE, id],
    queryFn: () => languageEducationService.languages.getLanguageById(id),
    enabled: !!id,
    ...options,
  });
}

export function useCreateLanguage() {
  return useMutation({
    mutationFn: (data: CreateLanguageInput) => languageEducationService.languages.createLanguage(data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.EDUCATION.LANGUAGES] });
    },
  });
}

export function useUpdateLanguage() {
  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateLanguageInput }) => languageEducationService.languages.updateLanguage(id, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.EDUCATION.LANGUAGES] });
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.EDUCATION.LANGUAGE, variables.id] });
    },
  });
}

export function useDeleteLanguage() {
  return useMutation({
    mutationFn: (id: string) => languageEducationService.languages.deleteLanguage(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.EDUCATION.LANGUAGES] });
    },
  });
}
