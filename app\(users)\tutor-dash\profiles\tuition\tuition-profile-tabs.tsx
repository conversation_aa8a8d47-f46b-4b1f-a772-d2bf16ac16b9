'use client';

import { BookOpen, Briefcase, User } from 'lucide-react';
import PrimaryTabs, { TabItem } from '@/components/dashboard/shared/misc/primary-tabs';

interface TuitionProfileTabsProps {
  selectedTab: 'info' | 'experience' | 'subjects';
  setSelectedTab: (tab: 'info' | 'experience' | 'subjects') => void;
  tabCounts: {
    info: number;
    experience: number;
    subjects: number;
  };
}

const TuitionProfileTabs = ({ selectedTab, setSelectedTab, tabCounts }: TuitionProfileTabsProps) => {
  const tabs: TabItem[] = [
    {
      id: 'info',
      label: 'Tuition Info',
      icon: User,
      description: 'Basic information',
      variant: 'primary',
      count: tabCounts.info,
    },
    {
      id: 'experience',
      label: 'Experience',
      icon: Briefcase,
      description: 'Teaching history',
      variant: 'green',
      count: tabCounts.experience,
    },
    {
      id: 'subjects',
      label: 'Subjects',
      icon: BookOpen,
      description: 'What you teach',
      variant: 'purple',
      count: tabCounts.subjects,
    },
  ];

  const completionPercentage = Math.round(
    (((tabCounts.info > 0 ? 1 : 0) + (tabCounts.experience > 0 ? 1 : 0) + (tabCounts.subjects > 0 ? 1 : 0)) / 3) * 100
  );

  return (
    <div className='h-fit'>
      <div className='mb-6'>
        <h2 className='text-xl font-bold text-gray-800 mb-2'>Tuition Profile</h2>
        <p className='text-sm text-gray-500'>Manage your teaching profile</p>
      </div>

      <PrimaryTabs tabs={tabs} selectedTab={selectedTab} setSelectedTab={setSelectedTab as (tab: string) => void} />

      <div className='mt-4 p-4 bg-gradient-to-br from-white to-gray-50 rounded-2xl border border-gray-100 shadow-sm relative overflow-hidden'>
        <div className='absolute bottom-0 left-0 w-24 h-24 bg-purple-50 rounded-full -ml-10 -mb-10 opacity-50'></div>

        <div className='relative'>
          <div className='flex items-center justify-between mb-3'>
            <h4 className='font-semibold text-gray-800 text-base'>Tuition Profile</h4>
            <span className='text-sm font-bold px-3 py-1 bg-white border border-gray-100 rounded-full shadow-sm'>{completionPercentage}%</span>
          </div>

          <div className='w-full h-3 bg-gray-100 rounded-full overflow-hidden shadow-inner'>
            <div
              className='h-full bg-gradient-to-r from-primaryColor-500 via-primaryColor-400 to-primaryColor-500 relative'
              style={{
                width: `${completionPercentage}%`,
              }}
            >
              <div className='absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent'></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TuitionProfileTabs;
