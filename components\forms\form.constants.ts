export const inputClassName = {
  primary: '!py-6 focus-visible:!ring-primaryColor-500 !ring-offset-primaryColor-50',
  secondary: '!py-6 focus-visible:!ring-secondaryColor-500 !ring-offset-secondaryColor-50',
  green: '!py-6 focus-visible:!ring-green-500 !ring-offset-green-50',
  purple: '!py-6 focus-visible:!ring-purple-500 !ring-offset-purple-50',
};

export const VARIANT_THEME_CLASSES = {
  primary: {
    // RED in general
    bg: 'bg-primaryColor-50',
    text: 'text-primaryColor-700',
    border: 'border-primaryColor-200',
    borderStrong: 'border-primaryColor-500',
    ring: 'ring-primaryColor-100',
    ringFocus: 'focus:ring-primaryColor-500',
    hoverBg: 'hover:bg-primaryColor-100',
    hoverText: 'hover:text-primaryColor-700',
    check: 'text-primaryColor-500',
    checkHover: 'hover:text-primaryColor-700',
    checkbox: 'accent-primaryColor text-primaryColor-600 focus:ring-primaryColor-500',
  },
  secondary: {
    // Rose in general
    bg: 'bg-secondaryColor-50',
    text: 'text-secondaryColor-700',
    border: 'border-secondaryColor-200',
    borderStrong: 'border-secondaryColor-500',
    ring: 'ring-secondaryColor-100',
    ringFocus: 'focus:ring-secondaryColor-500',
    hoverBg: 'hover:bg-secondaryColor-100',
    hoverText: 'hover:text-secondaryColor-700',
    check: 'text-secondaryColor-500',
    checkHover: 'hover:text-secondaryColor-700',
    checkbox: 'accent-secondaryColor text-secondaryColor-600 focus:ring-secondaryColor-500',
  },
  green: {
    bg: 'bg-green-50',
    text: 'text-green-700',
    border: 'border-green-200',
    borderStrong: 'border-green-500',
    ring: 'ring-green-100',
    ringFocus: 'focus:ring-green-500',
    hoverBg: 'hover:bg-green-100',
    hoverText: 'hover:text-green-700',
    check: 'text-green-500',
    checkHover: 'hover:text-green-700',
    checkbox: 'accent-green-500 text-green-600 focus:ring-green-500',
  },
  purple: {
    bg: 'bg-purple-50',
    text: 'text-purple-700',
    border: 'border-purple-200',
    borderStrong: 'border-purple-500',
    ring: 'ring-purple-100',
    ringFocus: 'focus:ring-purple-500',
    hoverBg: 'hover:bg-purple-100',
    hoverText: 'hover:text-purple-700',
    check: 'text-purple-500',
    checkHover: 'hover:text-purple-700',
    checkbox: 'accent-purple-500 text-purple-600 focus:ring-purple-500',
  },
};
