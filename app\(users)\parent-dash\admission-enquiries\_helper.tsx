'use client';

import { useState } from 'react';
import { MobileTabsFilter, TabsFilter } from '@/components/dashboard/shared/misc';
import { ParentDashActionLinks, MobileLeadItemCard, SimpleTable } from '@/components/dashboard/parent-dash/misc';
import { BookA } from 'lucide-react';

const leads = [
  {
    id: 1,
    name: '<PERSON><PERSON>',
    enquiryFor: 'Class 10',
    location: 'New Delhi',
    distance: '5 km',
    address: '123 Main St, New Delhi, DL 110001',
    state: 'Delhi',
    country: 'India',
    pincode: '110001',
    subjects: ['Math', 'Science', 'English'],
    board: 'CBSE',
    status: 'converted',
  },
  {
    id: 5,
    name: '<PERSON><PERSON>er<PERSON>',
    enquiryFor: 'Class 10',
    location: 'New Delhi',
    distance: '5 km',
    status: 'active',
    address: '123 Main St, New Delhi, DL 110001',
    state: 'Delhi',
    country: 'India',
    pincode: '110001',
    subjects: ['Math', 'Science', 'English'],
    board: 'CBSE',
  },
  {
    id: 2,
    name: '<PERSON><PERSON>',
    enquiryFor: 'B.Sc',
    location: 'Mumbai',
    distance: '15 km',
    status: 'active',
    address: '456 Elm St, Mumbai, MH 400001',
    state: 'Maharashtra',
    country: 'India',
    pincode: '400001',
    subjects: ['Physics', 'Chemistry', 'Mathematics', 'Biology', 'Computer Science'],
    board: 'CBSE',
  },
  {
    id: 3,
    name: 'Amit Sharma',
    enquiryFor: 'Class 12',
    location: 'Bangalore',
    distance: '10 km',
    status: 'closed',
    address: '789 Oak St, Bangalore, KA 560001',
    state: 'Karnataka',
    country: 'India',
    pincode: '560001',
    subjects: ['Physics', 'Chemistry', 'Mathematics', 'Biology', 'Computer Science'],
    board: 'CBSE',
  },
  {
    id: 4,
    name: 'Neha Patel',
    enquiryFor: 'M.Sc',
    location: 'Ahmedabad',
    distance: '20 km',
    status: 'closed',
    address: '321 Pine St, Ahmedabad, GJ 380001',
    state: 'Gujarat',
    country: 'India',
    pincode: '380001',
    subjects: ['Physics', 'Chemistry', 'Mathematics', 'Biology', 'Computer Science'],
    board: 'CBSE',
  },
];

const ParentAdmissionLeadsHelper = () => {
  const [selectedTab, setSelectedTab] = useState('active');

  const filteredLeads = leads.filter((lead) => lead.status === selectedTab);

  const tabCounts = {
    active: leads.filter((lead) => lead.status === 'active').length,
    closed: leads.filter((lead) => lead.status === 'closed').length,
  };

  const headers = ['Name', 'Enquiry For', 'Location', 'Date', 'Action'];

  const rows = filteredLeads.map((lead, index) => [
    <div key={`${lead.name}-${index}`}>
      <p>{lead.name}</p>
      <p>{lead.id}000</p>
    </div>,
    <div key={`${lead.enquiryFor}-${index}`}>
      <p>{lead.enquiryFor}</p>
      <p>{['B.Sc', 'M.Sc'].includes(lead.enquiryFor) ? 'Computer' : 'CBSE'}</p>
    </div>,
    lead.address,
    '06 July 2024',
    <ParentDashActionLinks key={`${lead.id}-${index}`} basePath='admission-enquiries' id={lead.id.toString()} view />,
  ]);

  return (
    <div className='bg-white p-6 rounded-3xl relative min-h-[650px] flex flex-col lg:flex-row items-start justify-start gap-4'>
      <div className='lg:w-1/4 w-full'>
        <TabsFilter label='Leads' selectedTab={selectedTab} setSelectedTab={setSelectedTab} tabCounts={tabCounts} />
      </div>
      <div className='lg:w-3/4 w-full'>
        <div className='flex justify-between items-center'>
          <h2 className='text-lg font-semibold mb-4 capitalize'>Recent {selectedTab} Leads For You</h2>
          <button className='btn-default-sm'>Add New Enquiry</button>
        </div>
        <div className='flex flex-col gap-4 md:hidden'>
          {filteredLeads.map((lead, index) => (
            <MobileLeadItemCard key={index} lead={lead} />
          ))}
        </div>
        <div className='max-md:hidden'>
          <SimpleTable headers={headers} rows={rows} />
        </div>
        <MobileTabsFilter
          label='Leads'
          icon={BookA}
          mobileTabs={['active', 'closed']}
          selectedTab={selectedTab}
          setSelectedTab={setSelectedTab}
          tabCounts={tabCounts}
        />
      </div>
    </div>
  );
};

export default ParentAdmissionLeadsHelper;
