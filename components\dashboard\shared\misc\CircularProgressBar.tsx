const CircularProgressBar = ({ percentage, label, colorClass }: { percentage: number; label: string; colorClass: string }) => {
  const circumference = 2 * Math.PI * 36;
  const offset = circumference - (percentage / 100) * circumference;

  return (
    <div className='flex flex-col items-center justify-center p-2 bg-gray-50 rounded-lg'>
      <div className='relative'>
        <svg className='size-20 transform -rotate-90'>
          <circle className='text-gray-200' strokeWidth='5' stroke='currentColor' fill='transparent' r='36' cx='40' cy='40' />
          <circle
            className={`transition-all duration-500 ${colorClass}`}
            strokeWidth='5'
            strokeDasharray={circumference}
            strokeDashoffset={offset}
            strokeLinecap='round'
            stroke='currentColor'
            fill='transparent'
            r='36'
            cx='40'
            cy='40'
          />
        </svg>
        <div className='absolute inset-0 flex items-center justify-center'>
          <span className='text-xl font-bold text-gray-700'>{percentage}%</span>
        </div>
      </div>
      <span className='mt-2 text-sm font-medium text-gray-600'>{label}</span>
    </div>
  );
};

export default CircularProgressBar;
