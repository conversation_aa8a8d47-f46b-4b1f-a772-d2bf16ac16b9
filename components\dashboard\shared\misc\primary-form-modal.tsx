'use client';

import React, { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, Sparkles, AlertCircle } from 'lucide-react';
import { cn } from '@/lib/utils';

export type ModalSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'full';

export interface ModalState {
  isOpen: boolean;
  open: () => void;
  close: () => void;
  toggle: () => void;
}

export interface PrimaryModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  subtitle?: string;
  size?: ModalSize;
  children: React.ReactNode;
  showCloseButton?: boolean;
  closeOnOutsideClick?: boolean;
  className?: string;
  titleClassName?: string;
  contentClassName?: string;
  showSparkles?: boolean;

  isLoading?: boolean;
  loadingTitle?: string;
  loadingMessage?: string;
  isError?: boolean;
  errorMessage?: string;
}

const sizeClasses: Record<ModalSize, string> = {
  xs: 'max-w-xs',
  sm: 'max-w-sm',
  md: 'max-w-md',
  lg: 'max-w-lg',
  xl: 'max-w-xl',
  full: 'max-w-4xl',
};

export const useModal = (initialState = false): ModalState => {
  const [isOpen, setIsOpen] = useState(initialState);

  const open = () => setIsOpen(true);
  const close = () => setIsOpen(false);
  const toggle = () => setIsOpen((prev) => !prev);

  return { isOpen, open, close, toggle };
};

const PrimaryFormModal: React.FC<PrimaryModalProps> = ({
  isOpen,
  onClose,
  title,
  subtitle,
  size = 'md',
  children,
  showCloseButton = true,
  closeOnOutsideClick = true,
  className,
  titleClassName,
  contentClassName,
  showSparkles = false,

  isLoading = false,
  loadingTitle,
  loadingMessage,

  isError = false,
  errorMessage = 'Could not load data. Please try again.',
}) => {
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') onClose();
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = '';
    };
  }, [isOpen, onClose]);

  const handleBackdropClick = () => {
    if (closeOnOutsideClick) {
      onClose();
    }
  };

  const modalVariants = {
    hidden: {
      opacity: 0,
      scale: 0.85,
      y: 30,
      rotateX: 5,
    },
    visible: {
      opacity: 1,
      scale: 1,
      y: 0,
      rotateX: 0,
      transition: {
        type: 'spring',
        damping: 20,
        stiffness: 300,
        delayChildren: 0.15,
        staggerChildren: 0.07,
      },
    },
    exit: {
      opacity: 0,
      scale: 0.95,
      y: -20,
      rotateX: -5,
      transition: {
        duration: 0.25,
        ease: [0.36, 0.66, 0.04, 1],
      },
    },
  };

  const backdropVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.3,
        ease: 'easeOut',
      },
    },
    exit: {
      opacity: 0,
      transition: {
        duration: 0.2,
        ease: 'easeIn',
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 15, scale: 0.95 },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        type: 'spring',
        damping: 25,
        stiffness: 300,
      },
    },
    exit: {
      opacity: 0,
      y: -10,
      transition: {
        duration: 0.2,
      },
    },
  };

  const generateSparkles = () => {
    return Array.from({ length: 20 }).map((_, i) => {
      const size = Math.random() * 4 + 2;
      const angle = Math.random() * 360;
      const distance = Math.random() * 100 + 50;
      const delay = Math.random() * 0.5;
      const duration = Math.random() * 1 + 1;

      return (
        <motion.div
          key={i}
          className='absolute rounded-full bg-primaryColor-400'
          style={{
            width: size,
            height: size,
            top: '50%',
            left: '50%',
            x: '-50%',
            y: '-50%',
          }}
          initial={{ opacity: 0, scale: 0 }}
          animate={{
            opacity: [0, 1, 0],
            scale: [0, 1, 0],
            x: `calc(-50% + ${Math.cos(angle * (Math.PI / 180)) * distance}px)`,
            y: `calc(-50% + ${Math.sin(angle * (Math.PI / 180)) * distance}px)`,
          }}
          transition={{
            duration,
            delay,
            repeat: Infinity,
            repeatDelay: Math.random() * 2,
          }}
        />
      );
    });
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          <motion.div
            className='fixed inset-0 bg-black/50 backdrop-blur-[2px] z-50 flex items-center justify-center'
            variants={backdropVariants}
            initial='hidden'
            animate='visible'
            exit='exit'
            onClick={handleBackdropClick}
            whileTap={{ backgroundColor: 'rgba(0, 0, 0, 0.55)' }}
          >
            <motion.div
              className={cn(
                'relative bg-white rounded-xl shadow-xl overflow-hidden',
                sizeClasses[size],
                'w-full mx-4',
                'border border-gray-100',
                className
              )}
              variants={modalVariants}
              initial='hidden'
              animate='visible'
              exit='exit'
              onClick={(e) => e.stopPropagation()}
            >
              <div className='absolute top-0 inset-x-0 h-1 bg-gradient-to-r from-primaryColor-400 via-primaryColor-500 to-primaryColor-600' />
              <div className='absolute -left-6 -top-6 w-12 h-12 rounded-full bg-primaryColor-100 opacity-50' />
              <div className='absolute -right-6 -bottom-6 w-12 h-12 rounded-full bg-primaryColor-100 opacity-50' />

              <motion.div
                className={cn('px-6 py-4 bg-gradient-to-r from-gray-50 to-white border-b border-gray-100', titleClassName)}
                variants={itemVariants}
              >
                <div className='flex items-center justify-between'>
                  <div className='flex-1'>
                    <div className='flex items-center gap-2'>
                      <h2 className='text-lg font-semibold text-gray-800 tracking-tight'>{title}</h2>
                      {showSparkles && (
                        <motion.div
                          className='relative'
                          initial={{ rotate: -5 }}
                          animate={{ rotate: 5 }}
                          transition={{ duration: 2, repeat: Infinity, repeatType: 'reverse', ease: 'easeInOut' }}
                        >
                          <Sparkles className='h-5 w-5 text-primaryColor-500' />
                          {generateSparkles()}
                        </motion.div>
                      )}
                    </div>
                    {subtitle && (
                      <motion.p className='text-sm text-gray-500 mt-1' variants={itemVariants}>
                        {subtitle}
                      </motion.p>
                    )}
                  </div>
                  {showCloseButton && (
                    <motion.button
                      onClick={onClose}
                      className='rounded-full p-1.5 bg-gray-100 hover:bg-gray-200 text-gray-500 transition-all duration-200 hover:scale-110 hover:rotate-90'
                      aria-label='Close modal'
                      whileHover={{ scale: 1.1, rotate: 90 }}
                      whileTap={{ scale: 0.9 }}
                    >
                      <X size={18} />
                    </motion.button>
                  )}
                </div>
              </motion.div>

              {/* Content */}
              <motion.div className={cn('relative z-10', contentClassName)} variants={itemVariants}>
                {isLoading ? (
                  <div className='p-8 flex flex-col items-center justify-center space-y-4'>
                    <div className='relative'>
                      <div className='w-12 h-12 rounded-full border-t-2 border-b-2 border-primaryColor-500 animate-spin'></div>
                      <div className='absolute inset-0 w-12 h-12 rounded-full border-r-2 border-l-2 border-primaryColor-300 animate-ping opacity-20'></div>
                    </div>
                    <p className='text-gray-500 animate-pulse font-medium'>{loadingTitle || 'Loading data...'}</p>
                    {loadingMessage && <p className='text-sm text-gray-400 text-center'>{loadingMessage}</p>}
                    <div className='w-16 h-1 bg-gradient-to-r from-primaryColor-200 to-primaryColor-500 rounded-full'></div>
                  </div>
                ) : isError ? (
                  <div className='p-8 flex flex-col items-center justify-center space-y-4'>
                    <div className='w-16 h-16 rounded-full bg-red-50 flex items-center justify-center'>
                      <AlertCircle className='h-8 w-8 text-red-500' />
                    </div>
                    <h3 className='text-gray-700 font-medium'>Unable to Load Data</h3>
                    <p className='text-gray-500 text-center max-w-xs'>{errorMessage}</p>
                  </div>
                ) : (
                  children
                )}
              </motion.div>

              <div className='absolute inset-0 pointer-events-none opacity-[0.03] z-0'>
                <div
                  className='absolute inset-0'
                  style={{
                    backgroundImage: 'radial-gradient(circle, #000 1px, transparent 1px)',
                    backgroundSize: '20px 20px',
                  }}
                />
              </div>
            </motion.div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
};

export default PrimaryFormModal;
