import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronLeft } from 'lucide-react';
import LoginWithEmailPasswordForm from './login-with-password';
import LoginWithMobileOtpForm from './login-with-otp';
import Link from 'next/link';
import { IUserTypeMap } from '@/validation/schemas/maps';

interface LoginFormsProps {
  selectedUserType: IUserTypeMap;
  goToStep: () => void;
}

export const LoginForms: React.FC<LoginFormsProps> = ({ selectedUserType, goToStep }) => {
  const [activeTab, setActiveTab] = useState<'otp' | 'password'>('otp');

  const tabs = [
    { id: 'otp', label: 'Login with OTP' },
    { id: 'password', label: 'Login with Email/Phone' },
  ];

  const contentVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 },
    exit: { opacity: 0, y: -20 },
  };

  return (
    <div className='space-y-6'>
      <button
        onClick={goToStep}
        className='group flex items-center gap-2 px-4 py-2 text-sm font-medium text-primaryColor transition-all hover:text-primaryColor bg-primaryColor-50 border border-primaryColor-200 rounded-lg hover:bg-primaryColor-100'
      >
        <ChevronLeft className='transition-transform group-hover:-translate-x-1' />
        Back to user selection
      </button>
      <div>
        <h2 className='text-3xl font-bold text-gray-900'>
          Welcome, <span className='text-primaryColor capitalize'>{selectedUserType}</span>
        </h2>
        <p className='mt-2 text-gray-600'>Please log in to access your dashboard.</p>
      </div>
      <div className='w-full'>
        <div className='relative mb-6'>
          <div className='flex bg-primaryColor-100 p-1 rounded-lg'>
            {tabs.map((tab) => (
              <motion.button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as 'otp' | 'password')}
                className={`relative z-10 flex-1 px-4 py-2 text-sm font-medium transition-colors duration-300 ${
                  activeTab === tab.id ? 'text-white' : 'text-gray-600'
                }`}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                {tab.label}
              </motion.button>
            ))}
          </div>
          <motion.div
            className='absolute top-1 left-1 bottom-1 rounded-md bg-primaryColor shadow-lg'
            initial={false}
            animate={{
              x: activeTab === 'otp' ? '0%' : '100%',
              width: '50%',
            }}
            transition={{ type: 'spring', stiffness: 300, damping: 30 }}
          />
        </div>
        <AnimatePresence mode='wait'>
          <motion.div key={activeTab} variants={contentVariants} initial='hidden' animate='visible' exit='exit' transition={{ duration: 0.3 }}>
            {activeTab === 'otp' ? (
              <LoginWithMobileOtpForm selectedUserType={selectedUserType} />
            ) : (
              <LoginWithEmailPasswordForm selectedUserType={selectedUserType} />
            )}
          </motion.div>
        </AnimatePresence>
      </div>
      <p className='text-center text-sm text-gray-600'>
        Don't have an account?{' '}
        <Link href='/register' className='font-medium text-primaryColor hover:underline'>
          Register here
        </Link>
      </p>
    </div>
  );
};
