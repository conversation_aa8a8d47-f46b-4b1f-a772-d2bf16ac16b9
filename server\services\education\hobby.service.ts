import apiClient from '@/server/apiClient';
import { IAPIResponse, IPagination } from '@/types/api';
import { CreateHobbyTypeInput, CreateHobbyInput, UpdateHobbyTypeInput, UpdateHobbyInput } from '@/validation/schemas/education/hobby.schema';

// Types for Hobby Types
export interface IHobbyTypeDocument extends CreateHobbyTypeInput {
  _id: string;
  createdAt: string;
  updatedAt: string;
}

export interface IHobbyTypesResponse {
  hobbyTypes: IHobbyTypeDocument[];
  pagination: IPagination;
}

export interface IHobbyTypeResponse {
  hobbyType: IHobbyTypeDocument;
}

// Types for Hobbies
export interface IHobbyDocument extends CreateHobbyInput {
  _id: string;
  hobbyTypeDetails?: IHobbyTypeDocument;
  createdAt: string;
  updatedAt: string;
}

export interface IHobbiesResponse {
  hobbies: IHobbyDocument[];
  pagination: IPagination;
}

export interface IHobbyResponse {
  hobby: IHobbyDocument;
}

// 1. Hobby Type Service
const hobbyTypeService = {
  getAllHobbyTypes: async (params?: Record<string, any>) => {
    const queryParams = new URLSearchParams(params);
    return await apiClient.get<IHobbyTypesResponse>(`/education/hobby-types?${queryParams.toString()}`);
  },

  getHobbyTypeById: async (id: string) => {
    return await apiClient.get<IHobbyTypeResponse>(`/education/hobby-types/${id}`);
  },

  createHobbyType: async (data: CreateHobbyTypeInput) => {
    return await apiClient.post<IHobbyTypeResponse>('/education/hobby-types', data);
  },

  updateHobbyType: async (id: string, data: UpdateHobbyTypeInput) => {
    return await apiClient.patch<IHobbyTypeResponse>(`/education/hobby-types/${id}`, data);
  },

  deleteHobbyType: async (id: string) => {
    return await apiClient.delete<IAPIResponse>(`/education/hobby-types/${id}`);
  },
};

// 2. Hobby Service
const hobbyService = {
  getAllHobbies: async (params?: Record<string, any>) => {
    const queryParams = new URLSearchParams(params);
    return await apiClient.get<IHobbiesResponse>(`/education/hobbies?${queryParams.toString()}`);
  },

  getHobbyById: async (id: string) => {
    return await apiClient.get<IHobbyResponse>(`/education/hobbies/${id}`);
  },

  createHobby: async (data: CreateHobbyInput) => {
    return await apiClient.post<IHobbyResponse>('/education/hobbies', data);
  },

  updateHobby: async (id: string, data: UpdateHobbyInput) => {
    return await apiClient.patch<IHobbyResponse>(`/education/hobbies/${id}`, data);
  },

  deleteHobby: async (id: string) => {
    return await apiClient.delete<IAPIResponse>(`/education/hobbies/${id}`);
  },
};

export { hobbyTypeService, hobbyService };
