import { useQuery, useMutation } from '@tanstack/react-query';
import { QUERY_KEYS } from '@/lib/react-query/queryKeys';
import { queryClient } from '@/lib/react-query/queryClient';

import { streamService, degreeLevelService, degreeService, branchService, collegeSubjectService } from '@/server/services/education/college.service';
import {
  CreateStreamInput,
  UpdateStreamInput,
  CreateDegreeLevelInput,
  UpdateDegreeLevelInput,
  CreateDegreeInput,
  UpdateDegreeInput,
  CreateBranchInput,
  UpdateBranchInput,
  CreateCollegeSubjectInput,
  UpdateCollegeSubjectInput,
} from '@/validation/schemas/education/college.schema';

const collegeService = {
  streams: streamService,
  degreeLevels: degreeLevelService,
  degrees: degreeService,
  branches: branchService,
  collegeSubjects: collegeSubjectService,
};

// Stream Hooks
export function useGetAllStreams(params?: Record<string, any>, options = {}) {
  return useQuery({
    queryKey: [QUERY_KEYS.EDUCATION.STREAMS, params],
    queryFn: () => collegeService.streams.getAllStreams(params),
    ...options,
  });
}

export function useGetStreamById(id: string, options = {}) {
  return useQuery({
    queryKey: [QUERY_KEYS.EDUCATION.STREAM, id],
    queryFn: () => collegeService.streams.getStreamById(id),
    enabled: !!id,
    ...options,
  });
}

export function useCreateStream() {
  return useMutation({
    mutationFn: (data: CreateStreamInput) => collegeService.streams.createStream(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.EDUCATION.STREAMS] });
    },
  });
}

export function useUpdateStream() {
  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateStreamInput }) => collegeService.streams.updateStream(id, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.EDUCATION.STREAMS] });
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.EDUCATION.STREAM, variables.id] });
    },
  });
}

export function useDeleteStream() {
  return useMutation({
    mutationFn: (id: string) => collegeService.streams.deleteStream(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.EDUCATION.STREAMS] });
    },
  });
}

// Degree Level Hooks
export function useGetAllDegreeLevels(params?: Record<string, any>, options = {}) {
  return useQuery({
    queryKey: [QUERY_KEYS.EDUCATION.DEGREE_LEVELS, params],
    queryFn: () => collegeService.degreeLevels.getAllDegreeLevels(params),
    ...options,
  });
}

export function useGetDegreeLevelById(id: string, options = {}) {
  return useQuery({
    queryKey: [QUERY_KEYS.EDUCATION.DEGREE_LEVEL, id],
    queryFn: () => collegeService.degreeLevels.getDegreeLevelById(id),
    enabled: !!id,
    ...options,
  });
}

export function useCreateDegreeLevel() {
  return useMutation({
    mutationFn: (data: CreateDegreeLevelInput) => collegeService.degreeLevels.createDegreeLevel(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.EDUCATION.DEGREE_LEVELS] });
    },
  });
}

export function useUpdateDegreeLevel() {
  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateDegreeLevelInput }) => collegeService.degreeLevels.updateDegreeLevel(id, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.EDUCATION.DEGREE_LEVELS] });
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.EDUCATION.DEGREE_LEVEL, variables.id] });
    },
  });
}

export function useDeleteDegreeLevel() {
  return useMutation({
    mutationFn: (id: string) => collegeService.degreeLevels.deleteDegreeLevel(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.EDUCATION.DEGREE_LEVELS] });
    },
  });
}

// Degree Hooks
export function useGetAllDegrees(params?: Record<string, any>, options = {}) {
  return useQuery({
    queryKey: [QUERY_KEYS.EDUCATION.DEGREES, params],
    queryFn: () => collegeService.degrees.getAllDegrees(params),
    ...options,
  });
}

export function useGetDegreeById(id: string, options = {}) {
  return useQuery({
    queryKey: [QUERY_KEYS.EDUCATION.DEGREE, id],
    queryFn: () => collegeService.degrees.getDegreeById(id),
    enabled: !!id,
    ...options,
  });
}

export function useCreateDegree() {
  return useMutation({
    mutationFn: (data: CreateDegreeInput) => collegeService.degrees.createDegree(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.EDUCATION.DEGREES] });
    },
  });
}

export function useUpdateDegree() {
  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateDegreeInput }) => collegeService.degrees.updateDegree(id, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.EDUCATION.DEGREES] });
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.EDUCATION.DEGREE, variables.id] });
    },
  });
}

export function useDeleteDegree() {
  return useMutation({
    mutationFn: (id: string) => collegeService.degrees.deleteDegree(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.EDUCATION.DEGREES] });
    },
  });
}

// Branch Hooks
export function useGetAllBranches(params?: Record<string, any>, options = {}) {
  return useQuery({
    queryKey: [QUERY_KEYS.EDUCATION.BRANCHES, params],
    queryFn: () => collegeService.branches.getAllBranches(params),
    ...options,
  });
}

export function useGetBranchById(id: string, options = {}) {
  return useQuery({
    queryKey: [QUERY_KEYS.EDUCATION.BRANCH, id],
    queryFn: () => collegeService.branches.getBranchById(id),
    enabled: !!id,
    ...options,
  });
}

export function useCreateBranch() {
  return useMutation({
    mutationFn: (data: CreateBranchInput) => collegeService.branches.createBranch(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.EDUCATION.BRANCHES] });
    },
  });
}

export function useUpdateBranch() {
  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateBranchInput }) => collegeService.branches.updateBranch(id, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.EDUCATION.BRANCHES] });
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.EDUCATION.BRANCH, variables.id] });
    },
  });
}

export function useDeleteBranch() {
  return useMutation({
    mutationFn: (id: string) => collegeService.branches.deleteBranch(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.EDUCATION.BRANCHES] });
    },
  });
}

// College Subject Hooks
export function useGetAllCollegeSubjects(params?: Record<string, any>, options = {}) {
  return useQuery({
    queryKey: [QUERY_KEYS.EDUCATION.COLLEGE_SUBJECTS, params],
    queryFn: () => collegeService.collegeSubjects.getAllCollegeSubjects(params),
    ...options,
  });
}

export function useGetCollegeSubjectById(id: string, options = {}) {
  return useQuery({
    queryKey: [QUERY_KEYS.EDUCATION.COLLEGE_SUBJECT, id],
    queryFn: () => collegeService.collegeSubjects.getCollegeSubjectById(id),
    enabled: !!id,
    ...options,
  });
}

export function useCreateCollegeSubject() {
  return useMutation({
    mutationFn: (data: CreateCollegeSubjectInput) => collegeService.collegeSubjects.createCollegeSubject(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.EDUCATION.COLLEGE_SUBJECTS] });
    },
  });
}

export function useUpdateCollegeSubject() {
  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateCollegeSubjectInput }) => collegeService.collegeSubjects.updateCollegeSubject(id, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.EDUCATION.COLLEGE_SUBJECTS] });
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.EDUCATION.COLLEGE_SUBJECT, variables.id] });
    },
  });
}

export function useDeleteCollegeSubject() {
  return useMutation({
    mutationFn: (id: string) => collegeService.collegeSubjects.deleteCollegeSubject(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.EDUCATION.COLLEGE_SUBJECTS] });
    },
  });
}
