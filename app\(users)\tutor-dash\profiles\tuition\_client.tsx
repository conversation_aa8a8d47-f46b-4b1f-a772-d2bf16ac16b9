'use client';

import { useState } from 'react';
import { Backgrounds, VisitorsLoader } from '@/components/dashboard/shared/misc';
import TuitionInfoSection from './tuition-info-section';
import TeachingExperienceSection from './teaching-experience-section';
import TeachingSubjectsSection from './teaching-subjects-section';
import TuitionProfileTabs from './tuition-profile-tabs';
import { useGetTuitionInfo, useGetAllTeachingExperience, useGetAllTeachingSubjects } from '@/hooks/tutor/tuition-profile.hooks';

const TutorTuitionProfileHelper = () => {
  const [selectedTab, setSelectedTab] = useState<'info' | 'experience' | 'subjects'>('info');

  const { data: tuitionInfoData, isLoading: isTuitionInfoLoading } = useGetTuitionInfo();
  const { data: teachingExperienceData, isLoading: isExperienceLoading } = useGetAllTeachingExperience();
  const { data: teachingSubjectsData, isLoading: isSubjectsLoading } = useGetAllTeachingSubjects();

  const tuitionInfo = tuitionInfoData?.data?.tuitionInfo;
  const teachingExperiences = teachingExperienceData?.data?.teachingExperiences || [];
  const teachingSubjects = teachingSubjectsData?.data?.teachingSubjects || [];

  const tabCounts = {
    info: tuitionInfo ? 1 : 0,
    experience: teachingExperiences.length,
    subjects: teachingSubjects.length,
  };

  const isCurrentTabLoading = () => {
    switch (selectedTab) {
      case 'info':
        return isTuitionInfoLoading;
      case 'experience':
        return isExperienceLoading;
      case 'subjects':
        return isSubjectsLoading;
      default:
        return false;
    }
  };

  if (isCurrentTabLoading()) {
    return (
      <div className='bg-gradient-to-b from-white to-gray-50 p-8 rounded-3xl relative min-h-[650px] shadow-sm border border-gray-100 flex items-center justify-center'>
        <VisitorsLoader loaderType='skeleton' title='Tuition Profile' message='Loading your tuition profile...' />
      </div>
    );
  }

  const getCurrentContent = () => {
    switch (selectedTab) {
      case 'info':
        return <TuitionInfoSection tuitionInfo={tuitionInfo} />;
      case 'experience':
        return <TeachingExperienceSection experiences={teachingExperiences} />;
      case 'subjects':
        return <TeachingSubjectsSection subjects={teachingSubjects} />;
      default:
        return null;
    }
  };

  return (
    <section className='bg-gradient-to-b from-white to-gray-50 p-8 rounded-3xl relative min-h-[650px] shadow-sm border border-gray-100'>
      <Backgrounds variant='primary' direction='br2tl' />
      <div className='relative z-10 flex flex-col lg:flex-row gap-8'>
        <div className='lg:w-1/4 w-full'>
          <TuitionProfileTabs selectedTab={selectedTab} setSelectedTab={setSelectedTab} tabCounts={tabCounts} />
        </div>
        <div className='lg:w-3/4 w-full'>{getCurrentContent()}</div>
      </div>
    </section>
  );
};

export default TutorTuitionProfileHelper;
