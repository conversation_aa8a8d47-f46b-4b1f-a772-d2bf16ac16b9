import { Input } from '@/components/ui/input';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, Paperc<PERSON>, Camera } from 'lucide-react';
import React from 'react';

const TutorComplaintViewPage = () => {
  return (
    <div className='flex flex-col gap-4 h-full'>
      <div className='rounded-3xl bg-white p-6 flex justify-between items-center'>
        <div className='flex flex-col gap-1'>
          <h2 className='text-xl font-semibold'>Unable to Sign Up</h2>
          <p className='text-gray-500 text-sm'>Last Update: 23/7/2024, 11:43:56 am</p>
        </div>
        <span className='px-6 py-1.5 rounded-full text-sm bg-green-500 text-white'>Active</span>
      </div>

      <div className='flex-1 overflow-auto'>
        <ChatBox />
      </div>

      <div className='sticky bottom-0 bg-white p-6 rounded-3xl'>
        <MessageInput />
      </div>
    </div>
  );
};

const messages = [
  {
    id: 1,
    sender: '<PERSON>',
    senderAvatar: '/icons/avatar-female.webp',
    text: "That's awesome. I think our users will really appreciate the improvements.",
    time: '11:46 PM',
    type: 'received',
  },
  {
    id: 2,
    sender: 'You',
    senderAvatar: '/icons/avatar-male.webp',
    text: "Thank you, Bonnie! We're working hard to make sure everyone is satisfied.",
    time: '11:50 PM',
    type: 'sent',
  },
  {
    id: 3,
    sender: 'Bonnie Green',
    senderAvatar: '/icons/avatar-female.webp',
    text: 'Can we have a meeting tomorrow?',
    time: '08:30 AM',
    type: 'received',
  },
  { id: 4, sender: 'You', senderAvatar: '/icons/avatar-male.webp', text: 'Sure, I will schedule it for 10 AM.', time: '08:45 AM', type: 'sent' },
  { id: 5, sender: 'Bonnie Green', senderAvatar: '/icons/avatar-female.webp', text: 'Perfect, thank you!', time: '08:50 AM', type: 'received' },
  { id: 6, sender: 'You', senderAvatar: '/icons/avatar-male.webp', text: 'You are welcome.', time: '08:55 AM', type: 'sent' },
  { id: 7, sender: 'Bonnie Green', senderAvatar: '/icons/avatar-female.webp', text: 'Looking forward to it.', time: '09:00 AM', type: 'received' },
  { id: 8, sender: 'You', senderAvatar: '/icons/avatar-male.webp', text: 'See you then!', time: '09:05 AM', type: 'sent' },
];

const ChatBox = () => {
  return (
    <div className='bg-white p-6 rounded-3xl'>
      {messages.map((message) => (
        <div key={message.id} className={`flex items-start gap-2.5 mb-4 ${message.type === 'sent' ? 'justify-end' : ''}`}>
          {message.type === 'received' && (
            <div className='flex flex-col items-center gap-3'>
              <img className='w-8 h-8 rounded-full' src={message.senderAvatar} alt={`${message.sender} image`} />
              <span className='text-sm font-normal text-primaryColor'>
                <CheckCheck />
              </span>
            </div>
          )}

          <div className={`flex flex-col gap-1 w-full max-w-[320px] ${message.type === 'sent' ? 'text-right' : ''}`}>
            <div className={`flex items-center gap-2 ${message.type === 'sent' ? 'justify-end' : ''}`}>
              {message.type === 'sent' ? (
                <>
                  <span className='text-sm font-normal text-gray-500'>{message.time}</span>
                  <span className='text-sm font-semibold text-gray-900'>{message.sender}</span>
                </>
              ) : (
                <>
                  <span className='text-sm font-semibold text-gray-900'>{message.sender}</span>
                  <span className='text-sm font-normal text-gray-500'>{message.time}</span>
                </>
              )}
            </div>
            <div
              className={`flex flex-col leading-1.5 p-4 border-gray-200 ${
                message.type === 'sent' ? 'bg-blue-100 rounded-xl rounded-tr-none' : 'bg-gray-100 rounded-e-xl rounded-es-xl'
              }`}
            >
              <p className='text-sm font-normal text-start text-gray-900'>{message.text}</p>
            </div>
          </div>
          {message.type === 'sent' && (
            <div className='flex flex-col items-center gap-3'>
              <img className='w-8 h-8 rounded-full' src={message.senderAvatar} alt={`${message.sender} image`} />
              <span className={`text-sm font-normal ${message.type === 'sent' ? 'text-gray-500' : 'text-primaryColor'}`}>
                <CheckCheck />
              </span>
            </div>
          )}
        </div>
      ))}
    </div>
  );
};

const MessageInput = () => {
  return (
    <div className='flex items-center gap-2'>
      <button className='bg-gray-200 p-2 rounded-full'>
        <Image size={20} className='text-gray-500' />
      </button>
      <button className='bg-gray-200 p-2 rounded-full'>
        <Camera size={20} className='text-gray-500' />
      </button>
      <button className='bg-gray-200 p-2 rounded-full'>
        <Paperclip size={20} className='text-gray-500' />
      </button>
      <Input type='text' placeholder='Type your message...' className='flex-1 primary-input' />
      <button className='btn-default'>
        <Rocket size={20} />
      </button>
    </div>
  );
};

export default TutorComplaintViewPage;
