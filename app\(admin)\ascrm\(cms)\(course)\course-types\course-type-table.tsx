'use client';

import { useState, useEffect, useCallback } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { AdminDashActionLinks, HeadingBar, LimitFilter, MasterFilter, MasterTable, SectionWrapper } from '@/components/dashboard/ascrm/misc';
import { ErrorState } from '@/components/dashboard/ascrm/misc/StateUI';
import { filterTypeMaps, FilterTypeKey } from '@/constants';
import { FilterValues, FilterOption } from '@/types/api';
import { useGetAllCourseTypes, useDeleteCourseType, useGetCourseTypeById } from '@/hooks/education/course.hooks';
import { AdminViewButton, PrimaryFormModal, useModal, StatusToggle } from '@/components/dashboard/shared/misc';
import CourseTypeForm from './course-type-form';
import { courseTypeService } from '@/server/services/education/course.service';
import { QUERY_KEYS } from '@/lib/react-query/queryKeys';

const CourseTypeTable: React.FC = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const addCourseTypeModal = useModal();
  const editCourseTypeModal = useModal();

  const [selectedCourseTypeId, setSelectedCourseTypeId] = useState<string | null>(null);
  const [filters, setFilters] = useState<FilterValues>({});

  const updateFiltersFromParams = useCallback(() => {
    const initialFilters: FilterValues = {};
    searchParams.forEach((value, key) => {
      if (key !== 'page') {
        initialFilters[key] = value;
      }
    });
    setFilters(initialFilters);
  }, [searchParams]);

  useEffect(() => {
    updateFiltersFromParams();
  }, [updateFiltersFromParams]);

  const page = Number(searchParams.get('page')) || 1;

  const queryParams = { page, ...filters };

  const { data: courseTypesResponse, isLoading, error } = useGetAllCourseTypes(queryParams);
  const deleteItem = useDeleteCourseType();
  const {
    data: selectedCourseTypeData,
    isLoading: isLoadingSelectedCourseType,
    error: selectedCourseTypeError,
  } = useGetCourseTypeById(selectedCourseTypeId || '');

  const courseTypes = courseTypesResponse?.data?.courseTypes || [];
  const pagination = courseTypesResponse?.data?.pagination;

  const handleEditCourseType = (id: string) => {
    setSelectedCourseTypeId(id);
    editCourseTypeModal.open();
  };

  if (error) {
    return <ErrorState message={error?.message || 'An unexpected error occurred.'} />;
  }

  const headers = ['Name', 'Status', 'Actions'];

  const rows = courseTypes.map((courseType) => {
    return {
      id: courseType._id,
      values: [
        <p className='font-medium'>{courseType.name}</p>,
        <StatusToggle
          id={courseType._id}
          isActive={courseType.isActive}
          updateFn={courseTypeService.updateCourseType}
          queryKey={[QUERY_KEYS.EDUCATION.COURSE_TYPES, [QUERY_KEYS.EDUCATION.COURSE_TYPES, filters]]}
          entityName='Course Type'
        />,
        <AdminDashActionLinks
          delete
          editAction={() => handleEditCourseType(courseType._id)}
          basePath='/course-types'
          id={courseType._id}
          itemName={courseType.name}
          deleteItem={() => deleteItem.mutateAsync(courseType._id)}
          manualAction={[<AdminViewButton variant='icon' href={`/courses?courseType=${courseType._id}`} label={`View ${courseType.name} Courses`} />]}
        />,
      ],
    };
  });

  const filterOptions: FilterOption[] = [
    {
      type: filterTypeMaps.input.key as FilterTypeKey,
      label: 'Course Type Name',
      key: 'name',
    },
  ];

  const handleApplyFilters = (newFilters: FilterValues) => {
    const params = new URLSearchParams(searchParams);
    Object.entries(newFilters).forEach(([key, value]) => {
      if (value && typeof value === 'string') {
        params.set(key, value);
      } else {
        params.delete(key);
      }
    });
    params.set('page', '1');
    router.push(`?${params.toString()}`);
  };

  const handleResetFilters = () => {
    router.push(`?page=1`);
  };

  return (
    <SectionWrapper>
      <HeadingBar
        title='IT Course Types'
        elements={[
          <LimitFilter
            key='limit'
            value={(filters.limit as string) || '10'}
            onChange={(value: string) => {
              handleApplyFilters({ limit: value });
            }}
          />,
          <MasterFilter
            key='filter'
            filterOptions={filterOptions}
            onApplyFilters={handleApplyFilters}
            onResetFilters={handleResetFilters}
            initialFilters={filters}
          />,
        ]}
        addAction={addCourseTypeModal.open}
      />
      <MasterTable headers={headers} rows={rows} isLoading={isLoading} pagination={pagination} />

      {/* Add Course Type Modal */}
      <PrimaryFormModal
        isOpen={addCourseTypeModal.isOpen}
        onClose={addCourseTypeModal.close}
        title='Add New IT Course Type'
        subtitle='Create a new IT course type for your platform'
        size='sm'
        showSparkles={true}
      >
        <CourseTypeForm onSuccess={addCourseTypeModal.close} />
      </PrimaryFormModal>

      {/* Edit Course Type Modal */}
      <PrimaryFormModal
        isOpen={editCourseTypeModal.isOpen}
        onClose={() => {
          editCourseTypeModal.close();
          setSelectedCourseTypeId(null);
        }}
        title='Edit IT Course Type'
        subtitle='Update existing IT course type details'
        size='sm'
        isLoading={isLoadingSelectedCourseType}
        loadingTitle='Loading Course Type Data'
        loadingMessage='Please wait while we fetch the course type details...'
        isError={!!selectedCourseTypeError}
        errorMessage={selectedCourseTypeError?.message}
      >
        {selectedCourseTypeData?.data?.courseType && (
          <CourseTypeForm data={selectedCourseTypeData.data.courseType} onSuccess={editCourseTypeModal.close} />
        )}
      </PrimaryFormModal>
    </SectionWrapper>
  );
};

export default CourseTypeTable;
