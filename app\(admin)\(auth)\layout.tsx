import type { Metada<PERSON> } from 'next';
import { Inter } from 'next/font/google';
import '@/app/globals.css';
import QueryProvider from '@/lib/react-query/QueryProvider';
const inter = Inter({ subsets: ['latin'] });
import ToastProvider from '@/components/providers/ToastProvider';

export const metadata: Metadata = {
  title: 'Perfect Tutor - Admin Authentication',
  description: 'Login and register to access Perfect Tutor admin panel',
};

export default function AuthLayout({ children }: Readonly<{ children: React.ReactNode }>) {
  return (
    <html lang='en'>
      <body className={inter.className}>
        <QueryProvider>{children}</QueryProvider>
        <ToastProvider />
      </body>
    </html>
  );
}
