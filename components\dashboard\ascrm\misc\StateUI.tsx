'use client';

import { Loader, Home, RefreshCw, AlertTriangle, HelpCircle, Search, FileX, Plus } from 'lucide-react';
import AdminDashLink from './AdminDashLink';
import { motion } from 'framer-motion';

export const ErrorState: React.FC<{ message: string }> = ({ message }) => (
  <div className='relative overflow-hidden min-h-[600px] flex items-center justify-center py-12 bg-gradient-to-b from-white to-red-50'>
    <div className='absolute top-0 right-0 w-full h-full overflow-hidden opacity-10 pointer-events-none'>
      <div className='absolute -right-10 -top-10 w-72 h-72 bg-red-300 rounded-full'></div>
      <div className='absolute right-20 top-20 w-40 h-40 bg-red-400 rounded-full'></div>
      <div className='absolute left-20 bottom-20 w-56 h-56 bg-red-300 rounded-full'></div>
      <div className='absolute -left-10 -bottom-10 w-72 h-72 bg-red-400 rounded-full'></div>
    </div>

    <div className='flex flex-col items-center w-full max-w-3xl mx-auto px-6 z-10'>
      <motion.div
        initial={{ scale: 0.8, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        transition={{ duration: 0.7, ease: 'easeOut' }}
        className='mb-4 relative'
      >
        <div className='relative'>
          <svg className='w-72 h-72' viewBox='0 0 400 400' fill='none' xmlns='http://www.w3.org/2000/svg'>
            {/* Background elements */}
            <motion.circle cx='200' cy='200' r='160' fill='#FEF2F2' initial={{ scale: 0 }} animate={{ scale: 1 }} transition={{ duration: 0.5 }} />

            <motion.path
              d='M200 40C111.6 40 40 111.6 40 200C40 288.4 111.6 360 200 360C288.4 360 360 288.4 360 200C360 111.6 288.4 40 200 40Z'
              stroke='#FECACA'
              strokeWidth='8'
              strokeLinecap='round'
              initial={{ pathLength: 0 }}
              animate={{ pathLength: 1 }}
              transition={{ duration: 1.5, ease: 'easeInOut' }}
            />

            {/* Error icon */}
            <motion.circle
              cx='200'
              cy='200'
              r='100'
              fill='#FEE2E2'
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ duration: 0.5, delay: 0.3 }}
            />

            <motion.path
              d='M200 120V200'
              stroke='#DC2626'
              strokeWidth='16'
              strokeLinecap='round'
              initial={{ pathLength: 0, opacity: 0 }}
              animate={{ pathLength: 1, opacity: 1 }}
              transition={{ duration: 0.8, delay: 0.8, ease: 'easeOut' }}
            />

            <motion.circle
              cx='200'
              cy='250'
              r='8'
              fill='#DC2626'
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ duration: 0.3, delay: 1.6 }}
            />

            {/* Decorative elements */}
            <motion.path
              d='M120 80L140 100M280 80L260 100M120 320L140 300M280 320L260 300'
              stroke='#F87171'
              strokeWidth='6'
              strokeLinecap='round'
              initial={{ pathLength: 0, opacity: 0 }}
              animate={{ pathLength: 1, opacity: 0.7 }}
              transition={{ duration: 0.8, delay: 1.2 }}
            />
          </svg>

          <motion.div
            className='absolute -top-4 -right-4 bg-gradient-to-br from-red-500 to-red-600 text-white p-4 rounded-2xl shadow-lg'
            initial={{ scale: 0, rotate: -20 }}
            animate={{ scale: 1, rotate: 0 }}
            transition={{
              type: 'spring',
              stiffness: 260,
              damping: 20,
              delay: 1.5,
            }}
          >
            <AlertTriangle size={28} />
          </motion.div>
        </div>
      </motion.div>

      <motion.h1
        initial={{ y: 30, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.7, delay: 0.3 }}
        className='text-4xl font-bold text-gray-800 text-center mb-4'
      >
        {message || 'An unexpected error occurred.'}
      </motion.h1>

      <motion.p
        initial={{ y: 30, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.7, delay: 0.5 }}
        className='text-lg text-gray-600 text-center max-w-xl mb-10'
      >
        {message
          ? 'We encountered an issue while processing your request. Our team has been notified and is working to resolve it.'
          : 'Something went wrong. Please try again later or contact support if the issue persists.'}
      </motion.p>

      <motion.div
        initial={{ y: 30, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.7, delay: 0.7 }}
        className='flex flex-col sm:flex-row gap-4'
      >
        <AdminDashLink
          className='group bg-white border border-gray-200 hover:border-red-200 rounded-xl flex items-center gap-2 px-5 py-2 text-gray-700 hover:text-red-600 font-medium transition-all shadow-sm hover:shadow'
          href='/'
        >
          <span className='bg-gray-100 group-hover:bg-red-100 p-2 rounded-lg transition-colors'>
            <Home size={18} />
          </span>
          <span>Back to Dashboard</span>
        </AdminDashLink>

        <button
          onClick={() => window.location.reload()}
          className='group bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 rounded-xl flex items-center gap-2 px-5 py-2 text-white font-medium transition-all shadow-sm hover:shadow'
        >
          <span className='bg-red-600 group-hover:bg-red-700 p-2 rounded-lg transition-colors'>
            <RefreshCw size={18} />
          </span>
          <span>Try Again</span>
        </button>
      </motion.div>
    </div>
  </div>
);

export const NoDataFoundState: React.FC = () => {
  return (
    <div className='relative overflow-hidden min-h-[300px] flex items-center justify-center py-12 bg-gradient-to-b from-white to-blue-50'>
      <div className='absolute top-0 right-0 w-full h-full overflow-hidden opacity-10 pointer-events-none'>
        <div className='absolute -right-10 -top-10 w-72 h-72 bg-blue-300 rounded-full'></div>
        <div className='absolute right-20 top-20 w-40 h-40 bg-blue-400 rounded-full'></div>
        <div className='absolute left-20 bottom-20 w-56 h-56 bg-blue-300 rounded-full'></div>
        <div className='absolute -left-10 -bottom-10 w-72 h-72 bg-blue-400 rounded-full'></div>
      </div>

      <div className='flex flex-col items-center w-full max-w-3xl mx-auto px-6 z-10'>
        <motion.div
          initial={{ scale: 0.8, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ duration: 0.7, ease: 'easeOut' }}
          className='mb-2 relative'
        >
          <div className='relative'>
            <svg className='w-72 h-72' viewBox='0 0 400 400' fill='none' xmlns='http://www.w3.org/2000/svg'>
              {/* Background elements */}
              <motion.circle cx='200' cy='200' r='160' fill='#EFF6FF' initial={{ scale: 0 }} animate={{ scale: 1 }} transition={{ duration: 0.5 }} />

              <motion.path
                d='M200 40C111.6 40 40 111.6 40 200C40 288.4 111.6 360 200 360C288.4 360 360 288.4 360 200C360 111.6 288.4 40 200 40Z'
                stroke='#BFDBFE'
                strokeWidth='8'
                strokeLinecap='round'
                initial={{ pathLength: 0 }}
                animate={{ pathLength: 1 }}
                transition={{ duration: 1.5, ease: 'easeInOut' }}
              />

              {/* Empty folder illustration */}
              <motion.path
                d='M120 140H280C291 140 300 149 300 160V260C300 271 291 280 280 280H120C109 280 100 271 100 260V160C100 149 109 140 120 140Z'
                fill='#DBEAFE'
                stroke='#3B82F6'
                strokeWidth='8'
                initial={{ pathLength: 0, opacity: 0 }}
                animate={{ pathLength: 1, opacity: 1 }}
                transition={{ duration: 1, ease: 'easeInOut' }}
              />

              <motion.path
                d='M100 160L130 120H180L210 160'
                stroke='#3B82F6'
                strokeWidth='8'
                strokeLinecap='round'
                initial={{ pathLength: 0, opacity: 0 }}
                animate={{ pathLength: 1, opacity: 1 }}
                transition={{ duration: 0.8, delay: 0.5 }}
              />

              {/* Empty state lines */}
              <motion.path
                d='M150 200H250M150 230H220'
                stroke='#60A5FA'
                strokeWidth='8'
                strokeLinecap='round'
                initial={{ pathLength: 0, opacity: 0 }}
                animate={{ pathLength: 1, opacity: 1 }}
                transition={{ duration: 0.8, delay: 0.8 }}
              />

              {/* Decorative elements */}
              <motion.path
                d='M320 180L340 180M320 220L340 220M60 180L80 180M60 220L80 220'
                stroke='#93C5FD'
                strokeWidth='6'
                strokeLinecap='round'
                initial={{ pathLength: 0, opacity: 0 }}
                animate={{ pathLength: 1, opacity: 0.7 }}
                transition={{ duration: 0.8, delay: 1.2 }}
              />
            </svg>

            <motion.div
              className='absolute -top-4 -right-4 bg-gradient-to-br from-blue-500 to-blue-600 text-white p-4 rounded-2xl shadow-lg'
              initial={{ scale: 0, rotate: -20 }}
              animate={{ scale: 1, rotate: 0 }}
              transition={{
                type: 'spring',
                stiffness: 260,
                damping: 20,
                delay: 1.5,
              }}
            >
              <FileX size={28} />
            </motion.div>
          </div>
        </motion.div>

        <motion.h1
          initial={{ y: 30, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.7, delay: 0.3 }}
          className='text-4xl font-bold text-gray-800 text-center mb-4'
        >
          No Data Available
        </motion.h1>

        <motion.p
          initial={{ y: 30, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.7, delay: 0.5 }}
          className='text-lg text-gray-600 text-center max-w-xl mb-10'
        >
          No records found. Try adjusting your filters or adding new data.
        </motion.p>

        <motion.div
          initial={{ y: 30, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.7, delay: 0.7 }}
          className='flex flex-col sm:flex-row gap-4'
        >
          <AdminDashLink
            className='group bg-white border border-gray-200 hover:border-blue-200 rounded-xl flex items-center gap-2 px-5 py-2 text-gray-700 hover:text-blue-600 font-medium transition-all shadow-sm hover:shadow'
            href='/'
          >
            <span className='bg-gray-100 group-hover:bg-blue-100 p-2 rounded-lg transition-colors'>
              <Home size={18} />
            </span>
            <span>Back to Dashboard</span>
          </AdminDashLink>

          <button
            onClick={() => window.location.reload()}
            className='group bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 rounded-xl flex items-center gap-2 px-5 py-2 text-white font-medium transition-all shadow-sm hover:shadow'
          >
            <span className='bg-blue-600 group-hover:bg-blue-700 p-2 rounded-lg transition-colors'>
              <RefreshCw size={18} />
            </span>
            <span>Refresh Data</span>
          </button>
        </motion.div>
      </div>
    </div>
  );
};

interface ILoadingState {
  message?: string;
}

export const LoadingState: React.FC<ILoadingState> = ({ message = 'Loading, please wait...' }) => (
  <div className='relative overflow-hidden min-h-[600px] flex items-center justify-center py-12 bg-gradient-to-b from-white to-purple-50'>
    <div className='absolute top-0 right-0 w-full h-full overflow-hidden opacity-10 pointer-events-none'>
      <div className='absolute -right-10 -top-10 w-72 h-72 bg-purple-300 rounded-full'></div>
      <div className='absolute right-20 top-20 w-40 h-40 bg-purple-400 rounded-full'></div>
      <div className='absolute left-20 bottom-20 w-56 h-56 bg-purple-300 rounded-full'></div>
      <div className='absolute -left-10 -bottom-10 w-72 h-72 bg-purple-400 rounded-full'></div>
    </div>

    <div className='flex flex-col items-center w-full max-w-3xl mx-auto px-6 z-10'>
      <motion.div
        initial={{ scale: 1, opacity: 1 }}
        animate={{ scale: 1, opacity: 1 }}
        transition={{ duration: 0, ease: 'easeOut' }}
        className='mb-10 relative'
      >
        <div className='relative'>
          <svg className='w-72 h-72' viewBox='0 0 400 400' fill='none' xmlns='http://www.w3.org/2000/svg'>
            {/* Background elements */}
            <motion.circle cx='200' cy='200' r='160' fill='#F5F3FF' initial={{ scale: 1 }} animate={{ scale: 1 }} transition={{ duration: 0 }} />

            <motion.path
              d='M200 40C111.6 40 40 111.6 40 200C40 288.4 111.6 360 200 360C288.4 360 360 288.4 360 200C360 111.6 288.4 40 200 40Z'
              stroke='#DDD6FE'
              strokeWidth='8'
              strokeLinecap='round'
              initial={{ pathLength: 1 }}
              animate={{ pathLength: 1 }}
              transition={{ duration: 0, ease: 'easeInOut' }}
            />

            {/* Loading animation */}
            <motion.circle
              cx='200'
              cy='200'
              r='100'
              stroke='#C4B5FD'
              strokeWidth='16'
              strokeDasharray='628'
              strokeDashoffset='628'
              strokeLinecap='round'
              animate={{
                strokeDashoffset: [628, 0],
                rotate: [0, 360],
              }}
              transition={{
                strokeDashoffset: { duration: 3, repeat: Infinity, delay: 0 },
                rotate: { duration: 3, repeat: Infinity, ease: 'linear', delay: 0 },
              }}
            />

            <motion.circle
              cx='200'
              cy='200'
              r='70'
              stroke='#A78BFA'
              strokeWidth='12'
              strokeDasharray='440'
              strokeDashoffset='440'
              strokeLinecap='round'
              animate={{
                strokeDashoffset: [440, 0],
                rotate: [360, 0],
              }}
              transition={{
                strokeDashoffset: { duration: 3, repeat: Infinity, delay: 0 },
                rotate: { duration: 3, repeat: Infinity, ease: 'linear', delay: 0 },
              }}
            />

            <motion.circle
              cx='200'
              cy='200'
              r='40'
              fill='#8B5CF6'
              initial={{ scale: 1 }}
              animate={{ scale: [0.8, 1, 0.8] }}
              transition={{
                duration: 2,
                repeat: Infinity,
                repeatType: 'reverse',
                times: [0, 0.5, 1],
                delay: 0,
              }}
            />

            {/* Decorative elements */}
            <motion.path
              d='M120 80L140 100M280 80L260 100M120 320L140 300M280 320L260 300'
              stroke='#A78BFA'
              strokeWidth='6'
              strokeLinecap='round'
              initial={{ pathLength: 0, opacity: 0.7 }}
              animate={{ pathLength: 1, opacity: 0.7 }}
              transition={{ duration: 0.8, delay: 0 }}
            />
          </svg>

          <motion.div
            className='absolute -bottom-4 -right-4 bg-gradient-to-br from-purple-500 to-purple-600 text-white p-4 rounded-2xl shadow-lg'
            initial={{ scale: 1, rotate: 0 }}
            animate={{
              rotate: 360,
              scale: [1, 1.1, 1],
            }}
            transition={{
              rotate: { duration: 2, repeat: Infinity, ease: 'linear', delay: 0 },
              scale: { duration: 1.5, repeat: Infinity, repeatType: 'reverse', delay: 0 },
            }}
          >
            <Loader size={28} />
          </motion.div>
        </div>
      </motion.div>

      <motion.h1
        initial={{ y: 0, opacity: 1 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0, delay: 0 }}
        className='text-4xl font-bold text-purple-800 text-center mb-4'
      >
        {message}
      </motion.h1>

      <motion.p
        initial={{ y: 0, opacity: 1 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0, delay: 0 }}
        className='text-lg text-gray-600 text-center max-w-xl mb-10'
      >
        We are fetching your data. Please be patient, it may take a moment. This won't take long.
      </motion.p>

      <motion.div initial={{ opacity: 1 }} animate={{ opacity: 1 }} transition={{ duration: 0, delay: 0 }}>
        <AdminDashLink
          className='group bg-white border border-gray-200 hover:border-purple-200 rounded-xl flex items-center gap-2 px-5 py-2 text-gray-700 hover:text-purple-600 font-medium transition-all shadow-sm hover:shadow'
          href='/'
        >
          <span className='bg-gray-100 group-hover:bg-purple-100 p-2 rounded-lg transition-colors'>
            <Home size={18} />
          </span>
          <span>Back to Dashboard</span>
        </AdminDashLink>
      </motion.div>

      <motion.div
        initial={{ opacity: 1 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0, delay: 0 }}
        className='mt-10 flex items-center gap-2 text-gray-500 text-sm'
      >
        <Search size={14} />
        <span>Searching through database records...</span>
      </motion.div>
    </div>
  </div>
);

interface LoadingSpinnerProps {
  message?: string;
}

export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({ message = 'Loading...' }) => {
  return (
    <div className='flex flex-col items-center justify-center p-4 space-y-4'>
      <div className='relative w-16 h-16'>
        {/* Outer spinning circle */}
        <motion.div className='absolute inset-0 rounded-full border-4 border-gray-100' style={{ borderRadius: '50%' }} />

        <motion.div
          className='absolute inset-0 rounded-full border-4 border-transparent border-t-red-500'
          style={{ borderRadius: '50%' }}
          animate={{ rotate: 360 }}
          transition={{ duration: 1.2, repeat: Infinity, ease: 'linear' }}
        />

        {/* Middle spinning circle */}
        <motion.div className='absolute inset-[4px] rounded-full border-4 border-gray-50' style={{ borderRadius: '50%' }} />

        <motion.div
          className='absolute inset-[4px] rounded-full border-4 border-transparent border-t-red-400'
          style={{ borderRadius: '50%' }}
          animate={{ rotate: -360 }}
          transition={{ duration: 1.8, repeat: Infinity, ease: 'linear' }}
        />

        {/* Inner pulsing circle */}
        <motion.div className='absolute inset-0 flex items-center justify-center'>
          <motion.div
            className='w-6 h-6 bg-red-500 rounded-full'
            animate={{
              scale: [0.8, 1.2, 0.8],
              opacity: [0.7, 1, 0.7],
            }}
            transition={{
              duration: 1.5,
              repeat: Infinity,
              ease: 'easeInOut',
            }}
          />
        </motion.div>
      </div>

      <motion.div className='flex flex-col items-center' initial={{ opacity: 0, y: 5 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.3 }}>
        <motion.p className='text-sm font-medium text-gray-800' animate={{ opacity: [0.7, 1, 0.7] }} transition={{ duration: 1.5, repeat: Infinity }}>
          {message}
        </motion.p>

        <motion.div
          className='flex space-x-1 mt-1'
          animate={{ opacity: [0.5, 1, 0.5] }}
          transition={{ duration: 1.5, repeat: Infinity, repeatType: 'reverse' }}
        >
          <span className='w-1 h-1 bg-red-400 rounded-full'></span>
          <span className='w-1 h-1 bg-red-400 rounded-full'></span>
          <span className='w-1 h-1 bg-red-400 rounded-full'></span>
        </motion.div>
      </motion.div>
    </div>
  );
};
