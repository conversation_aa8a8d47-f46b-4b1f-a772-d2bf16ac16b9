'use client';

import { <PERSON><PERSON>, KeyValueGrid, VisitorsLoader } from '@/components/dashboard/shared/misc';

interface AboutInfoProps {
  data: Record<string, any>;
  showPromotionBadge?: boolean;
  isLoading?: boolean;
  error?: Error | null;
}

const AboutInfo: React.FC<AboutInfoProps> = ({ data, showPromotionBadge = false, isLoading = false, error = null }) => {
  return (
    <div className='bg-white rounded-3xl p-6 w-full'>
      <div className='flex items-start justify-between mb-4'>
        <h2 className='text-lg font-semibold'>About</h2>
        {showPromotionBadge && !isLoading && !error && (
          <div className='bg-red-500 py-1 px-4 text-white rounded-full animate-pulse text-sm shrink-0'>Promotion Exceed</div>
        )}
      </div>

      {isLoading ? (
        <VisitorsLoader title='About' message='Loading user information...' size='sm' variant='minimal' />
      ) : error ? (
        <Alert type='error' title='Error' message={error.message || 'Failed to load user information'} />
      ) : (
        <KeyValueGrid rows={1} className='gap-2' data={data} />
      )}
    </div>
  );
};

export default AboutInfo;
