'use client';

import { Form } from '@/components/ui/form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import { toast } from 'react-toastify';
import { PrimaryInput, PrimarySwitchInput, SubmitButton } from '@/components/forms';
import { IAddUpdateFormProps } from '@/types/api';
import { useCreateExam, useUpdateExam } from '@/hooks/education/exam.hooks';
import { IExamDocument } from '@/server/services/education/exam.service';
import { createExamSchema } from '@/validation/schemas/education/exam.schema';
import { extractId } from '@/validation/utils/form.utils';

interface IExamFormProps extends IAddUpdateFormProps<IExamDocument> {
  onSuccess: () => void;
  examCategoryId?: string | null;
}

const ExamForm: React.FC<IExamFormProps> = ({ data, onSuccess, examCategoryId }) => {
  const { mutateAsync: createExam, isPending: isCreating } = useCreateExam();
  const { mutateAsync: updateExam, isPending: isUpdating } = useUpdateExam();

  const examId = data?._id;
  const isSubmitting = isCreating || isUpdating;

  const form = useForm<z.infer<typeof createExamSchema>>({
    resolver: zodResolver(createExamSchema),
    defaultValues: {
      name: data?.name || '',
      examCategory: extractId(data?.examCategory) || examCategoryId || '',
      isActive: data?.isActive !== undefined ? data.isActive : true,
    },
  });

  const onSubmit = async (values: z.infer<typeof createExamSchema>) => {
    try {
      const result = examId 
        ? await updateExam({ id: examId, data: values }) 
        : await createExam(values);
        
      if (!result.success) throw new Error(result.message || 'Operation failed');

      toast.success(result.message);
      onSuccess();
    } catch (error: any) {
      toast.error(error.message || 'An error occurred');
    }
  };

  return (
    <section className='relative'>
      <div className='bg-white rounded-lg shadow-sm p-6 max-w-2xl'>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className='grid grid-cols-1 gap-6 items-start justify-start'>
            {/* Name field */}
            <PrimaryInput form={form} name='name' label='Exam Name' placeholder='Enter exam name' required />

            {/* Is Active */}
            <PrimarySwitchInput
              form={form}
              name='isActive'
              label='Is this exam active?'
              description='Making it inactive will hide it from the website.'
            />

            <SubmitButton
              submittingLabel={examId ? 'Updating...' : 'Creating...'}
              label={examId ? 'Update Exam' : 'Create Exam'}
              isSubmitting={isSubmitting}
              disabled={isSubmitting}
              className='w-fit'
            />
          </form>
        </Form>
      </div>
    </section>
  );
};

export default ExamForm;
