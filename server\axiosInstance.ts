import axios from 'axios';

export const NETWORK_TIMEOUT = 30000; // 30 seconds

const isProduction = process.env.NODE_ENV === 'production';
export const baseURL = isProduction ? 'https://api-v1.perfecttutor.in' : 'http://localhost:3040';
export const baseAPIURL = `${baseURL}/api/v1`;

const axiosInstance = axios.create({
  baseURL: baseAPIURL,
  withCredentials: true,
  timeout: NETWORK_TIMEOUT,
  headers: { 'Content-Type': 'application/json' },
});

export default axiosInstance;
