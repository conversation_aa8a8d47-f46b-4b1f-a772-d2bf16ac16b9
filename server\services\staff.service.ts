import apiClient from '@/server/apiClient';
import { IAPIResponse, IPagination } from '@/types/api';
import { CreateStaffInput, UpdateStaffInput, UpdateStaffPasswordInput, StaffLoginInput } from '@/validation/schemas/staff.schema';

export interface IStaffMember extends Omit<StaffLoginInput, 'confirmPassword' | 'password'> {
  _id: string;
  createdAt: string;
  updatedAt: string;
}

export interface IStaffResponse {
  staff: IStaffMember;
}

export interface IStaffAuthResponse extends IStaffResponse {
  token?: string;
}

export interface IStaffListResponse {
  staff: IStaffMember[];
  pagination: IPagination;
}

// Staff authentication service (staff)
const staffAuthService = {
  login: async (data: StaffLoginInput): Promise<IAPIResponse<IStaffAuthResponse>> => await apiClient.post<IStaffAuthResponse>('/staff/login', data),
  logout: async (): Promise<IAPIResponse> => await apiClient.delete<IAPIResponse>('/staff/logout'),
  refreshToken: async (): Promise<IAPIResponse<IStaffAuthResponse>> => await apiClient.post<IStaffAuthResponse>('/staff/refresh-token', {}),
  updatePassword: async (data: UpdateStaffPasswordInput): Promise<IAPIResponse> =>
    await apiClient.patch<IAPIResponse>('/staff/update-password', data),
};

// Staff management service (admin only)
const staffService = {
  createStaff: async (data: CreateStaffInput) => await apiClient.post<IStaffResponse>('/staff', data),
  getAllStaff: async () => await apiClient.get<IStaffListResponse>('/staff'),
  getStaffById: async (id: string) => await apiClient.get<IStaffResponse>(`/staff/${id}`),
  updateStaff: async (id: string, data: UpdateStaffInput) => await apiClient.patch<IStaffResponse>(`/staff/${id}`, data),
  deleteStaff: async (id: string) => await apiClient.delete<IAPIResponse>(`/staff/${id}`),
  getCurrentStaff: async () => await apiClient.get<IStaffResponse>('/staff/me'),
};

export { staffService, staffAuthService };
