'use client';

import { FileText, User, MapPinned, CalendarArrowUp } from 'lucide-react';
import { PrimaryModalWithHeader } from '@/components/dashboard/shared/misc';
import { IEnquiryDocument } from '@/server/services/enquiry.service';
import { formatDate } from '@/lib/date.utils';
import { tutorGenderMap, startTimeMap, deliveryModeMap, enquiryStatusMap } from '@/validation/schemas/enquiry.maps';
import { serviceCategoryMap } from '@/validation/schemas/education/index.maps';
import { cn } from '@/lib/utils';
import { getCategoryIcon, getStatusInfo, getSubjectDisplay } from './utils';

interface LeadViewModalProps {
  isOpen: boolean;
  onClose: () => void;
  enquiry: IEnquiryDocument | null;
}

const LeadViewModal = ({ isOpen, onClose, enquiry }: LeadViewModalProps) => {
  if (!enquiry) return null;

  const categoryDisplay = serviceCategoryMap[enquiry.category]?.label || 'Unknown';
  const createdDate = formatDate(enquiry.createdAt);
  const statusInfo = getStatusInfo(enquiry.status);
  const deliveryModesDisplay = enquiry.deliveryModes.map((mode) => deliveryModeMap[mode as keyof typeof deliveryModeMap]?.label || mode).join(', ');

  const studentName = enquiry.childProfileDetails?.fullName || 'Unknown Student';
  const CategoryIcon = getCategoryIcon(enquiry.category);

  return (
    <PrimaryModalWithHeader isOpen={isOpen} onClose={onClose} variant='secondary' maxWidth='max-w-5xl'>
      <div className='bg-white rounded-b-xl overflow-hidden'>
        <div className='relative px-8 pt-6'>
          <div className='flex flex-col md:flex-row md:items-center justify-between gap-4 border-b border-gray-100 pb-6'>
            <div className='flex items-center gap-5'>
              <div className={cn('p-3 rounded-xl shadow-md flex items-center justify-center', `bg-gradient-to-br ${statusInfo.gradient}`)}>
                <div className='relative z-10 text-white'>
                  <CategoryIcon size={22} />
                </div>
              </div>
              <div className='flex flex-col items-start gap-0.5'>
                <h2 className='text-lg font-bold text-gray-800'>{studentName}</h2>
                <div className='flex items-center gap-3'>
                  <span className='text-sm font-medium text-gray-600'>{createdDate}</span>
                  <div className='flex items-center gap-2 text-primaryColor-500'>
                    <span className='text-sm font-medium'>ID: {enquiry._id.substring(0, 8)}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className='px-8 pb-8 mt-6'>
          <div className='grid grid-cols-1 md:grid-cols-2 gap-8'>
            {/* Left column */}
            <div>
              {/* Location information */}
              <div className='mb-6'>
                <h3 className='text-base font-semibold text-gray-700 flex items-center gap-2 mb-3'>
                  <div className='p-1.5 rounded-lg bg-blue-50'>
                    <MapPinned size={18} className='text-blue-600' />
                  </div>
                  <span>Location Information</span>
                </h3>
                <div className='bg-gradient-to-br from-blue-50/50 to-white border border-blue-100 rounded-xl p-4 shadow-sm capitalize'>
                  <p className='text-gray-800 font-medium'>{enquiry.location.address}</p>
                  {enquiry.location.landmark && (
                    <div className='mt-2 flex items-center gap-2'>
                      <span className='text-xs px-2 py-0.5 bg-blue-100 text-blue-700 rounded-full'>Landmark</span>
                      <p className='text-gray-600 text-sm'>{enquiry.location.landmark}</p>
                    </div>
                  )}
                </div>
              </div>

              {/* Schedule & Preferences */}
              <div className='mb-6'>
                <h3 className='text-base font-semibold text-gray-700 flex items-center gap-2 mb-3'>
                  <div className='p-1.5 rounded-lg bg-purple-50'>
                    <CalendarArrowUp size={18} className='text-purple-600' />
                  </div>
                  <span>Schedule & Preferences</span>
                </h3>
                <div className='bg-gradient-to-br from-purple-50/50 to-white border border-purple-100 rounded-xl p-4 shadow-sm'>
                  <div className='grid grid-cols-2 gap-5'>
                    <div className='p-3 bg-white rounded-lg border border-purple-100'>
                      <p className='text-xs font-medium text-purple-600 mb-1'>Classes Per Week</p>
                      <p className='font-semibold text-gray-800'>{enquiry.classesPerWeek}</p>
                    </div>
                    <div className='p-3 bg-white rounded-lg border border-purple-100'>
                      <p className='text-xs font-medium text-purple-600 mb-1'>Preferred Start Time</p>
                      <p className='font-semibold text-gray-800'>
                        {startTimeMap[enquiry.startTime as keyof typeof startTimeMap]?.label || enquiry.startTime}
                      </p>
                    </div>
                    <div className='p-3 bg-white rounded-lg border border-purple-100'>
                      <p className='text-xs font-medium text-purple-600 mb-1'>Preferred Tutor</p>
                      <p className='font-semibold text-gray-800'>
                        {tutorGenderMap[enquiry.tutorGender as keyof typeof tutorGenderMap]?.label || enquiry.tutorGender}
                      </p>
                    </div>
                    <div className='p-3 bg-white rounded-lg border border-purple-100'>
                      <p className='text-xs font-medium text-purple-600 mb-1'>Mode of Delivery</p>
                      <p className='font-semibold text-gray-800'>{deliveryModesDisplay}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Right column - Category specific details */}
            <div>
              <div className='mb-6'>
                <h3 className='text-base font-semibold text-gray-700 flex items-center gap-2 mb-3'>
                  <div className='p-1.5 rounded-lg bg-amber-50'>
                    <User size={18} className='text-amber-600' />
                  </div>
                  <span>{categoryDisplay} Details</span>
                </h3>
                <div className='bg-gradient-to-br from-amber-50/50 to-white border border-amber-100 rounded-xl p-4 shadow-sm'>
                  {/* School specific fields */}
                  {enquiry.category === 'schools' && (
                    <div className='grid grid-cols-2 gap-4'>
                      <div className='p-3 bg-white rounded-lg border border-amber-100'>
                        <p className='text-xs font-medium text-amber-600 mb-1'>Board</p>
                        <p className='font-semibold text-gray-800'>{enquiry.boardDetails?.name || 'Not specified'}</p>
                      </div>
                      <div className='p-3 bg-white rounded-lg border border-amber-100'>
                        <p className='text-xs font-medium text-amber-600 mb-1'>Class</p>
                        <p className='font-semibold text-gray-800'>{enquiry.classDetails?.name || 'Not specified'}</p>
                      </div>
                      <div className='col-span-2 p-3 bg-white rounded-lg border border-amber-100'>
                        <p className='text-xs font-medium text-amber-600 mb-1'>Subjects</p>
                        <p className='font-semibold text-gray-800'>{getSubjectDisplay(enquiry)}</p>
                      </div>
                    </div>
                  )}

                  {/* College specific fields */}
                  {enquiry.category === 'colleges' && (
                    <div className='grid grid-cols-2 gap-4'>
                      <div className='p-3 bg-white rounded-lg border border-amber-100'>
                        <p className='text-xs font-medium text-amber-600 mb-1'>Stream</p>
                        <p className='font-semibold text-gray-800'>{enquiry.streamDetails?.name || 'Not specified'}</p>
                      </div>
                      <div className='p-3 bg-white rounded-lg border border-amber-100'>
                        <p className='text-xs font-medium text-amber-600 mb-1'>Degree Level</p>
                        <p className='font-semibold text-gray-800'>{enquiry.degreeLevelDetails?.name || 'Not specified'}</p>
                      </div>
                      <div className='p-3 bg-white rounded-lg border border-amber-100'>
                        <p className='text-xs font-medium text-amber-600 mb-1'>Degree</p>
                        <p className='font-semibold text-gray-800'>{enquiry.degreeDetails?.name || 'Not specified'}</p>
                      </div>
                      <div className='p-3 bg-white rounded-lg border border-amber-100'>
                        <p className='text-xs font-medium text-amber-600 mb-1'>Branch</p>
                        <p className='font-semibold text-gray-800'>{enquiry.branchDetails?.name || 'Not specified'}</p>
                      </div>
                      <div className='col-span-2 p-3 bg-white rounded-lg border border-amber-100'>
                        <p className='text-xs font-medium text-amber-600 mb-1'>Subjects</p>
                        <p className='font-semibold text-gray-800'>{getSubjectDisplay(enquiry)}</p>
                      </div>
                    </div>
                  )}

                  {/* Hobby specific fields */}
                  {enquiry.category === 'hobbies' && (
                    <div className='grid grid-cols-2 gap-4'>
                      <div className='p-3 bg-white rounded-lg border border-amber-100'>
                        <p className='text-xs font-medium text-amber-600 mb-1'>Hobby Type</p>
                        <p className='font-semibold text-gray-800'>{enquiry.hobbyTypeDetails?.name || 'Not specified'}</p>
                      </div>
                      <div className='p-3 bg-white rounded-lg border border-amber-100'>
                        <p className='text-xs font-medium text-amber-600 mb-1'>Hobby</p>
                        <p className='font-semibold text-gray-800'>{enquiry.hobbyDetails?.name || 'Not specified'}</p>
                      </div>
                    </div>
                  )}

                  {/* Language specific fields */}
                  {enquiry.category === 'languages' && (
                    <div className='grid grid-cols-2 gap-4'>
                      <div className='p-3 bg-white rounded-lg border border-amber-100'>
                        <p className='text-xs font-medium text-amber-600 mb-1'>Language Type</p>
                        <p className='font-semibold text-gray-800'>{enquiry.languageTypeDetails?.name || 'Not specified'}</p>
                      </div>
                      <div className='p-3 bg-white rounded-lg border border-amber-100'>
                        <p className='text-xs font-medium text-amber-600 mb-1'>Language</p>
                        <p className='font-semibold text-gray-800'>{enquiry.languageDetails?.name || 'Not specified'}</p>
                      </div>
                    </div>
                  )}

                  {/* Course specific fields */}
                  {enquiry.category === 'it_courses' && (
                    <div className='grid grid-cols-2 gap-4'>
                      <div className='p-3 bg-white rounded-lg border border-amber-100'>
                        <p className='text-xs font-medium text-amber-600 mb-1'>Course Type</p>
                        <p className='font-semibold text-gray-800'>{enquiry.courseTypeDetails?.name || 'Not specified'}</p>
                      </div>
                      <div className='p-3 bg-white rounded-lg border border-amber-100'>
                        <p className='text-xs font-medium text-amber-600 mb-1'>Course</p>
                        <p className='font-semibold text-gray-800'>{enquiry.courseDetails?.name || 'Not specified'}</p>
                      </div>
                    </div>
                  )}

                  {/* Exam specific fields */}
                  {enquiry.category === 'exams' && (
                    <div className='grid grid-cols-2 gap-4'>
                      <div className='p-3 bg-white rounded-lg border border-amber-100'>
                        <p className='text-xs font-medium text-amber-600 mb-1'>Exam Category</p>
                        <p className='font-semibold text-gray-800'>{enquiry.examCategoryDetails?.name || 'Not specified'}</p>
                      </div>
                      <div className='p-3 bg-white rounded-lg border border-amber-100'>
                        <p className='text-xs font-medium text-amber-600 mb-1'>Exam</p>
                        <p className='font-semibold text-gray-800'>{enquiry.examDetails?.name || 'Not specified'}</p>
                      </div>
                      <div className='col-span-2 p-3 bg-white rounded-lg border border-amber-100'>
                        <p className='text-xs font-medium text-amber-600 mb-1'>Exam Subjects</p>
                        <p className='font-semibold text-gray-800'>{getSubjectDisplay(enquiry)}</p>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Special Requirements */}
              {enquiry.specialRequirements && (
                <div className='mb-6'>
                  <h3 className='text-base font-semibold text-gray-700 flex items-center gap-2 mb-3'>
                    <div className='p-1.5 rounded-lg bg-red-50'>
                      <FileText size={18} className='text-red-600' />
                    </div>
                    <span>Special Requirements</span>
                  </h3>
                  <div className='bg-gradient-to-br from-red-50/50 to-white border border-red-100 rounded-xl p-4 shadow-sm'>
                    <p className='text-gray-800 break-words whitespace-pre-wrap'>{enquiry.specialRequirements}</p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </PrimaryModalWithHeader>
  );
};

export default LeadViewModal;
