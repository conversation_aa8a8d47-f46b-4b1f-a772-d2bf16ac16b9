import { z } from 'zod';
import { objectIdSchema } from '../../common.schema';
import { currencyMap, ICurrencyMap } from '../tuition.maps';
import { serviceCategoryMap, IServiceCategoryMap } from '../../education/index.maps';

const commonFieldsSchema = z.object({
  serviceCategory: z
    .enum(Object.keys(serviceCategoryMap) as [IServiceCategoryMap, ...IServiceCategoryMap[]])
    .describe('Category of service being offered'),
  amount: z.number().min(0, 'Amount must be non-negative').describe('Amount charged for the service'),
  budget: z.number().min(0, 'Budget must be non-negative').describe('Expected budget for the service'),
  currency: z
    .enum(Object.keys(currencyMap) as [ICurrencyMap, ...ICurrencyMap[]])
    .default('inr')
    .describe('Currency for the amount and budget'),
});

const schoolSubjectSchema = z.object({
  serviceCategory: z.literal('schools').describe('School service category'),
  boardId: objectIdSchema.describe('School board ID'),
  classId: objectIdSchema.describe('Class/grade ID'),
  subjectIds: z.array(objectIdSchema).min(1, 'At least one subject must be selected').describe('Selected subjects'),
  allSubjects: z.boolean().optional().describe('Whether all subjects are selected'),
});

const collegeSubjectSchema = z.object({
  serviceCategory: z.literal('colleges').describe('College service category'),
  streamId: objectIdSchema.describe('Stream ID'),
  degreeLevelId: objectIdSchema.describe('Degree level ID'),
  degreeId: objectIdSchema.describe('Degree ID'),
  branchId: z.union([objectIdSchema, z.string()]).describe('Branch/specialization ID'),
  collegeSubjectIds: z.array(objectIdSchema).min(1, 'At least one subject must be selected').describe('Selected subjects'),
  allSubjects: z.boolean().optional().describe('Whether all subjects are selected'),
});

const languageSubjectSchema = z.object({
  serviceCategory: z.literal('languages').describe('Language service category'),
  languageTypeId: objectIdSchema.describe('Language type ID'),
  languageId: objectIdSchema.describe('Language ID'),
});

const hobbySubjectSchema = z.object({
  serviceCategory: z.literal('hobbies').describe('Hobby service category'),
  hobbyTypeId: objectIdSchema.describe('Hobby type ID'),
  hobbyId: objectIdSchema.describe('Hobby ID'),
});

const examSubjectSchema = z.object({
  serviceCategory: z.literal('exams').describe('Exam service category'),
  examCategoryId: objectIdSchema.describe('Exam category ID'),
  examId: objectIdSchema.describe('Exam ID'),
  examSubjectIds: z.array(objectIdSchema).min(1, 'At least one subject must be selected').describe('Selected subjects'),
  allSubjects: z.boolean().optional().describe('Whether all subjects are selected'),
});

const itCourseSubjectSchema = z.object({
  serviceCategory: z.literal('it_courses').describe('IT course service category'),
  courseTypeId: objectIdSchema.describe('Course type ID'),
  courseId: objectIdSchema.describe('Course ID'),
});

export const createTeachingSubjectSchema = z
  .object(commonFieldsSchema.shape)
  .and(
    z.discriminatedUnion('serviceCategory', [
      schoolSubjectSchema,
      collegeSubjectSchema,
      languageSubjectSchema,
      hobbySubjectSchema,
      examSubjectSchema,
      itCourseSubjectSchema,
    ])
  )
  .superRefine((data: { amount: number; budget: number }, ctx) => {
    if (data.amount < data.budget) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Amount cannot be less than budget',
        path: ['amount'],
      });
    }
  });

export const updateTeachingSubjectSchema = z
  .object({
    serviceCategory: z.enum(Object.keys(serviceCategoryMap) as [IServiceCategoryMap, ...IServiceCategoryMap[]]).optional(),
    amount: z.number().min(0, 'Amount must be non-negative').optional(),
    budget: z.number().min(0, 'Budget must be non-negative').optional(),
    currency: z.enum(Object.keys(currencyMap) as [ICurrencyMap, ...ICurrencyMap[]]).optional(),

    // School fields
    boardId: objectIdSchema.optional(),
    classId: objectIdSchema.optional(),
    subjectIds: z.array(objectIdSchema).optional(),
    allSubjects: z.boolean().optional(),

    // College fields
    streamId: objectIdSchema.optional(),
    degreeLevelId: objectIdSchema.optional(),
    degreeId: objectIdSchema.optional(),
    branchId: z.union([objectIdSchema, z.string()]).optional(),
    collegeSubjectIds: z.array(objectIdSchema).optional(),

    // Language fields
    languageTypeId: objectIdSchema.optional(),
    languageId: objectIdSchema.optional(),

    // Hobby fields
    hobbyTypeId: objectIdSchema.optional(),
    hobbyId: objectIdSchema.optional(),

    // Exam fields
    examCategoryId: objectIdSchema.optional(),
    examId: objectIdSchema.optional(),
    examSubjectIds: z.array(objectIdSchema).optional(),
    allSubjects: z.boolean().optional(),

    // IT Course fields
    courseTypeId: objectIdSchema.optional(),
    courseId: objectIdSchema.optional(),
  })
  .superRefine((data, ctx) => {
    if (data.amount !== undefined && data.budget !== undefined && data.amount < data.budget) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Amount cannot be less than budget',
        path: ['amount'],
      });
    }
  });

export type CreateTeachingSubjectInput = z.infer<typeof createTeachingSubjectSchema>;
export type UpdateTeachingSubjectInput = z.infer<typeof updateTeachingSubjectSchema>;
