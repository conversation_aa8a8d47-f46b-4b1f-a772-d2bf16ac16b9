import { format, differenceInYears, parseISO, FormatOptions } from 'date-fns';

export const calculateAge = (dateOfBirth?: Date | string): number => {
  if (!dateOfBirth) return 0;

  if (typeof dateOfBirth === 'string') {
    dateOfBirth = new Date(dateOfBirth);
  }

  if (isNaN(dateOfBirth.getTime())) return 0;
  if (dateOfBirth.toString() === 'Invalid Date') return 0;

  return differenceInYears(new Date(), dateOfBirth);
};

export const formatDateOfBirth = (date?: Date | string): string => {
  if (!date) return '';

  if (typeof date === 'string') {
    date = new Date(date);
  }

  if (isNaN(date.getTime())) return '';
  if (date.toString() === 'Invalid Date') return '';

  return format(date, 'PPP');
};

export const formatDate = (date: Date | string | undefined, formatStr: 'PPP' | 'MMM yyyy' = 'MMM yyyy') => {
  if (!date) return 'N/A';
  try {
    const parsedDate = typeof date === 'string' ? parseISO(date) : date;
    return format(parsedDate, formatStr);
  } catch (error) {
    console.error('Error formatting date:', error);
    return 'Invalid Date';
  }
};

export const formatExperienceByMonth = (months: number) => {
  if (months === 0 || !months) return 'N/A';
  const years = Math.floor(months / 12);
  const remainingMonths = months % 12;
  if (years > 0) {
    return remainingMonths > 0 ? `${years}y ${remainingMonths}m` : `${years} year${years > 1 ? 's' : ''}`;
  }
  return `${months} month${months > 1 ? 's' : ''}`;
};
