'use client';

import { useState, useEffect, useCallback } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { AdminDashActionLinks, HeadingBar, LimitFilter, MasterFilter, MasterTable, SectionWrapper } from '@/components/dashboard/ascrm/misc';
import { ErrorState } from '@/components/dashboard/ascrm/misc/StateUI';
import { filterTypeMaps, FilterTypeKey } from '@/constants';
import { FilterValues, FilterOption } from '@/types/api';
import { useGetAllBranches, useDeleteBranch, useGetBranchById, useGetDegreeById } from '@/hooks/education/college.hooks';
import { AdminViewButton, PrimaryFormModal, useModal, StatusToggle } from '@/components/dashboard/shared/misc';
import BranchForm from './branch-form';
import { branchService } from '@/server/services/education/college.service';
import { QUERY_KEYS } from '@/lib/react-query/queryKeys';

const BranchTable: React.FC = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const addBranchModal = useModal();
  const editBranchModal = useModal();

  const [selectedBranchId, setSelectedBranchId] = useState<string | null>(null);
  const [filters, setFilters] = useState<FilterValues>({});
  const [degreeName, setDegreeName] = useState<string>('');
  const [streamName, setStreamName] = useState<string>('');
  const [degreeLevelName, setDegreeLevelName] = useState<string>('');

  const degreeId = searchParams.get('degree');

  if (!degreeId) return <ErrorState message='You are not authorized to view this page.' />;

  const { data: degreeData, error: degreeError } = useGetDegreeById(degreeId || '');

  useEffect(() => {
    if (degreeData?.data?.degree) {
      const degree = degreeData.data.degree;
      setDegreeName(degree.name);

      if (degree.degreeLevelDetails) {
        setDegreeLevelName(degree.degreeLevelDetails.name);
      }

      if (degree.streamDetails) {
        setStreamName(degree.streamDetails.name);
      }
    }
  }, [degreeData]);

  const updateFiltersFromParams = useCallback(() => {
    const initialFilters: FilterValues = {};
    searchParams.forEach((value, key) => {
      if (key !== 'page' && key !== 'degree') {
        initialFilters[key] = value;
      }
    });
    setFilters(initialFilters);
  }, [searchParams]);

  useEffect(() => {
    updateFiltersFromParams();
  }, [updateFiltersFromParams]);

  const page = Number(searchParams.get('page')) || 1;

  const queryParams = { page, degree: degreeId, ...filters };

  const { data: branchesResponse, isLoading, error } = useGetAllBranches(queryParams);
  const deleteItem = useDeleteBranch();
  const { data: selectedBranchData, isLoading: isLoadingSelectedBranch, error: selectedBranchError } = useGetBranchById(selectedBranchId || '');

  const branches = branchesResponse?.data?.branches || [];
  const pagination = branchesResponse?.data?.pagination;

  const handleEditBranch = (id: string) => {
    setSelectedBranchId(id);
    editBranchModal.open();
  };

  if (error || degreeError) {
    return <ErrorState message={error?.message || degreeError?.message || 'An unexpected error occurred.'} />;
  }

  const headers = ['Name', 'Status', 'Actions'];

  const rows = branches.map((branch) => {
    return {
      id: branch._id,
      values: [
        <p className='font-medium'>{branch.name}</p>,
        <StatusToggle
          id={branch._id}
          isActive={branch.isActive}
          updateFn={branchService.updateBranch}
          queryKey={[QUERY_KEYS.EDUCATION.BRANCHES, [QUERY_KEYS.EDUCATION.BRANCHES, { page, degree: degreeId, ...filters }]]}
          entityName='Branch'
        />,
        <AdminDashActionLinks
          delete
          editAction={() => handleEditBranch(branch._id)}
          basePath='/branches'
          id={branch._id}
          itemName={branch.name}
          deleteItem={() => deleteItem.mutateAsync(branch._id)}
          manualAction={[<AdminViewButton variant='icon' href={`/college-subjects?branch=${branch._id}`} label={`View ${branch.name} Subjects`} />]}
        />,
      ],
    };
  });

  const filterOptions: FilterOption[] = [
    {
      type: filterTypeMaps.input.key as FilterTypeKey,
      label: 'Branch Name',
      key: 'name',
    },
  ];

  const handleApplyFilters = (newFilters: FilterValues) => {
    const params = new URLSearchParams(searchParams);
    Object.entries(newFilters).forEach(([key, value]) => {
      if (value && typeof value === 'string') {
        params.set(key, value);
      } else {
        params.delete(key);
      }
    });
    params.set('page', '1');
    router.push(`?${params.toString()}`);
  };

  const handleResetFilters = () => {
    const params = new URLSearchParams();
    params.set('degree', degreeId);
    params.set('page', '1');
    router.push(`?${params.toString()}`);
  };

  const title = degreeName
    ? streamName && degreeLevelName
      ? `Branches of ${degreeName} (${degreeLevelName}, ${streamName})`
      : `Branches of ${degreeName}`
    : 'College Branches';

  return (
    <SectionWrapper>
      <HeadingBar
        title={title}
        elements={[
          <LimitFilter
            key='limit'
            value={(filters.limit as string) || '10'}
            onChange={(value: string) => {
              handleApplyFilters({ limit: value });
            }}
          />,
          <MasterFilter
            key='filter'
            filterOptions={filterOptions}
            onApplyFilters={handleApplyFilters}
            onResetFilters={handleResetFilters}
            initialFilters={filters}
          />,
        ]}
        addAction={addBranchModal.open}
      />
      <MasterTable headers={headers} rows={rows} isLoading={isLoading} pagination={pagination} />

      {/* Add Branch Modal */}
      <PrimaryFormModal
        isOpen={addBranchModal.isOpen}
        onClose={addBranchModal.close}
        title={degreeName ? `Add New Branch for ${degreeName}` : 'Add New College Branch'}
        subtitle='Create a new college branch for your platform'
        size='sm'
        showSparkles={true}
      >
        <BranchForm onSuccess={addBranchModal.close} degreeId={degreeId} />
      </PrimaryFormModal>

      {/* Edit Branch Modal */}
      <PrimaryFormModal
        isOpen={editBranchModal.isOpen}
        onClose={() => {
          editBranchModal.close();
          setSelectedBranchId(null);
        }}
        title={degreeName ? `Edit ${degreeName} Branch` : 'Edit College Branch'}
        subtitle='Update existing college branch details'
        size='sm'
        isLoading={isLoadingSelectedBranch}
        loadingTitle='Loading Branch Data'
        loadingMessage='Please wait while we fetch the branch details...'
        isError={!!selectedBranchError}
        errorMessage={selectedBranchError?.message}
      >
        {selectedBranchData?.data?.branch && (
          <BranchForm data={selectedBranchData.data.branch} onSuccess={editBranchModal.close} degreeId={degreeId} />
        )}
      </PrimaryFormModal>
    </SectionWrapper>
  );
};

export default BranchTable;
