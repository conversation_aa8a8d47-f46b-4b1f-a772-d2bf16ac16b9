'use client';

import React from 'react';
import { FormField, FormItem, FormControl, FormLabel, FormMessage, FormDescription } from '@/components/ui/form';
import { Switch } from '@/components/ui/switch';
import type { UseFormReturn, FieldValues, Path } from 'react-hook-form';
import { cn } from '@/lib/utils';

interface PrimarySwitchProps<TFieldValues extends FieldValues> {
  form: UseFormReturn<TFieldValues>;
  name: Path<TFieldValues>;
  label?: string;
  description?: string;
  required?: boolean;
  disabled?: boolean;
  className?: string;
  onCheckedChange?: (checked: boolean) => void;
  containerClassName?: string;
  labelClassName?: string;
}

const PrimarySwitchInput = <TFieldValues extends FieldValues>({
  form,
  name,
  label,
  description,
  required = false,
  disabled = false,
  className = '',
  onCheckedChange,
  containerClassName = '',
  labelClassName = '',
}: PrimarySwitchProps<TFieldValues>) => {
  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => (
        <FormItem className={`flex flex-row items-start space-x-3 space-y-0 ${containerClassName}`}>
          <FormControl>
            <Switch
              checked={field.value}
              onCheckedChange={(checked) => {
                field.onChange(checked);
                if (onCheckedChange) {
                  onCheckedChange(checked);
                }
              }}
              disabled={disabled}
              className={cn(' data-[state=checked]:bg-primaryColor-500', className)}
            />
          </FormControl>
          <div className='space-y-1 leading-none'>
            {label && (
              <FormLabel className={`primary-label ${labelClassName}`}>
                <span>{label}</span>
                {required && <span className='text-primaryColor'>*</span>}
              </FormLabel>
            )}
            {description && <FormDescription className='text-xs text-gray-500 leading-snug'>{description}</FormDescription>}
            <FormMessage />
          </div>
        </FormItem>
      )}
    />
  );
};

export default PrimarySwitchInput;
