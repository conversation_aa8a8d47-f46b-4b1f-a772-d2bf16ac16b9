import { useQuery, useMutation } from '@tanstack/react-query';
import { QUERY_KEYS } from '@/lib/react-query/queryKeys';
import { queryClient } from '@/lib/react-query/queryClient';
import tuitionProfileService from '@/server/services/tutor/tuition-profile.service';
import { CreateTuitionInfoInput, UpdateTuitionInfoInput } from '@/validation/schemas/tutor/profiles/tuition-info.schema';
import { CreateTeachingExperienceInput, UpdateTeachingExperienceInput } from '@/validation/schemas/tutor/profiles/teaching-experience.schema';
import { CreateTeachingSubjectInput, UpdateTeachingSubjectInput } from '@/validation/schemas/tutor/profiles/teaching-subjects.schema';

// Tuition Info Hooks
export function useGetTuitionInfo(options = {}) {
  return useQuery({
    queryKey: [QUERY_KEYS.TUTOR.TUITION_INFO],
    queryFn: () => tuitionProfileService.tuitionInfo.getTuitionInfo(),
    ...options,
  });
}

export function useCreateTuitionInfo() {
  return useMutation({
    mutationFn: (data: CreateTuitionInfoInput) => tuitionProfileService.tuitionInfo.createTuitionInfo(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.TUTOR.TUITION_INFO] });
    },
  });
}

export function useUpdateTuitionInfo() {
  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateTuitionInfoInput }) => tuitionProfileService.tuitionInfo.updateTuitionInfo(id, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.TUTOR.TUITION_INFO] });
    },
  });
}

export function useDeleteTuitionInfo() {
  return useMutation({
    mutationFn: (id: string) => tuitionProfileService.tuitionInfo.deleteTuitionInfo(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.TUTOR.TUITION_INFO] });
    },
  });
}

// Teaching Experience Hooks
export function useGetAllTeachingExperience(options = {}) {
  return useQuery({
    queryKey: [QUERY_KEYS.TUTOR.TEACHING_EXPERIENCE],
    queryFn: () => tuitionProfileService.teachingExperience.getAllTeachingExperience(),
    ...options,
  });
}

export function useCreateTeachingExperience() {
  return useMutation({
    mutationFn: (data: CreateTeachingExperienceInput) => tuitionProfileService.teachingExperience.createTeachingExperience(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.TUTOR.TEACHING_EXPERIENCE] });
    },
  });
}

export function useUpdateTeachingExperience() {
  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateTeachingExperienceInput }) =>
      tuitionProfileService.teachingExperience.updateTeachingExperience(id, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.TUTOR.TEACHING_EXPERIENCE] });
    },
  });
}

export function useDeleteTeachingExperience() {
  return useMutation({
    mutationFn: (id: string) => tuitionProfileService.teachingExperience.deleteTeachingExperience(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.TUTOR.TEACHING_EXPERIENCE] });
    },
  });
}

// Teaching Subjects Hooks
export function useGetAllTeachingSubjects(options = {}) {
  return useQuery({
    queryKey: [QUERY_KEYS.TUTOR.TEACHING_SUBJECTS],
    queryFn: () => tuitionProfileService.teachingSubjects.getAllTeachingSubjects(),
    ...options,
  });
}

export function useCreateTeachingSubject() {
  return useMutation({
    mutationFn: (data: CreateTeachingSubjectInput) => tuitionProfileService.teachingSubjects.createTeachingSubject(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.TUTOR.TEACHING_SUBJECTS] });
    },
  });
}

export function useUpdateTeachingSubject() {
  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateTeachingSubjectInput }) =>
      tuitionProfileService.teachingSubjects.updateTeachingSubject(id, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.TUTOR.TEACHING_SUBJECTS] });
    },
  });
}

export function useDeleteTeachingSubject() {
  return useMutation({
    mutationFn: (id: string) => tuitionProfileService.teachingSubjects.deleteTeachingSubject(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.TUTOR.TEACHING_SUBJECTS] });
    },
  });
}
