'use client';

import { useState } from 'react';
import { Backgrounds, VisitorsLoader } from '@/components/dashboard/shared/misc';
import LeadStatusTabs from './lead-status-tabs';
import LeadList from './lead-list';
import { useGetParentEnquiries } from '@/hooks/enquiry.hooks';

const ParentLeadsClient = () => {
  const [selectedTab, setSelectedTab] = useState<'active' | 'converted' | 'closed'>('active');

  const { data: activeEnquiriesData, isLoading: isActiveEnquiriesLoading } = useGetParentEnquiries({ isActive: true });
  const { data: convertedEnquiriesData, isLoading: isConvertedEnquiriesLoading } = useGetParentEnquiries({ isConverted: true });
  const { data: closedEnquiriesData, isLoading: isClosedEnquiriesLoading } = useGetParentEnquiries({ isActive: false }); // correct one

  const activeEnquiries = activeEnquiriesData?.data?.enquiries || [];
  const convertedEnquiries = convertedEnquiriesData?.data?.enquiries || [];
  const closedEnquiries = closedEnquiriesData?.data?.enquiries || [];

  const getCurrentEnquiries = () => {
    switch (selectedTab) {
      case 'active':
        return activeEnquiries;
      case 'converted':
        return convertedEnquiries;
      case 'closed':
        return closedEnquiries;
      default:
        return [];
    }
  };

  const tabCounts = {
    active: activeEnquiries.length,
    converted: convertedEnquiries.length,
    closed: closedEnquiries.length,
  };

  const isCurrentTabLoading = () => {
    switch (selectedTab) {
      case 'active':
        return isActiveEnquiriesLoading;
      case 'converted':
        return isConvertedEnquiriesLoading;
      case 'closed':
        return isClosedEnquiriesLoading;
      default:
        return false;
    }
  };

  if (isCurrentTabLoading()) {
    return (
      <div className='bg-gradient-to-b from-white to-gray-50 p-8 rounded-3xl relative min-h-[650px] shadow-sm border border-gray-100 flex items-center justify-center'>
        <VisitorsLoader loaderType='skeleton' title='Tuition Enquiries' message='Loading your enquiries...' />
      </div>
    );
  }

  return (
    <section className='bg-gradient-to-b from-white to-gray-50 p-8 rounded-3xl relative min-h-[650px] shadow-sm border border-gray-100'>
      <Backgrounds variant='primary' direction='br2tl' />
      <div className='relative z-10 flex flex-col lg:flex-row gap-8'>
        <div className='lg:w-1/4 w-full'>
          <LeadStatusTabs selectedTab={selectedTab} setSelectedTab={setSelectedTab} tabCounts={tabCounts} />
        </div>
        <LeadList enquiries={getCurrentEnquiries()} selectedTab={selectedTab} />
      </div>
    </section>
  );
};

export default ParentLeadsClient;
