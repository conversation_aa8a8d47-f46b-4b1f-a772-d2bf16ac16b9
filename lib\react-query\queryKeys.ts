export const QUERY_KEYS = {
  AUTH: {
    SESSION: 'session',
  },
  USER: {
    PROFILE: 'user-profile',
    GENERAL_INFO: 'user-general-info',
    ADDRESSES: 'user-addresses',
    ADDRESS: 'user-address',
    BUSINESS_LOCATIONS: 'user-business-locations',
  },
  STAFF: {
    PROFILE: 'staff-profile',
    LIST: 'staff-list',
    DETAIL: 'staff-detail',
  },
  PROFILE: {
    CHILD_PROFILES: 'child-profiles',
    CHILD_PROFILE: 'child-profile',
    EDUCATION_DETAILS: 'education-details',
    EDUCATION_DETAIL: 'education-detail',
  },
  EDUCATION: {
    SERVICE_CATEGORIES: 'education-service-categories',
    SERVICE_CATEGORY: 'education-service-category',
    // School
    BOARDS: 'education-boards',
    BOARD: 'education-board',
    CLASSES: 'education-classes',
    CLASSES_BY_BOARD: 'education-classes-by-board',
    CLASS: 'education-class',
    SUBJECTS: 'education-subjects',
    SUBJECTS_BY_CLASS: 'education-subjects-by-class',
    SUBJECT: 'education-subject',
    // College
    STREAMS: 'education-streams',
    STREAM: 'education-stream',
    DEGREE_LEVELS: 'education-degree-levels',
    DEGREE_LEVEL: 'education-degree-level',
    DEGREES: 'education-degrees',
    DEGREE: 'education-degree',
    BRANCHES: 'education-branches',
    BRANCHES_BY_DEGREE: 'education-branches-by-degree',
    BRANCH: 'education-branch',
    COLLEGE_SUBJECTS: 'education-college-subjects',
    COLLEGE_SUBJECTS_BY_BRANCH: 'education-college-subjects-by-branch',
    COLLEGE_SUBJECT: 'education-college-subject',
    // Hobby
    HOBBY_TYPES: 'education-hobby-types',
    HOBBY_TYPE: 'education-hobby-type',
    HOBBIES: 'education-hobbies',
    HOBBY: 'education-hobby',
    // Language
    LANGUAGE_TYPES: 'education-language-types',
    LANGUAGE_TYPE: 'education-language-type',
    LANGUAGES: 'education-languages',
    LANGUAGE: 'education-language',
    // IT Course
    COURSE_TYPES: 'education-course-types',
    COURSE_TYPE: 'education-course-type',
    COURSES: 'education-courses',
    COURSE: 'education-course',
    // Exam
    EXAM_CATEGORIES: 'education-exam-categories',
    EXAM_CATEGORY: 'education-exam-category',
    EXAMS: 'education-exams',
    EXAM: 'education-exam',
    EXAM_SUBJECTS: 'education-exam-subjects',
    EXAM_SUBJECT: 'education-exam-subject',
  },
  ENQUIRY: {
    SEARCH: 'enquiry-search',
    CATEGORY_ITEMS: 'enquiry-category-items',
    PARENT_ENQUIRIES: 'parent-enquiries',
    PARENT_ENQUIRY: 'parent-enquiry',
  },
  TUTOR: {
    TUITION_INFO: 'tutor-tuition-info',
    TEACHING_EXPERIENCE: 'tutor-teaching-experience',
    TEACHING_SUBJECTS: 'tutor-teaching-subjects',
  },
} as const;

export type QueryKey = keyof typeof QUERY_KEYS;
