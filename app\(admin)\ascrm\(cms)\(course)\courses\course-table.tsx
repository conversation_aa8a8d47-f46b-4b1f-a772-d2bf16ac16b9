'use client';

import { useState, useEffect, useCallback } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { AdminDashActionLinks, HeadingBar, LimitFilter, MasterFilter, MasterTable, SectionWrapper } from '@/components/dashboard/ascrm/misc';
import { ErrorState } from '@/components/dashboard/ascrm/misc/StateUI';
import { filterTypeMaps, FilterTypeKey } from '@/constants';
import { FilterValues, FilterOption } from '@/types/api';
import { useGetAllCourses, useDeleteCourse, useGetCourseById, useGetCourseTypeById } from '@/hooks/education/course.hooks';
import { PrimaryFormModal, useModal, StatusToggle } from '@/components/dashboard/shared/misc';
import CourseForm from './course-form';
import { courseService } from '@/server/services/education/course.service';
import { QUERY_KEYS } from '@/lib/react-query/queryKeys';

const CourseTable: React.FC = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const addCourseModal = useModal();
  const editCourseModal = useModal();

  const [selectedCourseId, setSelectedCourseId] = useState<string | null>(null);
  const [filters, setFilters] = useState<FilterValues>({});
  const [courseTypeName, setCourseTypeName] = useState<string>('');

  const courseTypeId = searchParams.get('courseType');

  if (!courseTypeId) return <ErrorState message='You are not authorized to view this page.' />;

  const { data: courseTypeData, error: courseTypeError } = useGetCourseTypeById(courseTypeId || '');

  useEffect(() => {
    if (courseTypeData?.data?.courseType) {
      setCourseTypeName(courseTypeData.data.courseType.name);
    }
  }, [courseTypeData]);

  const updateFiltersFromParams = useCallback(() => {
    const initialFilters: FilterValues = {};
    searchParams.forEach((value, key) => {
      if (key !== 'page' && key !== 'courseType') {
        initialFilters[key] = value;
      }
    });
    setFilters(initialFilters);
  }, [searchParams]);

  useEffect(() => {
    updateFiltersFromParams();
  }, [updateFiltersFromParams]);

  const page = Number(searchParams.get('page')) || 1;

  const queryParams = { page, courseType: courseTypeId, ...filters };

  const { data: coursesResponse, isLoading, error } = useGetAllCourses(queryParams);
  const deleteItem = useDeleteCourse();
  const { data: selectedCourseData, isLoading: isLoadingSelectedCourse, error: selectedCourseError } = useGetCourseById(selectedCourseId || '');

  const courses = coursesResponse?.data?.courses || [];
  const pagination = coursesResponse?.data?.pagination;

  const handleEditCourse = (id: string) => {
    setSelectedCourseId(id);
    editCourseModal.open();
  };

  if (error || courseTypeError) {
    return <ErrorState message={error?.message || courseTypeError?.message || 'An unexpected error occurred.'} />;
  }

  const headers = ['Name', 'Status', 'Actions'];

  const rows = courses.map((course) => {
    return {
      id: course._id,
      values: [
        <p className='font-medium'>{course.name}</p>,
        <StatusToggle
          id={course._id}
          isActive={course.isActive}
          updateFn={courseService.updateCourse}
          queryKey={[QUERY_KEYS.EDUCATION.COURSES, [QUERY_KEYS.EDUCATION.COURSES, { page, courseType: courseTypeId, ...filters }]]}
          entityName='Course'
        />,
        <AdminDashActionLinks
          delete
          editAction={() => handleEditCourse(course._id)}
          basePath='/courses'
          id={course._id}
          itemName={course.name}
          deleteItem={() => deleteItem.mutateAsync(course._id)}
        />,
      ],
    };
  });

  const filterOptions: FilterOption[] = [
    {
      type: filterTypeMaps.input.key as FilterTypeKey,
      label: 'Course Name',
      key: 'name',
    },
  ];

  const handleApplyFilters = (newFilters: FilterValues) => {
    const params = new URLSearchParams(searchParams);
    Object.entries(newFilters).forEach(([key, value]) => {
      if (value && typeof value === 'string') {
        params.set(key, value);
      } else {
        params.delete(key);
      }
    });
    params.set('page', '1');
    router.push(`?${params.toString()}`);
  };

  const handleResetFilters = () => {
    const params = new URLSearchParams();
    params.set('courseType', courseTypeId);
    params.set('page', '1');
    router.push(`?${params.toString()}`);
  };

  return (
    <SectionWrapper>
      <HeadingBar
        title={courseTypeName ? `IT Courses of ${courseTypeName}` : 'IT Courses'}
        elements={[
          <LimitFilter
            key='limit'
            value={(filters.limit as string) || '10'}
            onChange={(value: string) => {
              handleApplyFilters({ limit: value });
            }}
          />,
          <MasterFilter
            key='filter'
            filterOptions={filterOptions}
            onApplyFilters={handleApplyFilters}
            onResetFilters={handleResetFilters}
            initialFilters={filters}
          />,
        ]}
        addAction={addCourseModal.open}
      />
      <MasterTable headers={headers} rows={rows} isLoading={isLoading} pagination={pagination} />

      {/* Add Course Modal */}
      <PrimaryFormModal
        isOpen={addCourseModal.isOpen}
        onClose={addCourseModal.close}
        title={courseTypeName ? `Add New ${courseTypeName} Course` : 'Add New IT Course'}
        subtitle='Create a new IT course for your platform'
        size='sm'
        showSparkles={true}
      >
        <CourseForm onSuccess={addCourseModal.close} courseTypeId={courseTypeId} />
      </PrimaryFormModal>

      {/* Edit Course Modal */}
      <PrimaryFormModal
        isOpen={editCourseModal.isOpen}
        onClose={() => {
          editCourseModal.close();
          setSelectedCourseId(null);
        }}
        title={courseTypeName ? `Edit ${courseTypeName} Course` : 'Edit IT Course'}
        subtitle='Update existing IT course details'
        size='sm'
        isLoading={isLoadingSelectedCourse}
        loadingTitle='Loading Course Data'
        loadingMessage='Please wait while we fetch the course details...'
        isError={!!selectedCourseError}
        errorMessage={selectedCourseError?.message}
      >
        {selectedCourseData?.data?.course && (
          <CourseForm data={selectedCourseData.data.course} onSuccess={editCourseModal.close} courseTypeId={courseTypeId} />
        )}
      </PrimaryFormModal>
    </SectionWrapper>
  );
};

export default CourseTable;
