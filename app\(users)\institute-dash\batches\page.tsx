import { InstiuteDashLink, SimpleTable } from '@/components/dashboard/institute-dash/misc';
import { Eye } from 'lucide-react';

const TutorBatchesPage = () => {
  return (
    <section className='bg-white p-6 rounded-3xl relative lg:min-h-[650px]'>
      <h2 className='text-base font-semibold m-4'>List of Batches</h2>
      <SimpleTable
        headers={['Batch ID', 'Batch Slot Date', 'Parent/Student Name', 'Location', 'Mode', 'Status', 'Action']}
        rows={[
          [
            'B001',
            '2024-07-22',
            '<PERSON>',
            'New York, USA',
            'Online',
            'Active',
            <InstiuteDashLink
              href='/batches/B001'
              className='bg-blue-100 text-blue-700 hover:bg-blue-200 rounded size-7 flex items-center justify-center cursor-pointer'
            >
              <Eye size={16} strokeWidth={1.5} />
            </InstiuteDashLink>,
          ],
          [
            'B002',
            '2024-07-23',
            '<PERSON>',
            'London, UK',
            'Offline',
            'Completed',
            <InstiuteDashLink
              href='/batches/B001'
              className='bg-blue-100 text-blue-700 hover:bg-blue-200 rounded size-7 flex items-center justify-center cursor-pointer'
            >
              <Eye size={16} strokeWidth={1.5} />
            </InstiuteDashLink>,
          ],
          [
            'B003',
            '2024-07-24',
            'Mike Johnson',
            'Sydney, Australia',
            'Online',
            'Pending',
            <InstiuteDashLink
              href='/batches/B001'
              className='bg-blue-100 text-blue-700 hover:bg-blue-200 rounded size-7 flex items-center justify-center cursor-pointer'
            >
              <Eye size={16} strokeWidth={1.5} />
            </InstiuteDashLink>,
          ],
          [
            'B004',
            '2024-07-25',
            'Emily Davis',
            'Toronto, Canada',
            'Offline',
            'Active',
            <InstiuteDashLink
              href='/batches/B001'
              className='bg-blue-100 text-blue-700 hover:bg-blue-200 rounded size-7 flex items-center justify-center cursor-pointer'
            >
              <Eye size={16} strokeWidth={1.5} />
            </InstiuteDashLink>,
          ],
        ]}
      />
    </section>
  );
};

export default TutorBatchesPage;
