'use client';

import { useState, useEffect, useCallback } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { AdminDashActionLinks, HeadingBar, LimitFilter, MasterFilter, MasterTable, SectionWrapper } from '@/components/dashboard/ascrm/misc';
import { ErrorState } from '@/components/dashboard/ascrm/misc/StateUI';
import { filterTypeMaps, FilterTypeKey } from '@/constants';
import { FilterValues, FilterOption } from '@/types/api';
import { useGetAllExamCategories, useDeleteExamCategory, useGetExamCategoryById } from '@/hooks/education/exam.hooks';
import { AdminViewButton, PrimaryFormModal, useModal, StatusToggle } from '@/components/dashboard/shared/misc';
import ExamCategoryForm from './exam-category-form';
import { examCategoryService } from '@/server/services/education/exam.service';
import { QUERY_KEYS } from '@/lib/react-query/queryKeys';

const ExamCategoryTable: React.FC = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const addExamCategoryModal = useModal();
  const editExamCategoryModal = useModal();

  const [selectedExamCategoryId, setSelectedExamCategoryId] = useState<string | null>(null);
  const [filters, setFilters] = useState<FilterValues>({});

  const updateFiltersFromParams = useCallback(() => {
    const initialFilters: FilterValues = {};
    searchParams.forEach((value, key) => {
      if (key !== 'page') {
        initialFilters[key] = value;
      }
    });
    setFilters(initialFilters);
  }, [searchParams]);

  useEffect(() => {
    updateFiltersFromParams();
  }, [updateFiltersFromParams]);

  const page = Number(searchParams.get('page')) || 1;

  const queryParams = { page, ...filters };

  const { data: examCategoriesResponse, isLoading, error } = useGetAllExamCategories(queryParams);
  const deleteItem = useDeleteExamCategory();
  const {
    data: selectedExamCategoryData,
    isLoading: isLoadingSelectedExamCategory,
    error: selectedExamCategoryError,
  } = useGetExamCategoryById(selectedExamCategoryId || '');

  const examCategories = examCategoriesResponse?.data?.examCategories || [];
  const pagination = examCategoriesResponse?.data?.pagination;

  const handleEditExamCategory = (id: string) => {
    setSelectedExamCategoryId(id);
    editExamCategoryModal.open();
  };

  if (error) {
    return <ErrorState message={error?.message || 'An unexpected error occurred.'} />;
  }

  const headers = ['Name', 'Status', 'Actions'];

  const rows = examCategories.map((examCategory) => {
    return {
      id: examCategory._id,
      values: [
        <p className='font-medium'>{examCategory.name}</p>,
        <StatusToggle
          id={examCategory._id}
          isActive={examCategory.isActive}
          updateFn={examCategoryService.updateExamCategory}
          queryKey={[QUERY_KEYS.EDUCATION.EXAM_CATEGORIES, [QUERY_KEYS.EDUCATION.EXAM_CATEGORIES, filters]]}
          entityName='Exam Category'
        />,
        <AdminDashActionLinks
          delete
          editAction={() => handleEditExamCategory(examCategory._id)}
          basePath='/exam-categories'
          id={examCategory._id}
          itemName={examCategory.name}
          deleteItem={() => deleteItem.mutateAsync(examCategory._id)}
          manualAction={[
            <AdminViewButton variant='icon' href={`/exams?examCategory=${examCategory._id}`} label={`View ${examCategory.name} Exams`} />,
          ]}
        />,
      ],
    };
  });

  const filterOptions: FilterOption[] = [
    {
      type: filterTypeMaps.input.key as FilterTypeKey,
      label: 'Exam Category Name',
      key: 'name',
    },
  ];

  const handleApplyFilters = (newFilters: FilterValues) => {
    const params = new URLSearchParams(searchParams);
    Object.entries(newFilters).forEach(([key, value]) => {
      if (value && typeof value === 'string') {
        params.set(key, value);
      } else {
        params.delete(key);
      }
    });
    params.set('page', '1');
    router.push(`?${params.toString()}`);
  };

  const handleResetFilters = () => {
    router.push(`?page=1`);
  };

  return (
    <SectionWrapper>
      <HeadingBar
        title='Exam Categories'
        elements={[
          <LimitFilter
            key='limit'
            value={(filters.limit as string) || '10'}
            onChange={(value: string) => {
              handleApplyFilters({ limit: value });
            }}
          />,
          <MasterFilter
            key='filter'
            filterOptions={filterOptions}
            onApplyFilters={handleApplyFilters}
            onResetFilters={handleResetFilters}
            initialFilters={filters}
          />,
        ]}
        addAction={addExamCategoryModal.open}
      />
      <MasterTable headers={headers} rows={rows} isLoading={isLoading} pagination={pagination} />

      {/* Add Exam Category Modal */}
      <PrimaryFormModal
        isOpen={addExamCategoryModal.isOpen}
        onClose={addExamCategoryModal.close}
        title='Add New Exam Category'
        subtitle='Create a new exam category for your platform'
        size='sm'
        showSparkles={true}
      >
        <ExamCategoryForm onSuccess={addExamCategoryModal.close} />
      </PrimaryFormModal>

      {/* Edit Exam Category Modal */}
      <PrimaryFormModal
        isOpen={editExamCategoryModal.isOpen}
        onClose={() => {
          editExamCategoryModal.close();
          setSelectedExamCategoryId(null);
        }}
        title='Edit Exam Category'
        subtitle='Update existing exam category details'
        size='sm'
        isLoading={isLoadingSelectedExamCategory}
        loadingTitle='Loading Exam Category Data'
        loadingMessage='Please wait while we fetch the exam category details...'
        isError={!!selectedExamCategoryError}
        errorMessage={selectedExamCategoryError?.message}
      >
        {selectedExamCategoryData?.data?.examCategory && (
          <ExamCategoryForm data={selectedExamCategoryData.data.examCategory} onSuccess={editExamCategoryModal.close} />
        )}
      </PrimaryFormModal>
    </SectionWrapper>
  );
};

export default ExamCategoryTable;
