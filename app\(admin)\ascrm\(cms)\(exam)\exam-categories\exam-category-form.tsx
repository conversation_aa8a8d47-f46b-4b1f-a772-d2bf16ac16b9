'use client';

import { Form } from '@/components/ui/form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import { toast } from 'react-toastify';
import { PrimaryInput, PrimarySwitchInput, SubmitButton } from '@/components/forms';
import { IAddUpdateFormProps } from '@/types/api';
import { useCreateExamCategory, useUpdateExamCategory } from '@/hooks/education/exam.hooks';
import { IExamCategoryDocument } from '@/server/services/education/exam.service';
import { createExamCategorySchema } from '@/validation/schemas/education/exam.schema';

interface IExamCategoryFormProps extends IAddUpdateFormProps<IExamCategoryDocument> {
  onSuccess: () => void;
}

const ExamCategoryForm: React.FC<IExamCategoryFormProps> = ({ data, onSuccess }) => {
  const { mutateAsync: createExamCategory, isPending: isCreating } = useCreateExamCategory();
  const { mutateAsync: updateExamCategory, isPending: isUpdating } = useUpdateExamCategory();

  const examCategoryId = data?._id;
  const isSubmitting = isCreating || isUpdating;

  const form = useForm<z.infer<typeof createExamCategorySchema>>({
    resolver: zodResolver(createExamCategorySchema),
    defaultValues: {
      name: data?.name || '',
      isActive: data?.isActive !== undefined ? data.isActive : true,
    },
  });

  const onSubmit = async (values: z.infer<typeof createExamCategorySchema>) => {
    try {
      const result = examCategoryId 
        ? await updateExamCategory({ id: examCategoryId, data: values }) 
        : await createExamCategory(values);
        
      if (!result.success) throw new Error(result.message || 'Operation failed');

      toast.success(result.message);
      onSuccess();
    } catch (error: any) {
      toast.error(error.message || 'An error occurred');
    }
  };

  return (
    <section className='relative'>
      <div className='bg-white rounded-lg shadow-sm p-6 max-w-2xl'>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className='grid grid-cols-1 gap-6 items-start justify-start'>
            {/* Name field */}
            <PrimaryInput form={form} name='name' label='Exam Category Name' placeholder='Enter exam category name' required />

            {/* Is Active */}
            <PrimarySwitchInput
              form={form}
              name='isActive'
              label='Is this exam category active?'
              description='Making it inactive will hide it from the website.'
            />

            <SubmitButton
              submittingLabel={examCategoryId ? 'Updating...' : 'Creating...'}
              label={examCategoryId ? 'Update Exam Category' : 'Create Exam Category'}
              isSubmitting={isSubmitting}
              disabled={isSubmitting}
              className='w-fit'
            />
          </form>
        </Form>
      </div>
    </section>
  );
};

export default ExamCategoryForm;
