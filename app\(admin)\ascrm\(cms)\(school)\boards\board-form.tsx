'use client';

import { Form } from '@/components/ui/form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import { toast } from 'react-toastify';
import { PrimaryInput, PrimarySwitchInput, SubmitButton } from '@/components/forms';
import { IAddUpdateFormProps } from '@/types/api';
import { useCreateBoard, useUpdateBoard } from '@/hooks/education/school.hooks';
import { IBoardDocument } from '@/server/services/education/school.service';
import { createBoardSchema } from '@/validation/schemas/education/school.schema';

interface IBoardFormProps extends IAddUpdateFormProps<IBoardDocument> {
  onSuccess: () => void;
}

const BoardForm: React.FC<IBoardFormProps> = ({ data, onSuccess }) => {
  const { mutateAsync: createBoard, isPending: isCreating } = useCreateBoard();
  const { mutateAsync: updateBoard, isPending: isUpdating } = useUpdateBoard();

  const boardId = data?._id;
  const isSubmitting = isCreating || isUpdating;

  const form = useForm<z.infer<typeof createBoardSchema>>({
    resolver: zodResolver(createBoardSchema),
    defaultValues: {
      name: data?.name || '',
      isActive: data?.isActive !== undefined ? data.isActive : true,
    },
  });

  const onSubmit = async (values: z.infer<typeof createBoardSchema>) => {
    try {
      const result = boardId ? await updateBoard({ id: boardId, data: values }) : await createBoard(values);
      if (!result.success) throw new Error(result.message || 'Operation failed');

      toast.success(result.message);
      onSuccess();
    } catch (error: any) {
      toast.error(error.message || 'An error occurred');
    }
  };

  return (
    <section className='relative'>
      <div className='bg-white rounded-lg shadow-sm p-6 max-w-2xl'>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className='grid grid-cols-1 gap-6 items-start justify-start'>
            {/* Name field */}
            <PrimaryInput form={form} name='name' label='Name' placeholder='Select board name' required />

            {/* Is Active */}
            <PrimarySwitchInput
              form={form}
              name='isActive'
              label='Is this board active?'
              description='Making it inactive will hide it from the website.'
            />

            <SubmitButton
              submittingLabel={boardId ? 'Updating...' : 'Creating...'}
              label={boardId ? 'Update Board' : 'Create Board'}
              isSubmitting={isSubmitting}
              disabled={isSubmitting}
              className='w-fit'
            />
          </form>
        </Form>
      </div>
    </section>
  );
};

export default BoardForm;
