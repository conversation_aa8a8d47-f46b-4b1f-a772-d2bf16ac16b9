'use client';

import { PrimaryInput, SubmitButton } from '@/components/forms';
import { Form } from '@/components/ui/form';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { UpdateUserPasswordInput, updateUserPasswordSchema } from '@/validation/schemas/user.schema';
import { useUpdatePassword } from '@/hooks/users/user.hooks';
import { toast } from 'react-toastify';

const PasswordForm = () => {
  const { mutateAsync: updatePassword, isPending } = useUpdatePassword();

  const form = useForm<UpdateUserPasswordInput>({
    resolver: zodResolver(updateUserPasswordSchema),
    defaultValues: {
      oldPassword: '',
      newPassword: '',
      confirmPassword: '',
    },
  });

  async function onSubmit(values: UpdateUserPasswordInput) {
    try {
      const response = await updatePassword(values);
      if (!response.success) throw new Error(response.message);

      form.reset();
      toast.success('Password updated successfully!');
    } catch (error: any) {
      toast.error(error.message || 'Failed to update password');
    }
  }

  return (
    <>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className='mt-4 space-y-4'>
          <PrimaryInput form={form} name='oldPassword' placeholder='Enter current password' type='password' required />
          <PrimaryInput form={form} name='newPassword' placeholder='Enter new password' type='password' required />
          <PrimaryInput form={form} name='confirmPassword' placeholder='Confirm new password' type='password' required />

          <SubmitButton
            isSubmitting={form.formState.isSubmitting || isPending}
            label='Modify Password'
            submittingLabel='Updating...'
            className='w-full justify-center !rounded-3xl mt-4'
          />
        </form>
      </Form>
    </>
  );
};

export default PasswordForm;
