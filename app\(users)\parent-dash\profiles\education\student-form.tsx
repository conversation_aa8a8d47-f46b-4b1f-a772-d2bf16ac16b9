'use client';

import { useEffect } from 'react';
import { UserCircle2 } from 'lucide-react';
import { PrimaryInput, PrimarySelect, PrimaryDateInput, ImageAndPdfUploader, CancelButton, SubmitButton } from '@/components/forms';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { toast } from 'react-toastify';
import { Form } from '@/components/ui/form';
import { useCreateChildProfile, useUpdateChildProfile } from '@/hooks/profile/profile.hooks';
import { IChildProfileDocument } from '@/server/services/profile.service';
import { createChildProfileSchema, CreateChildProfileInput } from '@/validation/schemas/parent/child-profile.schema';
import { genderOptions } from '@/validation/schemas/maps';
import { PrimaryModalWithHeader } from '@/components/dashboard/shared/misc';

interface StudentFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: () => void;
  initialValues?: IChildProfileDocument;
}

const StudentForm = ({ isOpen, onClose, onSuccess, initialValues }: StudentFormProps) => {
  const studentId = initialValues?._id;
  const isEditing = !!studentId;

  const createChildProfile = useCreateChildProfile();
  const updateChildProfile = useUpdateChildProfile();

  const form = useForm<CreateChildProfileInput>({
    resolver: zodResolver(createChildProfileSchema),
    defaultValues: initialValues || {
      fullName: '',
      gender: undefined,
      dateOfBirth: new Date(),
      avatar: undefined,
    },
  });

  useEffect(() => {
    if (initialValues) {
      form.reset(initialValues);
    }
  }, [form, initialValues]);

  const onSubmit = async (values: CreateChildProfileInput) => {
    try {
      const formData = new FormData();
      formData.append('fullName', values.fullName);
      formData.append('gender', values.gender.toLowerCase());
      formData.append('dateOfBirth', values?.dateOfBirth?.toISOString() || '');

      if (values.avatar && typeof values.avatar === 'object') {
        formData.append('avatar', values.avatar);
      }

      let response;

      if (isEditing && studentId) {
        response = await updateChildProfile.mutateAsync({ id: studentId, data: formData });
      } else {
        response = await createChildProfile.mutateAsync(formData);
      }

      if (!response.success) throw new Error(response.message || 'Operation failed');
      toast.success(`Student ${isEditing ? 'updated' : 'added'} successfully!`);
      onSuccess?.();
      onClose();
      form.reset();
    } catch (error) {
      toast.error(`Failed to ${isEditing ? 'update' : 'add'} student details`);
      console.error(error);
    }
  };

  return (
    <PrimaryModalWithHeader
      isOpen={isOpen}
      onClose={onClose}
      title={`${isEditing ? 'Edit' : 'Add'} Student`}
      subtitle={isEditing ? 'Update student profile details' : 'Add a new student profile'}
      icon={<UserCircle2 className='text-white' size={22} />}
      variant='primary'
      maxWidth='max-w-3xl'
    >
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className='p-8'>
          <div className='flex flex-col md:flex-row gap-8'>
            {/* Left Column - Profile Picture Upload */}
            <div className='md:w-1/3'>
              <div className='sticky top-0'>
                <h3 className='text-lg font-semibold text-gray-700 mb-4'>Profile Picture</h3>
                <div className='mt-4'>
                  <ImageAndPdfUploader
                    form={form}
                    name='avatar'
                    label='Profile Picture'
                    description='Upload a profile photo of the student'
                    variant='primary'
                    accept={{ 'image/*': ['.jpg', '.jpeg', '.png'] }}
                  />
                </div>
              </div>
            </div>

            {/* Right Column - Form Fields */}
            <div className='md:w-2/3'>
              <h3 className='text-lg font-semibold text-gray-700 mb-6'>Student Details</h3>
              <div className='grid grid-cols-1 gap-6'>
                <PrimaryInput form={form} name='fullName' label='Full Name' placeholder='e.g. Rahul Sharma' required />
                <PrimarySelect form={form} name='gender' label='Gender' options={genderOptions} required />
                <PrimaryDateInput form={form} name='dateOfBirth' label='Date of Birth' required />

                {/* Footer with actions */}
                <div className='mt-8 flex justify-end gap-3 border-t border-gray-100 pt-5'>
                  <CancelButton onClose={onClose} />
                  <SubmitButton
                    isSubmitting={form.formState.isSubmitting}
                    label={`${isEditing ? 'Update' : 'Save'} Student`}
                    submittingLabel='Saving...'
                    variant='primary'
                  />
                </div>
              </div>
            </div>
          </div>
        </form>
      </Form>
    </PrimaryModalWithHeader>
  );
};

export default StudentForm;
