'use client';

import { FormField, FormItem, FormControl, Form, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Switch } from '@/components/ui/switch';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { FileUp, Loader2 } from 'lucide-react';
import * as z from 'zod';
import { useState } from 'react';
import { tutorManualAadharKycSchema } from '@/lib/validations/tutor-dash/zod';
import RedirectAfterKyc from '../misc/RedirectAfterKyc';

const TutorManualAadharKycForm = () => {
  const form = useForm<z.infer<typeof tutorManualAadharKycSchema>>({
    resolver: zodResolver(tutorManualAadharKycSchema),
    defaultValues: {
      aadharNumber: '',
      name: '',
      frontAadhar: '',
      backAadhar: '',
    },
  });

  const [frontAadharFile, setFrontAadharFile] = useState<File | null>(null);
  const [backAadharFile, setBackAadharFile] = useState<File | null>(null);
  const [isConfirmed, setIsConfirmed] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);

  const handleFrontAadharChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setFrontAadharFile(e.target.files[0]);
    }
  };

  const handleBackAadharChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setBackAadharFile(e.target.files[0]);
    }
  };

  async function onSubmit(values: z.infer<typeof tutorManualAadharKycSchema>) {
    console.log(values);
    setIsSuccess(true);
  }

  return (
    <div>
      {isSuccess ? (
        <RedirectAfterKyc redirectUrl='/tutor-dash/profiles/kyc/manual/' />
      ) : (
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className='flex flex-col gap-6'>
            <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
              <FormField
                control={form.control}
                name='aadharNumber'
                render={({ field }) => (
                  <FormItem className='w-full'>
                    <FormLabel className='primary-label'>Aadhar Number</FormLabel>
                    <FormControl>
                      <Input className='primary-input' type='number' placeholder='Enter your Aadhar number' {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name='name'
                render={({ field }) => (
                  <FormItem className='w-full'>
                    <FormLabel className='primary-label'>Name</FormLabel>
                    <FormControl>
                      <Input className='primary-input' type='text' placeholder='Enter your name' {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
              {/* Front Aadhar Upload */}
              <FormItem className='w-full'>
                <FormLabel className='primary-label'>Front Aadhar Image</FormLabel>
                <div className='border-2 border-dashed border-gray-300 rounded-md p-4 text-center h-72 flex flex-col justify-center items-center'>
                  {frontAadharFile ? (
                    <div className='max-w-full'>
                      <p className='text-primaryColor'>{frontAadharFile.name}</p>
                      <p className='text-gray-500 text-sm'>{(frontAadharFile.size / 1024).toFixed(2)} KB</p>
                      <button type='button' onClick={() => setFrontAadharFile(null)} className='text-red-500 mt-2'>
                        Delete
                      </button>
                    </div>
                  ) : (
                    <label className='cursor-pointer'>
                      <input type='file' accept='image/*,application/pdf' className='hidden' onChange={handleFrontAadharChange} />
                      <div className='flex flex-col items-center'>
                        <FileUp size={100} strokeWidth={1} className='text-primaryColor' />
                        <span className='mt-2 text-sm text-gray-500'>Upload Front Side of Document</span>
                      </div>
                    </label>
                  )}
                </div>
                <FormMessage />
              </FormItem>

              {/* Back Aadhar Upload */}
              <FormItem className='w-full'>
                <FormLabel className='primary-label'>Back Aadhar Image</FormLabel>
                <div className='border-2 border-dashed border-gray-300 rounded-md p-4 text-center h-72 flex flex-col justify-center items-center'>
                  {backAadharFile ? (
                    <div className='max-w-full'>
                      <p className='text-primaryColor'>{backAadharFile.name}</p>
                      <p className='text-gray-500 text-sm'>{(backAadharFile.size / 1024).toFixed(2)} KB</p>
                      <button type='button' onClick={() => setBackAadharFile(null)} className='text-red-500 mt-2'>
                        Delete
                      </button>
                    </div>
                  ) : (
                    <label className='cursor-pointer'>
                      <input type='file' accept='image/*,application/pdf' className='hidden' onChange={handleBackAadharChange} />
                      <div className='flex flex-col items-center'>
                        <FileUp size={100} strokeWidth={1} className='text-primaryColor' />
                        <span className='mt-2 text-sm text-gray-500'>Upload Back Side of Document</span>
                      </div>
                    </label>
                  )}
                </div>
                <FormMessage />
              </FormItem>
              <div className='md:col-span-2'>
                <p className='text-slate-900 text-lg font-semibold mb-1'>Supported documents:</p>
                <ol className='list-decimal text-sm text-red-500 pl-4'>
                  <li>Accepted formats include both image files (such as JPEG, PNG, etc) and PDF files.</li>
                  <li>Please ensure that the documents you upload are of high resolution to avoid any processing delays.</li>
                  <li>The maximum allowed file size for each document is 2MB. Files larger than this will not be accepted.</li>
                </ol>
              </div>
            </div>

            {/* Confirmation */}
            <FormItem className='flex items-end gap-2'>
              <FormControl>
                <Switch checked={isConfirmed} onCheckedChange={setIsConfirmed} />
              </FormControl>
              <FormLabel>I confirm that I uploaded a valid government photo ID</FormLabel>
            </FormItem>

            <button type='submit' disabled={form.formState.isSubmitting} className='btn-default-md w-fit'>
              {form.formState.isSubmitting ? (
                <>
                  <Loader2 size={20} className='animate-spin mr-3' />
                  <span>Saving...</span>
                </>
              ) : (
                <span>Submit</span>
              )}
            </button>
          </form>
        </Form>
      )}
    </div>
  );
};

export default TutorManualAadharKycForm;
