'use client';

import { FormField, FormItem, FormControl, Form, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Switch } from '@/components/ui/switch';
import { tutorQualificationSchema } from '@/lib/validations/tutor-dash/zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { Loader2 } from 'lucide-react';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import * as z from 'zod';

const TutorQualificationForm = ({ isUndergraduate = false }: { isUndergraduate?: boolean }) => {
  const form = useForm<z.infer<typeof tutorQualificationSchema>>({
    resolver: zodResolver(tutorQualificationSchema),
    defaultValues: {
      degree: '',
      branch: '',
      classOfStudy: '',
      startYear: new Date().getFullYear(),
      endYearOrExpectedYear: new Date().getFullYear(),
      percentage: '',
      collegeOrUniversityName: '',
      location: '',
    },
  });

  const [isPursuing, setIsPursuing] = useState(false);

  async function onSubmit(values: z.infer<typeof tutorQualificationSchema>) {
    console.log(values);
  }

  return (
    <div>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className='grid grid-cols-1 md:grid-cols-2 items-start gap-6'>
          {/* Status of Study */}
          <FormField
            control={form.control}
            name='isPursuing'
            render={({ field }) => (
              <FormItem className='w-full flex gap-2 flex-col'>
                <FormLabel className='primary-label'>Are you still pursuing?</FormLabel>
                <FormControl>
                  <Switch
                    checked={field.value}
                    onCheckedChange={(value) => {
                      setIsPursuing((prev) => !prev);
                      return field.onChange(value);
                    }}
                  />
                </FormControl>
              </FormItem>
            )}
          />

          {!isUndergraduate ? (
            <>
              {/* Degree */}
              <FormField
                control={form.control}
                name='degree'
                render={({ field }) => (
                  <FormItem className='w-full'>
                    <FormLabel className='primary-label'>
                      <span>Degree</span>
                      <span className='text-primaryColor'>*</span>
                    </FormLabel>
                    <FormControl>
                      <Input className='primary-input' type='text' placeholder='Enter your degree' {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </>
          ) : (
            <>
              {/* Class of Study */}
              <FormField
                control={form.control}
                name='classOfStudy'
                render={({ field }) => (
                  <FormItem className='w-full'>
                    <FormLabel className='primary-label'>
                      <span>Class of Study</span>
                    </FormLabel>
                    <FormControl>
                      <Input className='primary-input' type='text' placeholder='Enter your class of study' {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </>
          )}

          {/* Branch */}
          <FormField
            control={form.control}
            name='branch'
            render={({ field }) => (
              <FormItem className='w-full'>
                <FormLabel className='primary-label'>
                  <span>Branch</span>
                  <span className='text-primaryColor'>*</span>
                </FormLabel>
                <FormControl>
                  <Input className='primary-input' type='text' placeholder='Enter your branch' {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Start Year */}
          <FormField
            control={form.control}
            name='startYear'
            render={({ field }) => (
              <FormItem className='w-full'>
                <FormLabel className='primary-label'>
                  <span>Start Year</span>
                  <span className='text-primaryColor'>*</span>
                </FormLabel>
                <FormControl>
                  <Input className='primary-input' type='number' placeholder='Enter your start year' {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* End Year or Expected Year */}
          <FormField
            control={form.control}
            name='endYearOrExpectedYear'
            render={({ field }) => (
              <FormItem className='w-full'>
                <FormLabel className='primary-label'>
                  <span>End Year or Expected Year</span>
                  <span className='text-primaryColor'>*</span>
                </FormLabel>
                <FormControl>
                  <Input className='primary-input' type='number' placeholder='Enter your end or expected year' {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {!isPursuing && (
            <>
              {/* Percentage */}
              <FormField
                control={form.control}
                name='percentage'
                render={({ field }) => (
                  <FormItem className='w-full'>
                    <FormLabel className='primary-label'>
                      <span>Percentage</span>
                      <span className='text-primaryColor'>*</span>
                    </FormLabel>
                    <FormControl>
                      <Input className='primary-input' type='text' placeholder='Enter your percentage' {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </>
          )}

          {/* College or University Name */}
          <FormField
            control={form.control}
            name='collegeOrUniversityName'
            render={({ field }) => (
              <FormItem className='w-full'>
                <FormLabel className='primary-label'>
                  <span>College/University Name</span>
                  <span className='text-primaryColor'>*</span>
                </FormLabel>
                <FormControl>
                  <Input className='primary-input' type='text' placeholder='Enter your college or university name' {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Location */}
          <FormField
            control={form.control}
            name='location'
            render={({ field }) => (
              <FormItem className='w-full'>
                <FormLabel className='primary-label'>
                  <span>Location</span>
                  <span className='text-primaryColor'>*</span>
                </FormLabel>
                <FormControl>
                  <Input className='primary-input' type='text' placeholder='Enter the location' {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Location */}
          <FormField
            control={form.control}
            name='location'
            render={({ field }) => (
              <FormItem className='w-full'>
                <FormLabel className='primary-label'>
                  <span>Attachments</span>
                  <span className='text-primaryColor'>*</span>
                </FormLabel>
                <FormControl>
                  <Input className='primary-input' type='text' placeholder='Image will be uploaded here (Coming Soon)' {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <button type='submit' disabled={form.formState.isSubmitting} className='btn-default-md md:col-span-2 w-fit'>
            {form.formState.isSubmitting ? (
              <>
                <Loader2 size={20} className='animate-spin mr-3' />
                <span>Saving...</span>
              </>
            ) : (
              <span>Submit</span>
            )}
          </button>
        </form>
      </Form>
    </div>
  );
};

export default TutorQualificationForm;
