import { createOptionsKeyMap } from '@/validation/utils/form.utils';

// Service Categories
export const serviceCategoryMap = {
  schools: { key: 'schools', label: 'Schools' },
  colleges: { key: 'colleges', label: 'Colleges' },
  languages: { key: 'languages', label: 'Languages' },
  hobbies: { key: 'hobbies', label: 'Hobbies' },
  it_courses: { key: 'it_courses', label: 'IT Courses' },
  exams: { key: 'exams', label: 'Exams' },
} as const;

export const serviceCategoryOptions = createOptionsKeyMap(serviceCategoryMap);
export type IServiceCategoryMap = keyof typeof serviceCategoryMap;

// School Class (Roman Numerals)
export const schoolClassRomanValues = [
  'Nursery KG',
  'Class I',
  'Class II',
  'Class III',
  'Class IV',
  'Class V',
  'Class VI',
  'Class VII',
  'Class VIII',
  'Class IX',
  'Class X',
  'Class XI',
  'Class XII',
] as const;

export type ISchoolClassRomanValues = (typeof schoolClassRomanValues)[number];
