'use client';

import { cn } from '@/lib/utils';
import { useSidebarStore } from '@/store/sidebarStore';

export default function LayoutWrapper({ children }: { children: React.ReactNode }) {
  const { isSidebarOpen } = useSidebarStore();

  // TODO: receive user here n set in store.

  return (
    <div className={cn('min-h-screen', 'transition-[margin] duration-300 ease-in-out', isSidebarOpen ? 'ml-[286px]' : 'ml-[88px]')}>{children}</div>
  );
}
