import React from 'react';
import { FormField, FormItem, FormControl, FormLabel, FormMessage } from '@/components/ui/form';
import { InputOTP, InputOTPGroup, InputOTPSlot } from '@/components/ui/input-otp';
import type { UseFormReturn, FieldValues, Path } from 'react-hook-form';

interface PrimaryOTPInputProps<TFieldValues extends FieldValues> {
  form: UseFormReturn<TFieldValues>;
  name: Path<TFieldValues>;
  label: string;
  required?: boolean;
  length?: number;
}

export const PrimaryOTPInput = <TFieldValues extends FieldValues>({
  form,
  name,
  label,
  required = false,
  length = 4,
}: PrimaryOTPInputProps<TFieldValues>) => {
  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => (
        <FormItem className='w-full'>
          <FormLabel className='primary-label'>
            <span>{label}</span>
            {required && <span className='text-primaryColor'>*</span>}
          </FormLabel>
          <FormControl>
            <InputOTP maxLength={length} {...field}>
              <InputOTPGroup className='flex justify-center gap-2'>
                {Array.from({ length }).map((_, index) => (
                  <InputOTPSlot
                    key={index}
                    index={index}
                    className='size-12 text-center text-2xl border-2 border-gray-300 rounded-md primary-input'
                  />
                ))}
              </InputOTPGroup>
            </InputOTP>
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
};

export default PrimaryOTPInput;
