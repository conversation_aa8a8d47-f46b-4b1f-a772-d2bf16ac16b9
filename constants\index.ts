import { IUserTypeMap } from '@/validation/schemas/maps';

export const gendersMap = {
  male: { label: '👨 Male', key: 'male' },
  female: { label: '👧 Female', key: 'female' },
};

export const tutorExperienceMap = {
  private: { label: 'Private Tuition', key: 'private' },
  school: { label: 'School', key: 'school' },
  college: { label: 'College', key: 'college' },
  institute: { label: 'Institute', key: 'institute' },
  other: { label: 'Other', key: 'other' },
};

export const studySectionMap = {
  school: { label: 'School', key: 'school' },
  college: { label: 'College', key: 'college' },
  language: { label: 'Language', key: 'language' },
  hobby: { label: 'Hobby', key: 'hobby' },
  itCourse: { label: 'IT Course', key: 'itCourse' },
  competitiveExam: { label: 'Competitive Exam', key: 'competitiveExam' },
  entranceExam: { label: 'Entrance Exam', key: 'entranceExam' },
};

export const studySchoolBoardMap = {
  cbse: { label: 'CBSE', key: 'cbse' },
  icse: { label: 'ICSE', key: 'icse' },
  stateBoard: { label: 'State Board', key: 'stateBoard' },
  ib: { label: 'International Baccalaureate (IB)', key: 'ib' },
  igcse: { label: 'IGCSE', key: 'igcse' },
  nios: { label: 'NIOS', key: 'nios' },
  davBoard: { label: 'DAV Board', key: 'davBoard' },
};

export const studyCollegeBoardMap = {
  artAndHumaities: { label: 'Arts & Humanities', key: 'artsAndHumanities' },
  commerce: { label: 'Commerce', key: 'commerce' },
  science: { label: 'Science', key: 'science' },
  management: { label: 'Management', key: 'management' },
  engineering: { label: 'Engineering', key: 'engineering' },
  medical: { label: 'Medical', key: 'medical' },
  law: { label: 'Law', key: 'law' },
  agriculture: { label: 'Agriculture', key: 'agriculture' },
  humanities: { label: 'Humanities', key: 'humanities' },
  education: { label: 'Education', key: 'education' },
  massCommunication: { label: 'Mass Communication', key: 'massCommunication' },
  paramedical: { label: 'Paramedical', key: 'paramedical' },
  architecture: { label: 'Architecture', key: 'architecture' },
  hotelManagement: { label: 'Hotel Management', key: 'hotelManagement' },
  computerApplication: { label: 'Computer Application', key: 'computerApplication' },
  pharmacy: { label: 'Pharmacy', key: 'pharmacy' },
  veterinarySciences: { label: 'Veterinary Sciences', key: 'veterinarySciences' },
  design: { label: 'Design', key: 'design' },
  electronicsAndCommunication: { label: 'Electronics & Communication', key: 'electronicsAndCommunication' },
};

export const studySchoolClassMap = {
  ukg: { label: 'Nursery - UKG', key: 'ukg' },
  class1: { label: 'Class 1', key: 'class1' },
  class2: { label: 'Class 2', key: 'class2' },
  class3: { label: 'Class 3', key: 'class3' },
  class4: { label: 'Class 4', key: 'class4' },
  class5: { label: 'Class 5', key: 'class5' },
  class6: { label: 'Class 6', key: 'class6' },
  class7: { label: 'Class 7', key: 'class7' },
  class8: { label: 'Class 8', key: 'class8' },
  class9: { label: 'Class 9', key: 'class9' },
  class10: { label: 'Class 10', key: 'class10' },
  class11: { label: 'Class 11', key: 'class11' },
  class12: { label: 'Class 12', key: 'class12' },
  other: { label: 'Other', key: 'other' },
};

export const studyCollegeStreamsMap = {
  arts: { label: 'Arts', key: 'arts' },
  humanities: { label: 'Humanities', key: 'humanities' },
  socialStudies: { label: 'Social Studies', key: 'socialStudies' },
  science: { label: 'Science', key: 'science' },
  technology: { label: 'Technology', key: 'technology' },
  other: { label: 'Other', key: 'other' },
};

export const studyCollegeClassMap = {
  graduation: { label: 'Graduation', key: 'graduation' },
  postGraduation: { label: 'Post Graduation', key: 'postGraduation' },
  diploma: { label: 'Diploma', key: 'diploma' },
  phd: { label: 'PhD', key: 'phd' },
};

export const studyCollegeProgramMap = {
  bachelorOfArts: { label: 'Bachelor of Arts', key: 'bachelorOfArts' },
  bachelorOfCommerce: { label: 'Bachelor of Commerce', key: 'bachelorOfCommerce' },
  bachelorOfScience: { label: 'Bachelor of Science', key: 'bachelorOfScience' },
  bachelorOfTechnology: { label: 'Bachelor of Technology', key: 'bachelorOfTechnology' },
  masterOfArts: { label: 'Master of Arts', key: 'masterOfArts' },
  masterOfCommerce: { label: 'Master of Commerce', key: 'masterOfCommerce' },
  masterOfScience: { label: 'Master of Science', key: 'masterOfScience' },
  masterOfTechnology: { label: 'Master of Technology', key: 'masterOfTechnology' },
};

export const studyCollegeProgramBranchesMap = {
  arts: { label: 'Arts', key: 'arts' },
  humanities: { label: 'Humanities', key: 'humanities' },
  socialStudies: { label: 'Social Studies', key: 'socialStudies' },
  science: { label: 'Science', key: 'science' },
  technology: { label: 'Technology', key: 'technology' },
  other: { label: 'Other', key: 'other' },
};

export const studySubjectMap = {
  maths: { label: 'Maths', key: 'maths' },
  science: { label: 'Science', key: 'science' },
  socialStudies: { label: 'Social Studies', key: 'socialStudies' },
  english: { label: 'English', key: 'english' },
  hindi: { label: 'Hindi', key: 'hindi' },
  urdu: { label: 'Urdu', key: 'urdu' },
  other: { label: 'Other', key: 'other' },
  abc: { label: 'ABC', key: 'abc' },
  def: { label: 'DEF', key: 'def' },
  ghi: { label: 'GHI', key: 'ghi' },
  jkl: { label: 'JKL', key: 'jkl' },
  mno: { label: 'MNO', key: 'mno' },
  pqr: { label: 'PQR', key: 'pqr' },
  stu: { label: 'STU', key: 'stu' },
  vwx: { label: 'VWX', key: 'vwx' },
  yz: { label: 'YZ', key: 'yz' },
  geography: { label: 'Geography', key: 'geography' },
  history: { label: 'History', key: 'history' },
  politics: { label: 'Politics', key: 'politics' },
  economics: { label: 'Economics', key: 'economics' },
  biology: { label: 'Biology', key: 'biology' },
  chemistry: { label: 'Chemistry', key: 'chemistry' },
  physics: { label: 'Physics', key: 'physics' },
  mathematics: { label: 'Mathematics', key: 'mathematics' },
};

export const studyAmountTypeMap = {
  hourly: { label: 'Hourly', key: 'hourly' },
  monthly: { label: 'Monthly', key: 'monthly' },
};

export const jobTypesMap = {
  fullTime: { key: 'full-time', label: 'Full Time' },
  partTime: { key: 'part-time', label: 'Part Time' },
  any: { key: 'any', label: 'Any' },
};

export const preferredJobModesMap = {
  online: { key: 'online', label: 'Online' },
  offline: { key: 'offline', label: 'Offline' },
  any: { key: 'any', label: 'Any' },
};

export const languagesMap = {
  english: { key: 'english', label: 'English' },
  hindi: { key: 'hindi', label: 'Hindi' },
};

export const languageLevelsMap = {
  basic: { key: 'basic', label: 'Basic' },
  intermediate: { key: 'intermediate', label: 'Intermediate' },
  advanced: { key: 'advanced', label: 'Advanced' },
  fluent: { key: 'fluent', label: 'Fluent' },
};

export const keySkillsMap = {
  communication: { key: 'communication', label: 'Communication' },
  problemSolving: { key: 'problem-solving', label: 'Problem Solving' },
  criticalThinking: { key: 'critical-thinking', label: 'Critical Thinking' },
  timeManagement: { key: 'time-management', label: 'Time Management' },
  teamwork: { key: 'teamwork', label: 'Teamwork' },
  adaptability: { key: 'adaptability', label: 'Adaptability' },
  creativity: { key: 'creativity', label: 'Creativity' },
  leadership: { key: 'leadership', label: 'Leadership' },
  technicalSkills: { key: 'technical-skills', label: 'Technical Skills' },
};

export const subjectsSpecializationMap = {
  mathematics: { key: 'mathematics', label: 'Mathematics' },
  science: { key: 'science', label: 'Science' },
  english: { key: 'english', label: 'English' },
  history: { key: 'history', label: 'History' },
  geography: { key: 'geography', label: 'Geography' },
  physics: { key: 'physics', label: 'Physics' },
  chemistry: { key: 'chemistry', label: 'Chemistry' },
  biology: { key: 'biology', label: 'Biology' },
  computerScience: { key: 'computer-science', label: 'Computer Science' },
  economics: { key: 'economics', label: 'Economics' },
};

export const comaplaintStatusMap = {
  open: { key: 'active', label: 'Active' },
  progress: { key: 'progress', label: 'In Progress' },
  inactive: { key: 'inactive', label: 'Inactive' },
  closed: { key: 'closed', label: 'Closed' },
};

export const complaintCategoryMap = {
  batch: { key: 'batch', label: 'Batch' },
  wallet: { key: 'wallet', label: 'Wallet' },
  billing: { key: 'billing', label: 'Billing' },
  leads: { key: 'leads', label: 'Leads' },
  other: { key: 'other', label: 'Other' },
};

export const leads = [
  {
    id: 1,
    name: 'Rahul Verma',
    enquiryFor: 'Class 10',
    location: 'New Delhi',
    distance: '5 km',
    status: 'all',
    address: '123 Main St, New Delhi, DL 110001',
    state: 'Delhi',
    country: 'India',
    pincode: '110001',
    subjects: ['Math', 'Science', 'English'],
    board: 'CBSE',
  },
  {
    id: 5,
    name: 'Mehul Verma',
    enquiryFor: 'Class 10',
    location: 'New Delhi',
    distance: '5 km',
    status: 'all',
    address: '123 Main St, New Delhi, DL 110001',
    state: 'Delhi',
    country: 'India',
    pincode: '110001',
    subjects: ['Math', 'Science', 'English'],
    board: 'CBSE',
  },
  {
    id: 2,
    name: 'Pooja Singh',
    enquiryFor: 'B.Sc',
    location: 'Mumbai',
    distance: '15 km',
    status: 'approached',
    address: '456 Elm St, Mumbai, MH 400001',
    state: 'Maharashtra',
    country: 'India',
    pincode: '400001',
    subjects: ['Physics', 'Chemistry', 'Mathematics', 'Biology', 'Computer Science'],
    board: 'CBSE',
  },
  {
    id: 3,
    name: 'Amit Sharma',
    enquiryFor: 'Class 12',
    location: 'Bangalore',
    distance: '10 km',
    status: 'favorite',
    address: '789 Oak St, Bangalore, KA 560001',
    state: 'Karnataka',
    country: 'India',
    pincode: '560001',
    subjects: ['Physics', 'Chemistry', 'Mathematics', 'Biology', 'Computer Science'],
    board: 'CBSE',
  },
  {
    id: 4,
    name: 'Neha Patel',
    enquiryFor: 'M.Sc',
    location: 'Ahmedabad',
    distance: '20 km',
    status: 'contacted',
    address: '321 Pine St, Ahmedabad, GJ 380001',
    state: 'Gujarat',
    country: 'India',
    pincode: '380001',
    subjects: ['Physics', 'Chemistry', 'Mathematics', 'Biology', 'Computer Science'],
    board: 'CBSE',
  },
];

// NEW
export const REGEX_MAP = {
  ALPHANUMERIC: /^[a-zA-Z0-9]+$/,
};

export const filterTypeMaps = {
  select: {
    key: 'select',
    label: 'Select',
  },
  input: {
    key: 'input',
    label: 'Input',
  },
  dateRange: {
    key: 'dateRange',
    length: 'Date Range',
  },
  multiSelect: {
    key: 'multiSelect',
    label: 'Multi Select',
  },
};

export type FilterTypeKey = keyof typeof filterTypeMaps;

export const userDashboardsByRoleMap: { [key in IUserTypeMap]: { key: IUserTypeMap; label: string; dashboard: string } } = {
  student: { key: 'student', label: 'Student', dashboard: 'parent-dash' },
  tutor: { key: 'tutor', label: 'Tutor', dashboard: 'tutor-dash' },
  institute: { key: 'institute', label: 'Institute', dashboard: 'institute-dash' },
  school: { key: 'school', label: 'School', dashboard: 'school-dash' },
  college: { key: 'college', label: 'College', dashboard: 'college-dash' },
} as const;
