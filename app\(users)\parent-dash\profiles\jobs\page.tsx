import TeachingExperienceForm from '@/components/dashboard/tutor-dash/forms/TeachingExperienceForm';
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge, BadgeCheck, X } from 'lucide-react';
import { AlertDialog, AlertDialogCancel, AlertDialogContent, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { SimpleTable } from '@/components/dashboard/tutor-dash/misc';
import { studySectionMap } from '@/constants';
import WhatDoYouTeachForm from '@/components/dashboard/tutor-dash/forms/WhatDoYouTeachForm';
import StudySectionTable from '@/components/dashboard/tutor-dash/misc/StudySectionTable';
import TutorJobProfileBasicForm from '@/components/dashboard/tutor-dash/forms/TutorJobProfileBasicForm';
import TutorQualificationForm from '@/components/dashboard/tutor-dash/forms/TutorQualificationForm';

const TutorTutionJobProfilePage = () => {
  return (
    <div className='bg-white p-6 rounded-3xl relative min-h-[650px]'>
      <Tabs defaultValue='basic-info' className='flex gap-6'>
        <TabsList className='primary-tab-list'>
          <TabsTrigger value='basic-info' className='primary-tab-trigger'>
            <span>Basic Info</span>
            <BadgeCheck strokeWidth={1.5} className='ml-auto text-primaryColor' />
          </TabsTrigger>
          <TabsTrigger value='qualification' className='primary-tab-trigger'>
            <span>Qualification</span>
            <BadgeCheck strokeWidth={1.5} className='ml-auto text-primaryColor' />
          </TabsTrigger>
          <TabsTrigger value='work-experience' className='primary-tab-trigger'>
            <span>Work Experience</span>
            <BadgeCheck strokeWidth={1.5} className='ml-auto text-primaryColor' />
          </TabsTrigger>
          <TabsTrigger value='job-background' className='primary-tab-trigger'>
            <span>Job Background</span>
            <BadgeCheck strokeWidth={1.5} className='ml-auto text-primaryColor' />
          </TabsTrigger>
        </TabsList>
        <TabsContent value='basic-info' className='w-full'>
          <div>
            <TutorJobProfileBasicForm />
          </div>
        </TabsContent>
        <TabsContent value='qualification' className='w-full'>
          <div>
            <Tabs defaultValue='undergraduate' className='flex gap-12'>
              <TabsList className='flex flex-col items-start justify-start h-fit w-64 py-6 px-4 rounded-3xl shrink-0 absolute top-[270px] left-[24px]'>
                <TabsTrigger value='undergraduate' className='primary-tab-trigger'>
                  <span>Undergraduate</span>
                  <BadgeCheck strokeWidth={1.5} className='ml-auto text-primaryColor' />
                </TabsTrigger>
                <TabsTrigger value='graduate' className='primary-tab-trigger'>
                  <span>Graduate</span>
                  <BadgeCheck strokeWidth={1.5} className='ml-auto text-primaryColor' />
                </TabsTrigger>
                <TabsTrigger value='other' className='primary-tab-trigger'>
                  <span>Any Other</span>
                  <Badge strokeWidth={1.5} className='ml-auto' />
                </TabsTrigger>
              </TabsList>
              <TabsContent value='undergraduate' className='w-full'>
                <div>
                  {/* Record Modal */}
                  <div className='flex justify-end items-start mb-4'>
                    <AlertDialog>
                      <div className='flex justify-between items-center w-full'>
                        <h2 className='text-base font-semibold'>Under Graduate Qualification Information</h2>
                        <AlertDialogTrigger className='btn-default-sm'>Add Record</AlertDialogTrigger>
                      </div>
                      <AlertDialogContent className='!w-[700px] !max-w-none'>
                        <TutorQualificationForm isUndergraduate />
                        <AlertDialogCancel className='border-0 absolute rounded-none rounded-tr-md right-0 top-0 bg-primaryColor-50 hover:bg-primaryColor-100 flex-shrink-0 p-2 tracking-wider cursor-pointer group overflow-hidden'>
                          <p className='text-primaryColor'>
                            <X />
                          </p>
                        </AlertDialogCancel>
                      </AlertDialogContent>
                    </AlertDialog>
                  </div>
                  {/* Record Table */}
                  <StudySectionTable
                    headers={['SN', 'Class', 'Branch', 'Start Year', 'End Year/Expected Year', 'Percentage']}
                    rows={[
                      [1, '10th', 'Science', 2015, 2018, '85%'],
                      [2, '11th', 'Commerce', 2016, 2019, '88%'],
                      [3, '12th', 'Engineering', 2014, 2018, '90%'],
                      [4, '12th', 'Arts', 2013, 2017, '82%'],
                      [5, '11th', 'Education', 2012, 2016, '87%'],
                      [6, '10th', 'Science', 2015, 2018, '-'],
                    ]}
                  />
                </div>
              </TabsContent>
              <TabsContent value='graduate' className='w-full'>
                <div>
                  {/* Record Modal */}
                  <div className='flex justify-end items-start mb-4'>
                    <AlertDialog>
                      <div className='flex justify-between items-center w-full'>
                        <h2 className='text-base font-semibold'>Graduate Qualification Information</h2>
                        <AlertDialogTrigger className='btn-default-sm'>Add Record</AlertDialogTrigger>
                      </div>
                      <AlertDialogContent className='!w-[700px] !max-w-none'>
                        <TutorQualificationForm />
                        <AlertDialogCancel className='border-0 absolute rounded-none rounded-tr-md right-0 top-0 bg-primaryColor-50 hover:bg-primaryColor-100 flex-shrink-0 p-2 tracking-wider cursor-pointer group overflow-hidden'>
                          <p className='text-primaryColor'>
                            <X />
                          </p>
                        </AlertDialogCancel>
                      </AlertDialogContent>
                    </AlertDialog>
                  </div>
                  {/* Record Table */}
                  <StudySectionTable
                    headers={['SN', 'Degree', 'Branch', 'Start Year', 'End Year/Expected Year', 'Percentage']}
                    rows={[
                      [1, 'B.Sc', 'Science', 2015, 2018, '85%'],
                      [2, 'B.Com', 'Commerce', 2016, 2019, '88%'],
                      [3, 'B.Tech', 'Engineering', 2014, 2018, '90%'],
                      [4, 'B.A', 'Arts', 2013, 2017, '82%'],
                      [5, 'B.Ed', 'Education', 2012, 2016, '87%'],
                      [6, 'M.Sc', 'Science', 2015, 2017, '-'],
                    ]}
                  />
                </div>
              </TabsContent>
              <TabsContent value='other' className='w-full'>
                Content for any other qualifications.
              </TabsContent>
            </Tabs>
          </div>
        </TabsContent>
        <TabsContent value='work-experience' className='w-full'>
          <div>
            {/* Experience Modal */}
            <div className='flex justify-end items-start mb-4'>
              <AlertDialog>
                <div className='flex justify-between items-center w-full'>
                  <h2 className='text-base font-semibold'>Work Experience Information</h2>
                  <AlertDialogTrigger className='btn-default-sm'>Add Work Experience</AlertDialogTrigger>
                </div>

                <AlertDialogContent className='!w-[700px] !max-w-none'>
                  <TeachingExperienceForm isWork />
                  <AlertDialogCancel className='border-0 absolute rounded-none rounded-tr-md right-0 top-0 bg-primaryColor-50 hover:bg-primaryColor-100 flex-shrink-0 p-2 tracking-wider cursor-pointer group overflow-hidden'>
                    <p className='text-primaryColor'>
                      <X />
                    </p>
                  </AlertDialogCancel>
                </AlertDialogContent>
              </AlertDialog>
            </div>
            {/* Experience Table */}
            <SimpleTable
              headers={['SN', 'Work Type', 'Exp. (M)', 'Place Name', 'Location']}
              rows={[
                [1, 'Private Tuition', 16, 'Self', 'Self'],
                [2, 'Institute Tuition', 24, 'Sherpur Public School', 'Sherpur, Bangladesh'],
                [3, 'College Tuition', 12, 'Mujibur Rahman Public School', 'Mujibur Rahman, Bangladesh'],
                [4, 'School Tuition', 6, 'Online Tutoring Platform', 'Online Tutoring Platform, Bangladesh'],
              ]}
            />
          </div>
        </TabsContent>
        <TabsContent value='job-background' className='w-full'>
          <Tabs defaultValue={studySectionMap.school.key} className='flex gap-12'>
            <TabsList className='flex flex-col items-start justify-start h-fit w-64 py-6 px-4 rounded-3xl shrink-0 absolute top-[270px] left-[24px]'>
              {Object.entries(studySectionMap).map(([key, { label, key: tabKey }]) => (
                <TabsTrigger key={tabKey} value={tabKey} className='primary-tab-trigger !justify-between'>
                  {label}{' '}
                  <span className='text-primaryColor border border-primaryColor size-5 text-sm flex items-center justify-center rounded-full shrink-0'>
                    {Math.ceil(Math.random() * 10)}
                  </span>
                </TabsTrigger>
              ))}
            </TabsList>
            {Object.entries(studySectionMap).map(([key, { key: tabKey }]) => (
              <TabsContent key={tabKey} value={tabKey} className='w-full'>
                <div>
                  {/* Record Modal */}
                  <div className='flex justify-end items-start mb-4'>
                    <AlertDialog>
                      <div className='flex justify-between items-center w-full'>
                        {/* @ts-ignore */}
                        <h2 className='text-base font-semibold'>{studySectionMap[key].label} Section Information</h2>
                        <AlertDialogTrigger className='btn-default-sm'>Add Record</AlertDialogTrigger>
                      </div>
                      <AlertDialogContent className='!w-[700px] !max-w-none'>
                        <WhatDoYouTeachForm />
                        <AlertDialogCancel className='border-0 absolute rounded-none rounded-tr-md right-0 top-0 bg-primaryColor-50 hover:bg-primaryColor-100 flex-shrink-0 p-2 tracking-wider cursor-pointer group overflow-hidden'>
                          <p className='text-primaryColor'>
                            <X />
                          </p>
                        </AlertDialogCancel>
                      </AlertDialogContent>
                    </AlertDialog>
                  </div>
                  {/* Record Table */}
                  <StudySectionTable
                    headers={['SN', 'Board', 'Class', 'Subjects', 'Budget']}
                    rows={[
                      [1, 'CBSE', '10th', 'All Subjects', '5000/hourly'],
                      [2, 'ICSE', '11th', 'Mathematics, Science, English, Mathematics, Science, English', '7000/hourly'],
                      [3, 'State Board', '12th', 'Mathematics, Science, English', '9000/hourly'],
                      [4, 'International Board', '12th', 'Mathematics, Science, English', '11000/hourly'],
                      [5, 'ICSE', '11th', 'Mathematics, Science, English', '7000/hourly'],
                    ]}
                  />
                </div>
              </TabsContent>
            ))}
          </Tabs>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default TutorTutionJobProfilePage;
